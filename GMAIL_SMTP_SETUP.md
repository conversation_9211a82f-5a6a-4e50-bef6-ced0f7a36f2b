# Gmail SMTP Setup Guide

## The Problem
Gmail requires **App Passwords** for SMTP authentication, not your regular Gmail password. This is why you're getting the "Authentication Required" error.

## Step-by-Step Setup

### 1. Enable 2-Factor Authentication (Required)
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click on "Security" in the left sidebar
3. Under "Signing in to Google", click "2-Step Verification"
4. Follow the setup process to enable 2FA
5. **You MUST have 2FA enabled to create App Passwords**

### 2. Generate Gmail App Password
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click on "Security" in the left sidebar
3. Under "Signing in to Google", click "App passwords"
4. You might need to sign in again
5. Select "Mail" from the dropdown
6. Select "Other (Custom name)" and enter "Muslim Directory API"
7. Click "Generate"
8. **Copy the 16-character password** (it looks like: `abcd efgh ijkl mnop`)

### 3. Update Your Configuration
Replace `YOUR_GMAIL_APP_PASSWORD_HERE` in `appsettings.json` with the App Password:

```json
{
  "EmailSettings": {
    "ServiceType": "SMTP",
    "SenderEmail": "<EMAIL>",
    "SenderName": "Muslim Directory",
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "Username": "<EMAIL>",
    "Password": "abcd efgh ijkl mnop"
  }
}
```

### 4. Test the Configuration
1. Build and run your API: `dotnet run`
2. Register a new user with your email
3. Check your email for the verification message

## Alternative: Use Resend (Recommended)

Instead of dealing with Gmail's complexity, I recommend switching to Resend:

```json
{
  "EmailSettings": {
    "ServiceType": "API",
    "ApiKey": "re_your_resend_api_key_here",
    "ApiBaseUrl": "https://api.resend.com",
    "SenderEmail": "<EMAIL>",
    "SenderName": "Muslim Directory"
  }
}
```

## Troubleshooting

### Common Issues:

1. **"Authentication Required" Error**
   - Make sure you're using the App Password, not your Gmail password
   - Verify 2FA is enabled on your Google account

2. **"Less Secure Apps" Error**
   - Google deprecated this setting. You MUST use App Passwords now

3. **App Password Option Missing**
   - Enable 2-Factor Authentication first
   - Wait a few minutes after enabling 2FA

4. **Still Getting Errors**
   - Double-check the App Password (no spaces in the config)
   - Make sure the Gmail account is the correct one
   - Try generating a new App Password

### Gmail SMTP Settings:
- **Server**: smtp.gmail.com
- **Port**: 587 (TLS) or 465 (SSL)
- **Security**: TLS/SSL enabled
- **Authentication**: Required (App Password)

## Why Resend is Better

- ✅ No 2FA setup required
- ✅ No App Password complexity
- ✅ Better deliverability
- ✅ 3,000 free emails/month
- ✅ Professional email infrastructure
- ✅ Simple API key authentication

Choose Resend for production applications!
