# Email Service Setup Guide

## Using Resend (Recommended - API-based)

### 1. Create a Resend Account
1. Go to [resend.com](https://resend.com)
2. Sign up for a free account
3. Verify your email address

### 2. Get Your API Key
1. Log into your Resend dashboard
2. Go to "API Keys" section
3. Click "Create API Key"
4. Give it a name (e.g., "Muslim Directory API")
5. Copy the generated API key

### 3. Set Up Your Domain (Optional but Recommended)
1. In Resend dashboard, go to "Domains"
2. Add your domain (e.g., `yourdomain.com`)
3. Follow the DNS verification steps
4. Once verified, you can send from `<EMAIL>`

### 4. Update Configuration
Update your `appsettings.json`:

```json
{
  "EmailSettings": {
    "ServiceType": "API",
    "ApiKey": "re_your_actual_api_key_here",
    "ApiBaseUrl": "https://api.resend.com",
    "SenderEmail": "<EMAIL>",
    "SenderName": "Muslim Directory"
  }
}
```

### 5. Update Email Templates (Optional)
In `EmailService.cs`, update the verification URLs:
- Replace `https://yourdomain.com/verify-email?token={verificationToken}` with your actual domain
- Replace `https://yourdomain.com/reset-password?token={resetToken}` with your actual domain

## Free Tier Limits
- **3,000 emails per month**
- **100 emails per day**
- Perfect for development and small applications

## Alternative: Using SMTP (Fallback)

If you prefer SMTP, set `ServiceType` to `"SMTP"` in your configuration:

```json
{
  "EmailSettings": {
    "ServiceType": "SMTP",
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderName": "Muslim Directory",
    "Username": "<EMAIL>",
    "Password": "your-app-password"
  }
}
```

## Testing

1. Start your API: `dotnet run`
2. Register a new user with your email address
3. Check your email for the verification message
4. The email should arrive within seconds with Resend

## Troubleshooting

### Common Issues:
1. **Invalid API Key**: Make sure you copied the full API key from Resend
2. **Domain Not Verified**: Use the default Resend domain or verify your custom domain
3. **Rate Limits**: Check your Resend dashboard for usage limits
4. **Email in Spam**: Add your domain to SPF/DKIM records (Resend provides these)

### Logs:
Check your application logs for email sending status:
- Success: "Email sent successfully via API to {email}"
- Failure: "Failed to send email via API to {email}"
