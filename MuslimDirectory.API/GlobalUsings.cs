global using System.Text;
global using System.Text.Json;
global using System.ComponentModel.DataAnnotations;
global using System.Security.Claims;
global using System.Security.Cryptography;
global using System.IdentityModel.Tokens.Jwt;
global using Microsoft.AspNetCore.Authentication.JwtBearer;
global using Microsoft.IdentityModel.Tokens;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.Extensions.Options;
global using Microsoft.Data.SqlClient;
global using Microsoft.AspNetCore.Mvc.Filters;

global using XGENO.DBHelpers.Extensions;
global using XGENO.DBHelpers.Core.Attributes;

global using MuslimDirectory.API.Filters;
global using MuslimDirectory.API.Models.DTOs.Auth;
global using MuslimDirectory.API.Models.DTOs.Common;
global using MuslimDirectory.API.Models.DTOs.User;
global using MuslimDirectory.API.Models.Entities;
global using MuslimDirectory.API.Models.Configuration;
global using MuslimDirectory.API.Services.Implementations;
global using MuslimDirectory.API.Services.Interfaces;
