using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.System;
using MuslimDirectory.API.Models.Common;
using MuslimDirectory.API.Models.Entities;
using XGENO.DBHelpers.Core;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Reflection;
using System.Text.Json;

namespace MuslimDirectory.API.Services.Implementations;

public class SystemService(IConfiguration configuration) : ISystemService
{
    private readonly string _connectionString = configuration.GetConnectionString("DefaultConnection") 
        ?? throw new ArgumentNullException("Connection string not found");
    private readonly IConfiguration _configuration = configuration;
    private static readonly DateTime _startTime = DateTime.UtcNow;

    public async Task<ApiResponse<SystemHealthDto>> GetSystemHealthAsync()
    {
        try
        {
            var healthDto = new SystemHealthDto
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Uptime = DateTime.UtcNow - _startTime,
                Version = GetApplicationVersion(),
                Components = new Dictionary<string, ComponentHealthDto>(),
                Metrics = await GetSystemMetricsAsync()
            };

            // Check database health
            var dbHealth = await CheckDatabaseHealthAsync();
            healthDto.Components["Database"] = dbHealth;

            // Check external services health (if any)
            var emailHealth = await CheckEmailServiceHealthAsync();
            healthDto.Components["EmailService"] = emailHealth;

            // Determine overall health status
            var unhealthyComponents = healthDto.Components.Values.Count(c => c.Status == "Unhealthy");
            var degradedComponents = healthDto.Components.Values.Count(c => c.Status == "Degraded");

            if (unhealthyComponents > 0)
                healthDto.Status = "Unhealthy";
            else if (degradedComponents > 0)
                healthDto.Status = "Degraded";

            return ApiResponse<SystemHealthDto>.SuccessResponse(healthDto);
        }
        catch (Exception ex)
        {
            return ApiResponse<SystemHealthDto>.ErrorResponse(
                "HEALTH_CHECK_FAILED", "System health check failed", ex.Message);
        }
    }

    public async Task<ApiResponse<SystemSettingsDto>> GetSystemSettingsAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get public system settings
            var settings = await connection.ExecuteQuery<SystemSetting>(@"
                SELECT [Key], Value, DataType
                FROM SystemSettings
                WHERE IsPublic = 1 AND IsActive = 1
                ORDER BY Category, [Key]");

            var settingsDto = new SystemSettingsDto
            {
                ApplicationName = GetSettingValue(settings, "ApplicationName", "Muslim Directory"),
                Environment = _configuration["Environment"] ?? "Production",
                MaintenanceMode = GetSettingValue(settings, "MaintenanceMode", false),
                MaintenanceMessage = GetSettingValue(settings, "MaintenanceMessage", (string?)null),
                RegistrationEnabled = GetSettingValue(settings, "RegistrationEnabled", true),
                EmailVerificationRequired = GetSettingValue(settings, "EmailVerificationRequired", true),
                ReviewModerationEnabled = GetSettingValue(settings, "ReviewModerationEnabled", true),
                MaxFileUploadSizeMB = GetSettingValue(settings, "MaxFileUploadSizeMB", 10),
                AllowedImageFormats = GetSettingValue(settings, "AllowedImageFormats", new List<string> { "jpg", "jpeg", "png", "webp" }),
                SessionTimeoutMinutes = GetSettingValue(settings, "SessionTimeoutMinutes", 60),
                PasswordMinLength = GetSettingValue(settings, "PasswordMinLength", 8),
                TwoFactorAuthEnabled = GetSettingValue(settings, "TwoFactorAuthEnabled", false),
                RateLimits = new RateLimitSettingsDto
                {
                    RequestsPerMinute = GetSettingValue(settings, "RateLimit.RequestsPerMinute", 100),
                    SearchRequestsPerMinute = GetSettingValue(settings, "RateLimit.SearchRequestsPerMinute", 50),
                    AuthRequestsPerMinute = GetSettingValue(settings, "RateLimit.AuthRequestsPerMinute", 10),
                    UploadRequestsPerMinute = GetSettingValue(settings, "RateLimit.UploadRequestsPerMinute", 5)
                },
                SearchSettings = new SearchSettingsDto
                {
                    MaxResultsPerPage = GetSettingValue(settings, "Search.MaxResultsPerPage", 100),
                    DefaultResultsPerPage = GetSettingValue(settings, "Search.DefaultResultsPerPage", 20),
                    MaxSearchRadius = GetSettingValue(settings, "Search.MaxSearchRadius", 100),
                    DefaultSearchRadius = GetSettingValue(settings, "Search.DefaultSearchRadius", 25),
                    GeolocationEnabled = GetSettingValue(settings, "Search.GeolocationEnabled", true),
                    SearchHistoryRetentionDays = GetSettingValue(settings, "Search.HistoryRetentionDays", 90)
                },
                NotificationSettings = new NotificationSettingsDto
                {
                    EmailNotificationsEnabled = GetSettingValue(settings, "Notifications.EmailEnabled", true),
                    PushNotificationsEnabled = GetSettingValue(settings, "Notifications.PushEnabled", false),
                    ReviewNotificationsEnabled = GetSettingValue(settings, "Notifications.ReviewEnabled", true),
                    ListingNotificationsEnabled = GetSettingValue(settings, "Notifications.ListingEnabled", true),
                    SystemNotificationsEnabled = GetSettingValue(settings, "Notifications.SystemEnabled", true)
                }
            };

            return ApiResponse<SystemSettingsDto>.SuccessResponse(settingsDto);
        }
        catch (Exception ex)
        {
            return ApiResponse<SystemSettingsDto>.ErrorResponse(
                "GET_SETTINGS_FAILED", "Failed to get system settings", ex.Message);
        }
    }

    public async Task<ApiResponse<SystemVersionDto>> GetSystemVersionAsync()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version?.ToString() ?? "1.0.0";
            var buildDate = GetBuildDate(assembly);

            var versionDto = new SystemVersionDto
            {
                Version = version,
                BuildNumber = GetBuildNumber(),
                BuildDate = buildDate,
                Environment = _configuration["Environment"] ?? "Production",
                GitCommit = _configuration["GitCommit"] ?? "unknown",
                GitBranch = _configuration["GitBranch"] ?? "main",
                Features = GetEnabledFeatures(),
                Dependencies = GetDependencyVersions(),
                Compatibility = new SystemCompatibilityDto
                {
                    MinimumClientVersion = "1.0.0",
                    RecommendedClientVersion = "1.0.0",
                    SupportedApiVersions = new List<string> { "v1" },
                    BackwardCompatible = true
                }
            };

            return ApiResponse<SystemVersionDto>.SuccessResponse(versionDto);
        }
        catch (Exception ex)
        {
            return ApiResponse<SystemVersionDto>.ErrorResponse(
                "GET_VERSION_FAILED", "Failed to get system version", ex.Message);
        }
    }

    public async Task<ApiResponse<List<CountryDto>>> GetCountriesAsync(bool includeStates = false, bool includeCities = false)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get countries with listing counts
            var countries = await connection.ExecuteQuery<dynamic>(@"
                SELECT c.ID, c.Code, c.Name, c.NativeName, c.Region, c.SubRegion, 
                       c.Languages, c.Currencies, c.Flag, c.IsSupported,
                       COUNT(l.ID) as ListingCount
                FROM Countries c
                LEFT JOIN Listings l ON c.Code = l.Country AND l.Status = 'Active'
                WHERE c.IsSupported = 1
                GROUP BY c.ID, c.Code, c.Name, c.NativeName, c.Region, c.SubRegion, 
                         c.Languages, c.Currencies, c.Flag, c.IsSupported
                ORDER BY c.DisplayOrder, c.Name");

            var countryDtos = new List<CountryDto>();

            foreach (var country in countries)
            {
                var countryDto = new CountryDto
                {
                    Code = country.Code?.ToString() ?? "",
                    Name = country.Name?.ToString() ?? "",
                    NativeName = country.NativeName?.ToString() ?? "",
                    Region = country.Region?.ToString() ?? "",
                    SubRegion = country.SubRegion?.ToString() ?? "",
                    Languages = ParseJsonArray(country.Languages?.ToString()),
                    Currencies = ParseJsonArray(country.Currencies?.ToString()),
                    Flag = country.Flag?.ToString() ?? "",
                    IsSupported = country.IsSupported != null && (bool)country.IsSupported,
                    ListingCount = country.ListingCount != null ? (int)country.ListingCount : 0
                };

                if (includeStates)
                {
                    var statesResult = await GetStatesByCountryAsync(countryDto.Code);
                    if (statesResult.Success)
                    {
                        countryDto.StatesProvinces = statesResult.Data ?? new List<StateProvinceDto>();
                    }
                }

                countryDtos.Add(countryDto);
            }

            return ApiResponse<List<CountryDto>>.SuccessResponse(countryDtos);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<CountryDto>>.ErrorResponse(
                "GET_COUNTRIES_FAILED", "Failed to get countries", ex.Message);
        }
    }

    public async Task<ApiResponse<List<CountryDto>>> GetSupportedCountriesAsync()
    {
        return await GetCountriesAsync(false, false);
    }

    public async Task<ApiResponse<CountryDto>> GetCountryByCodeAsync(string countryCode, bool includeStates = false, bool includeCities = false)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var country = await connection.ExecuteQuery<dynamic>(@"
                SELECT c.ID, c.Code, c.Name, c.NativeName, c.Region, c.SubRegion,
                       c.Languages, c.Currencies, c.Flag, c.IsSupported,
                       COUNT(l.ID) as ListingCount
                FROM Countries c
                LEFT JOIN Listings l ON c.Code = l.Country AND l.Status = 'Active'
                WHERE c.Code = @CountryCode AND c.IsSupported = 1
                GROUP BY c.ID, c.Code, c.Name, c.NativeName, c.Region, c.SubRegion,
                         c.Languages, c.Currencies, c.Flag, c.IsSupported",
                countryCode.ToSqlParam("@CountryCode"));

            var countryData = country.FirstOrDefault();
            if (countryData == null)
            {
                return ApiResponse<CountryDto>.ErrorResponse(
                    "COUNTRY_NOT_FOUND", "Country not found or not supported");
            }

            var countryDto = new CountryDto
            {
                Code = countryData.Code?.ToString() ?? "",
                Name = countryData.Name?.ToString() ?? "",
                NativeName = countryData.NativeName?.ToString() ?? "",
                Region = countryData.Region?.ToString() ?? "",
                SubRegion = countryData.SubRegion?.ToString() ?? "",
                Languages = ParseJsonArray(countryData.Languages?.ToString()),
                Currencies = ParseJsonArray(countryData.Currencies?.ToString()),
                Flag = countryData.Flag?.ToString() ?? "",
                IsSupported = countryData.IsSupported != null && (bool)countryData.IsSupported,
                ListingCount = countryData.ListingCount != null ? (int)countryData.ListingCount : 0
            };

            if (includeStates)
            {
                var statesResult = await GetStatesByCountryAsync(countryCode);
                if (statesResult.Success)
                {
                    countryDto.StatesProvinces = statesResult.Data ?? new List<StateProvinceDto>();
                }
            }

            return ApiResponse<CountryDto>.SuccessResponse(countryDto);
        }
        catch (Exception ex)
        {
            return ApiResponse<CountryDto>.ErrorResponse(
                "GET_COUNTRY_FAILED", "Failed to get country", ex.Message);
        }
    }

    public async Task<ApiResponse<List<StateProvinceDto>>> GetStatesByCountryAsync(string countryCode)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var states = await connection.ExecuteQuery<dynamic>(@"
                SELECT sp.ID, sp.Code, sp.Name, sp.Type,
                       COUNT(l.ID) as ListingCount
                FROM StateProvinces sp
                INNER JOIN Countries c ON sp.CountryID = c.ID
                LEFT JOIN Listings l ON sp.Name = l.State AND l.Status = 'Active'
                WHERE c.Code = @CountryCode
                GROUP BY sp.ID, sp.Code, sp.Name, sp.Type
                ORDER BY sp.DisplayOrder, sp.Name",
                countryCode.ToSqlParam("@CountryCode"));

            var stateDtos = states.Select(s => new StateProvinceDto
            {
                Code = s.Code?.ToString() ?? "",
                Name = s.Name?.ToString() ?? "",
                Type = s.Type?.ToString() ?? "",
                ListingCount = s.ListingCount != null ? (int)s.ListingCount : 0
            }).ToList();

            return ApiResponse<List<StateProvinceDto>>.SuccessResponse(stateDtos);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<StateProvinceDto>>.ErrorResponse(
                "GET_STATES_FAILED", "Failed to get states", ex.Message);
        }
    }

    public async Task<ApiResponse<List<CityDto>>> GetCitiesByStateAsync(Guid stateId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var cities = await connection.ExecuteQuery<dynamic>(@"
                SELECT c.Name, c.Latitude, c.Longitude, c.IsCapital, c.IsMajorCity,
                       COUNT(l.ID) as ListingCount
                FROM Cities c
                LEFT JOIN Listings l ON c.Name = l.City AND l.Status = 'Active'
                WHERE c.StateProvinceID = @StateId
                GROUP BY c.Name, c.Latitude, c.Longitude, c.IsCapital, c.IsMajorCity
                ORDER BY c.IsMajorCity DESC, c.IsCapital DESC, c.Name",
                stateId.ToSqlParam("@StateId"));

            var cityDtos = cities.Select(c => new CityDto
            {
                Name = c.Name?.ToString() ?? "",
                Latitude = c.Latitude as decimal?,
                Longitude = c.Longitude as decimal?,
                IsCapital = c.IsCapital != null && (bool)c.IsCapital,
                IsMajorCity = c.IsMajorCity != null && (bool)c.IsMajorCity,
                ListingCount = c.ListingCount != null ? (int)c.ListingCount : 0
            }).ToList();

            return ApiResponse<List<CityDto>>.SuccessResponse(cityDtos);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<CityDto>>.ErrorResponse(
                "GET_CITIES_FAILED", "Failed to get cities", ex.Message);
        }
    }

    public async Task<ApiResponse<List<CityDto>>> GetMajorCitiesByCountryAsync(string countryCode)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var cities = await connection.ExecuteQuery<dynamic>(@"
                SELECT c.Name, c.Latitude, c.Longitude, c.IsCapital, c.IsMajorCity,
                       COUNT(l.ID) as ListingCount
                FROM Cities c
                INNER JOIN Countries co ON c.CountryID = co.ID
                LEFT JOIN Listings l ON c.Name = l.City AND l.Status = 'Active'
                WHERE co.Code = @CountryCode AND (c.IsMajorCity = 1 OR c.IsCapital = 1)
                GROUP BY c.Name, c.Latitude, c.Longitude, c.IsCapital, c.IsMajorCity
                ORDER BY c.IsCapital DESC, c.IsMajorCity DESC, COUNT(l.ID) DESC",
                countryCode.ToSqlParam("@CountryCode"));

            var cityDtos = cities.Select(c => new CityDto
            {
                Name = c.Name?.ToString() ?? "",
                Latitude = c.Latitude as decimal?,
                Longitude = c.Longitude as decimal?,
                IsCapital = c.IsCapital != null && (bool)c.IsCapital,
                IsMajorCity = c.IsMajorCity != null && (bool)c.IsMajorCity,
                ListingCount = c.ListingCount != null ? (int)c.ListingCount : 0
            }).ToList();

            return ApiResponse<List<CityDto>>.SuccessResponse(cityDtos);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<CityDto>>.ErrorResponse(
                "GET_MAJOR_CITIES_FAILED", "Failed to get major cities", ex.Message);
        }
    }

    // Private helper methods
    private async Task<SystemMetricsDto> GetSystemMetricsAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var metrics = new SystemMetricsDto();

            // Get user metrics
            var userMetrics = await connection.ExecuteQuery<dynamic>(@"
                SELECT
                    COUNT(*) as TotalUsers,
                    COUNT(CASE WHEN LastLoginAt >= DATEADD(day, -30, GETDATE()) THEN 1 END) as ActiveUsers
                FROM Users WHERE IsActive = 1");

            var userData = userMetrics.FirstOrDefault();
            if (userData != null)
            {
                metrics.TotalUsers = userData.TotalUsers != null ? (long)(int)userData.TotalUsers : 0;
                metrics.ActiveUsers = userData.ActiveUsers != null ? (long)(int)userData.ActiveUsers : 0;
            }

            // Get listing metrics
            var listingMetrics = await connection.ExecuteQuery<dynamic>(@"
                SELECT
                    COUNT(*) as TotalListings,
                    COUNT(CASE WHEN Status = 'Active' THEN 1 END) as ActiveListings
                FROM Listings");

            var listingData = listingMetrics.FirstOrDefault();
            if (listingData != null)
            {
                metrics.TotalListings = listingData.TotalListings != null ? (long)(int)listingData.TotalListings : 0;
                metrics.ActiveListings = listingData.ActiveListings != null ? (long)(int)listingData.ActiveListings : 0;
            }

            // Get other metrics
            var otherMetrics = await connection.ExecuteQuery<dynamic>(@"
                SELECT
                    (SELECT COUNT(*) FROM Reviews WHERE Status = 'Approved') as TotalReviews,
                    (SELECT COUNT(*) FROM Organizations WHERE IsActive = 1) as TotalOrganizations,
                    (SELECT COUNT(*) FROM SearchHistory WHERE CreatedAt >= DATEADD(day, -1, GETDATE())) as TotalSearches");

            var otherData = otherMetrics.FirstOrDefault();
            if (otherData != null)
            {
                metrics.TotalReviews = otherData.TotalReviews != null ? (long)(int)otherData.TotalReviews : 0;
                metrics.TotalOrganizations = otherData.TotalOrganizations != null ? (long)(int)otherData.TotalOrganizations : 0;
                metrics.TotalSearches = otherData.TotalSearches != null ? (long)(int)otherData.TotalSearches : 0;
            }

            // Set default values for system metrics (would be implemented with actual monitoring)
            metrics.AverageResponseTime = 150.0; // milliseconds
            metrics.RequestsPerMinute = 25;
            metrics.DatabaseConnectionPoolUsage = 15.5; // percentage
            metrics.MemoryUsageMB = 256;
            metrics.CpuUsagePercent = 12.5;

            return metrics;
        }
        catch
        {
            return new SystemMetricsDto();
        }
    }

    private async Task<ComponentHealthDto> CheckDatabaseHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var result = await connection.ExecuteQuery<int>("SELECT 1");
            stopwatch.Stop();

            return new ComponentHealthDto
            {
                Status = "Healthy",
                Description = "Database connection successful",
                ResponseTime = stopwatch.Elapsed,
                LastChecked = DateTime.UtcNow,
                Details = new Dictionary<string, object>
                {
                    ["ConnectionString"] = _connectionString.Split(';')[0], // Only server part
                    ["ResponseTimeMs"] = stopwatch.ElapsedMilliseconds
                }
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return new ComponentHealthDto
            {
                Status = "Unhealthy",
                Description = $"Database connection failed: {ex.Message}",
                ResponseTime = stopwatch.Elapsed,
                LastChecked = DateTime.UtcNow,
                Details = new Dictionary<string, object>
                {
                    ["Error"] = ex.Message,
                    ["ResponseTimeMs"] = stopwatch.ElapsedMilliseconds
                }
            };
        }
    }

    private async Task<ComponentHealthDto> CheckEmailServiceHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            // Simple health check - just verify configuration exists
            var emailConfig = _configuration.GetSection("Resend");
            var apiKey = emailConfig["ApiKey"];

            stopwatch.Stop();

            return new ComponentHealthDto
            {
                Status = !string.IsNullOrEmpty(apiKey) ? "Healthy" : "Degraded",
                Description = !string.IsNullOrEmpty(apiKey) ? "Email service configured" : "Email service not configured",
                ResponseTime = stopwatch.Elapsed,
                LastChecked = DateTime.UtcNow,
                Details = new Dictionary<string, object>
                {
                    ["Configured"] = !string.IsNullOrEmpty(apiKey),
                    ["Provider"] = "Resend"
                }
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return new ComponentHealthDto
            {
                Status = "Unhealthy",
                Description = $"Email service check failed: {ex.Message}",
                ResponseTime = stopwatch.Elapsed,
                LastChecked = DateTime.UtcNow,
                Details = new Dictionary<string, object>
                {
                    ["Error"] = ex.Message
                }
            };
        }
    }

    private string GetApplicationVersion()
    {
        var assembly = Assembly.GetExecutingAssembly();
        return assembly.GetName().Version?.ToString() ?? "1.0.0";
    }

    private string GetBuildNumber()
    {
        return _configuration["BuildNumber"] ?? DateTime.UtcNow.ToString("yyyyMMddHHmm");
    }

    private DateTime GetBuildDate(Assembly assembly)
    {
        try
        {
            var buildDate = _configuration["BuildDate"];
            if (!string.IsNullOrEmpty(buildDate) && DateTime.TryParse(buildDate, out var date))
                return date;

            // Fallback to assembly creation time
            var location = assembly.Location;
            if (!string.IsNullOrEmpty(location) && File.Exists(location))
                return File.GetCreationTimeUtc(location);

            return DateTime.UtcNow;
        }
        catch
        {
            return DateTime.UtcNow;
        }
    }

    private List<string> GetEnabledFeatures()
    {
        var features = new List<string>();

        if (_configuration.GetValue<bool>("Features:EmailVerification", true))
            features.Add("EmailVerification");

        if (_configuration.GetValue<bool>("Features:ReviewModeration", true))
            features.Add("ReviewModeration");

        if (_configuration.GetValue<bool>("Features:GeolocationSearch", true))
            features.Add("GeolocationSearch");

        if (_configuration.GetValue<bool>("Features:FileUpload", true))
            features.Add("FileUpload");

        if (_configuration.GetValue<bool>("Features:TwoFactorAuth", false))
            features.Add("TwoFactorAuth");

        return features;
    }

    private Dictionary<string, string> GetDependencyVersions()
    {
        var dependencies = new Dictionary<string, string>();

        try
        {
            var assemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => !a.IsDynamic && !string.IsNullOrEmpty(a.Location))
                .ToList();

            foreach (var assembly in assemblies)
            {
                var name = assembly.GetName();
                if (name.Name != null && IsRelevantDependency(name.Name))
                {
                    dependencies[name.Name] = name.Version?.ToString() ?? "unknown";
                }
            }
        }
        catch
        {
            // Ignore errors in dependency enumeration
        }

        return dependencies;
    }

    private bool IsRelevantDependency(string assemblyName)
    {
        var relevantPrefixes = new[]
        {
            "Microsoft.AspNetCore",
            "Microsoft.EntityFrameworkCore",
            "System.Data.SqlClient",
            "XGENO.DBHelpers",
            "Newtonsoft.Json",
            "System.Text.Json"
        };

        return relevantPrefixes.Any(prefix => assemblyName.StartsWith(prefix, StringComparison.OrdinalIgnoreCase));
    }

    private T GetSettingValue<T>(IEnumerable<SystemSetting> settings, string key, T defaultValue)
    {
        var setting = settings.FirstOrDefault(s => s.Key == key);
        if (setting == null)
            return defaultValue;

        try
        {
            return setting.DataType.ToLower() switch
            {
                "bool" => (T)(object)bool.Parse(setting.Value),
                "int" => (T)(object)int.Parse(setting.Value),
                "decimal" => (T)(object)decimal.Parse(setting.Value),
                "json" => JsonSerializer.Deserialize<T>(setting.Value) ?? defaultValue,
                _ => (T)(object)setting.Value
            };
        }
        catch
        {
            return defaultValue;
        }
    }

    private List<string> ParseJsonArray(string? json)
    {
        if (string.IsNullOrEmpty(json))
            return new List<string>();

        try
        {
            return JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }
}
