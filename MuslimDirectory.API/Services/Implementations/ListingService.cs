using MuslimDirectory.API.Models.DTOs.Listing;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;
using MuslimDirectory.API.Models.DTOs.Organization;
using MuslimDirectory.API.Models.DTOs.Category;
using MuslimDirectory.API.Models.DTOs.Tag;
using MuslimDirectory.API.Services.Interfaces;
using System.Data;
using System.Data.SqlClient;
using XGENO.DBHelpers.Core;
using System.Text.Json;

namespace MuslimDirectory.API.Services.Implementations;

public class ListingService : IListingService
{
    private readonly string _connectionString;

    public ListingService(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("Connection string not found");
    }

    public async Task<ApiResponse<PaginatedResult<ListingDto>>> GetListingsAsync(
        int page = 1, int limit = 20, string? search = null, string? category = null,
        string? platform = null, string? language = null, string? pricingModel = null,
        string? complianceStatus = null, bool? featured = null, string? sortBy = null,
        int? minRating = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var offset = (page - 1) * limit;
            var whereConditions = new List<string> { "l.Status = 'Approved'" };
            var parameters = new List<SqlParameter>();

            // Build WHERE conditions
            if (!string.IsNullOrWhiteSpace(search))
            {
                whereConditions.Add("(l.Title LIKE @Search OR l.ShortDescription LIKE @Search OR l.FullDescription LIKE @Search)");
                parameters.Add(($"%{search}%").ToSqlParam("@Search"));
            }

            if (!string.IsNullOrWhiteSpace(category))
            {
                whereConditions.Add("EXISTS (SELECT 1 FROM ListingCategories lc INNER JOIN Categories c ON lc.CategoryID = c.ID WHERE lc.ListingID = l.ID AND c.ID = @CategoryId)");
                parameters.Add((Guid.Parse(category)).ToSqlParam("@CategoryId"));
            }

            if (!string.IsNullOrWhiteSpace(platform))
            {
                whereConditions.Add("l.SupportedPlatforms LIKE @Platform");
                parameters.Add(($"%{platform}%").ToSqlParam("@Platform"));
            }

            if (!string.IsNullOrWhiteSpace(language))
            {
                whereConditions.Add("(l.PrimaryLanguage = @Language OR l.SupportedLanguages LIKE @LanguagePattern)");
                parameters.Add((language).ToSqlParam("@Language"));
                parameters.Add(($"%{language}%").ToSqlParam("@LanguagePattern"));
            }

            if (!string.IsNullOrWhiteSpace(pricingModel))
            {
                whereConditions.Add("l.PricingModel = @PricingModel");
                parameters.Add((pricingModel).ToSqlParam("@PricingModel"));
            }

            if (!string.IsNullOrWhiteSpace(complianceStatus))
            {
                whereConditions.Add("l.IslamicComplianceStatus = @ComplianceStatus");
                parameters.Add((complianceStatus).ToSqlParam("@ComplianceStatus"));
            }

            if (featured.HasValue && featured.Value)
            {
                whereConditions.Add("l.FeaturedLevel > 0");
            }

            if (minRating.HasValue)
            {
                whereConditions.Add("ISNULL(r.AverageRating, 0) >= @MinRating");
                parameters.Add((minRating.Value).ToSqlParam("@MinRating"));
            }

            var whereClause = whereConditions.Count > 0 ? $"WHERE {string.Join(" AND ", whereConditions)}" : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(DISTINCT l.ID)
                FROM Listings l
                LEFT JOIN (
                    SELECT ListingID, AVG(CAST(Rating AS FLOAT)) as AverageRating
                    FROM Reviews 
                    WHERE Status = 'Approved'
                    GROUP BY ListingID
                ) r ON l.ID = r.ListingID
                {whereClause}";

            var totalCountResult = await connection.ExecuteQuery<int>(countQuery, parameters.ToArray());
            var totalCount = totalCountResult.First();

            // Build ORDER BY clause
            var orderBy = sortBy?.ToLower() switch
            {
                "newest" => "l.CreatedAt DESC",
                "oldest" => "l.CreatedAt ASC",
                "rating" => "ISNULL(r.AverageRating, 0) DESC, l.CreatedAt DESC",
                "popular" => "l.ViewCount DESC, l.CreatedAt DESC",
                "title" => "l.Title ASC",
                _ => "l.FeaturedLevel DESC, l.CreatedAt DESC"
            };

            // Get listings
            var query = $@"
                SELECT 
                    l.ID,
                    l.Title,
                    l.ShortDescription,
                    l.LogoURL,
                    l.PlatformType,
                    l.SupportedPlatforms,
                    l.PricingModel,
                    l.Price,
                    l.Currency,
                    l.IslamicComplianceStatus,
                    l.FeaturedLevel,
                    l.ViewCount,
                    l.CreatedAt,
                    l.UpdatedAt,
                    ISNULL(r.AverageRating, 0) as AverageRating,
                    ISNULL(r.ReviewCount, 0) as ReviewCount,
                    o.ID as OrganizationId,
                    o.Name as OrganizationName,
                    o.LogoURL as OrganizationLogoURL,
                    o.IsVerified as OrganizationIsVerified,
                    pc.ID as PrimaryCategoryId,
                    pc.Name as PrimaryCategoryName
                FROM Listings l
                LEFT JOIN Organizations o ON l.OrganizationID = o.ID
                LEFT JOIN (
                    SELECT ListingID, AVG(CAST(Rating AS FLOAT)) as AverageRating, COUNT(*) as ReviewCount
                    FROM Reviews 
                    WHERE Status = 'Approved'
                    GROUP BY ListingID
                ) r ON l.ID = r.ListingID
                LEFT JOIN ListingCategories lc ON l.ID = lc.ListingID AND lc.IsPrimary = 1
                LEFT JOIN Categories pc ON lc.CategoryID = pc.ID
                {whereClause}
                ORDER BY {orderBy}
                OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY";

            parameters.Add((offset).ToSqlParam("@Offset"));
            parameters.Add((limit).ToSqlParam("@Limit"));

            var listings = await connection.ExecuteQuery<dynamic>(query, parameters.ToArray());

            var listingDtos = listings.Select(l => new ListingDto
            {
                Id = l.ID,
                Title = l.Title ?? "",
                ShortDescription = l.ShortDescription,
                LogoURL = l.LogoURL,
                PlatformType = l.PlatformType,
                SupportedPlatforms = ParseJsonArray(l.SupportedPlatforms),
                PricingModel = l.PricingModel,
                Price = l.Price,
                Currency = l.Currency,
                IslamicComplianceStatus = l.IslamicComplianceStatus ?? "",
                Rating = new RatingDto
                {
                    Average = l.AverageRating ?? 0,
                    Count = l.ReviewCount ?? 0
                },
                Organization = l.OrganizationId != null ? new OrganizationListDto
                {
                    Id = l.OrganizationId,
                    Name = l.OrganizationName ?? "",
                    LogoURL = l.OrganizationLogoURL,
                    IsVerified = l.OrganizationIsVerified ?? false
                } : null,
                PrimaryCategory = l.PrimaryCategoryId != null ? new CategoryListDto
                {
                    Id = l.PrimaryCategoryId,
                    Name = l.PrimaryCategoryName ?? ""
                } : null,
                FeaturedLevel = l.FeaturedLevel ?? 0,
                ViewCount = l.ViewCount ?? 0,
                CreatedAt = l.CreatedAt,
                UpdatedAt = l.UpdatedAt
            }).ToList();

            var result = new PaginatedResult<ListingDto>
            {
                Items = listingDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<ListingDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<ListingDto>>.ErrorResponse("Failed to get listings", ex.Message);
        }
    }

    public async Task<ApiResponse<List<FeaturedListingDto>>> GetFeaturedListingsAsync(int limit = 6)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var query = @"
                SELECT TOP (@Limit)
                    l.ID,
                    l.Title,
                    l.ShortDescription,
                    l.LogoURL,
                    l.FeaturedLevel,
                    ISNULL(r.AverageRating, 0) as Rating,
                    ISNULL(r.ReviewCount, 0) as ReviewCount,
                    o.ID as OrganizationId,
                    o.Name as OrganizationName,
                    o.LogoURL as OrganizationLogoURL,
                    o.IsVerified as OrganizationIsVerified
                FROM Listings l
                LEFT JOIN Organizations o ON l.OrganizationID = o.ID
                LEFT JOIN (
                    SELECT ListingID, AVG(CAST(Rating AS FLOAT)) as AverageRating, COUNT(*) as ReviewCount
                    FROM Reviews 
                    WHERE Status = 'Approved'
                    GROUP BY ListingID
                ) r ON l.ID = r.ListingID
                WHERE l.Status = 'Approved' AND l.FeaturedLevel > 0
                ORDER BY l.FeaturedLevel DESC, l.CreatedAt DESC";

            var parameters = new[] { (limit).ToSqlParam("@Limit") };
            var listings = await connection.ExecuteQuery<dynamic>(query, parameters);

            var featuredListings = listings.Select(l => new FeaturedListingDto
            {
                Id = l.ID,
                Title = l.Title ?? "",
                ShortDescription = l.ShortDescription,
                LogoURL = l.LogoURL,
                Rating = l.Rating ?? 0,
                ReviewCount = l.ReviewCount ?? 0,
                FeaturedLevel = l.FeaturedLevel ?? 0,
                Organization = l.OrganizationId != null ? new OrganizationListDto
                {
                    Id = l.OrganizationId,
                    Name = l.OrganizationName ?? "",
                    LogoURL = l.OrganizationLogoURL,
                    IsVerified = l.OrganizationIsVerified ?? false
                } : null
            }).ToList();

            return ApiResponse<List<FeaturedListingDto>>.SuccessResponse(featuredListings);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<FeaturedListingDto>>.ErrorResponse("Failed to get featured listings", ex.Message);
        }
    }

    public async Task<ApiResponse<List<ListingDto>>> GetNewReleasesAsync(int limit = 10, int days = 30)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var cutoffDate = DateTime.UtcNow.AddDays(-days);
            var query = @"
                SELECT TOP (@Limit)
                    l.ID,
                    l.Title,
                    l.ShortDescription,
                    l.LogoURL,
                    l.PlatformType,
                    l.SupportedPlatforms,
                    l.PricingModel,
                    l.Price,
                    l.Currency,
                    l.IslamicComplianceStatus,
                    l.FeaturedLevel,
                    l.ViewCount,
                    l.CreatedAt,
                    l.UpdatedAt,
                    ISNULL(r.AverageRating, 0) as AverageRating,
                    ISNULL(r.ReviewCount, 0) as ReviewCount,
                    o.ID as OrganizationId,
                    o.Name as OrganizationName,
                    o.LogoURL as OrganizationLogoURL,
                    o.IsVerified as OrganizationIsVerified,
                    pc.ID as PrimaryCategoryId,
                    pc.Name as PrimaryCategoryName
                FROM Listings l
                LEFT JOIN Organizations o ON l.OrganizationID = o.ID
                LEFT JOIN (
                    SELECT ListingID, AVG(CAST(Rating AS FLOAT)) as AverageRating, COUNT(*) as ReviewCount
                    FROM Reviews
                    WHERE Status = 'Approved'
                    GROUP BY ListingID
                ) r ON l.ID = r.ListingID
                LEFT JOIN ListingCategories lc ON l.ID = lc.ListingID AND lc.IsPrimary = 1
                LEFT JOIN Categories pc ON lc.CategoryID = pc.ID
                WHERE l.Status = 'Approved' AND l.CreatedAt >= @CutoffDate
                ORDER BY l.CreatedAt DESC";

            var parameters = new[] {
                (limit).ToSqlParam("@Limit"),
                (cutoffDate).ToSqlParam("@CutoffDate")
            };
            var listings = await connection.ExecuteQuery<dynamic>(query, parameters);

            var listingDtos = listings.Select(l => new ListingDto
            {
                Id = l.ID,
                Title = l.Title ?? "",
                ShortDescription = l.ShortDescription,
                LogoURL = l.LogoURL,
                PlatformType = l.PlatformType,
                SupportedPlatforms = ParseJsonArray(l.SupportedPlatforms),
                PricingModel = l.PricingModel,
                Price = l.Price,
                Currency = l.Currency,
                IslamicComplianceStatus = l.IslamicComplianceStatus ?? "",
                Rating = new RatingDto
                {
                    Average = l.AverageRating ?? 0,
                    Count = l.ReviewCount ?? 0
                },
                Organization = l.OrganizationId != null ? new OrganizationListDto
                {
                    Id = l.OrganizationId,
                    Name = l.OrganizationName ?? "",
                    LogoURL = l.OrganizationLogoURL,
                    IsVerified = l.OrganizationIsVerified ?? false
                } : null,
                PrimaryCategory = l.PrimaryCategoryId != null ? new CategoryListDto
                {
                    Id = l.PrimaryCategoryId,
                    Name = l.PrimaryCategoryName ?? ""
                } : null,
                FeaturedLevel = l.FeaturedLevel ?? 0,
                ViewCount = l.ViewCount ?? 0,
                CreatedAt = l.CreatedAt,
                UpdatedAt = l.UpdatedAt
            }).ToList();

            return ApiResponse<List<ListingDto>>.SuccessResponse(listingDtos);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<ListingDto>>.ErrorResponse("Failed to get new releases", ex.Message);
        }
    }

    public async Task<ApiResponse<List<ListingDto>>> GetTrendingListingsAsync(
        string timeframe = "week", int limit = 10)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var days = timeframe.ToLower() switch
            {
                "day" => 1,
                "week" => 7,
                "month" => 30,
                _ => 7
            };

            var cutoffDate = DateTime.UtcNow.AddDays(-days);
            var query = @"
                SELECT TOP (@Limit)
                    l.ID,
                    l.Title,
                    l.ShortDescription,
                    l.LogoURL,
                    l.PlatformType,
                    l.SupportedPlatforms,
                    l.PricingModel,
                    l.Price,
                    l.Currency,
                    l.IslamicComplianceStatus,
                    l.FeaturedLevel,
                    l.ViewCount,
                    l.CreatedAt,
                    l.UpdatedAt,
                    ISNULL(r.AverageRating, 0) as AverageRating,
                    ISNULL(r.ReviewCount, 0) as ReviewCount,
                    o.ID as OrganizationId,
                    o.Name as OrganizationName,
                    o.LogoURL as OrganizationLogoURL,
                    o.IsVerified as OrganizationIsVerified,
                    pc.ID as PrimaryCategoryId,
                    pc.Name as PrimaryCategoryName
                FROM Listings l
                LEFT JOIN Organizations o ON l.OrganizationID = o.ID
                LEFT JOIN (
                    SELECT ListingID, AVG(CAST(Rating AS FLOAT)) as AverageRating, COUNT(*) as ReviewCount
                    FROM Reviews
                    WHERE Status = 'Approved'
                    GROUP BY ListingID
                ) r ON l.ID = r.ListingID
                LEFT JOIN ListingCategories lc ON l.ID = lc.ListingID AND lc.IsPrimary = 1
                LEFT JOIN Categories pc ON lc.CategoryID = pc.ID
                WHERE l.Status = 'Approved' AND l.UpdatedAt >= @CutoffDate
                ORDER BY l.ViewCount DESC, ISNULL(r.AverageRating, 0) DESC, l.CreatedAt DESC";

            var parameters = new[] {
                (limit).ToSqlParam("@Limit"),
                (cutoffDate).ToSqlParam("@CutoffDate")
            };
            var listings = await connection.ExecuteQuery<dynamic>(query, parameters);

            var listingDtos = listings.Select(l => new ListingDto
            {
                Id = l.ID,
                Title = l.Title ?? "",
                ShortDescription = l.ShortDescription,
                LogoURL = l.LogoURL,
                PlatformType = l.PlatformType,
                SupportedPlatforms = ParseJsonArray(l.SupportedPlatforms),
                PricingModel = l.PricingModel,
                Price = l.Price,
                Currency = l.Currency,
                IslamicComplianceStatus = l.IslamicComplianceStatus ?? "",
                Rating = new RatingDto
                {
                    Average = l.AverageRating ?? 0,
                    Count = l.ReviewCount ?? 0
                },
                Organization = l.OrganizationId != null ? new OrganizationListDto
                {
                    Id = l.OrganizationId,
                    Name = l.OrganizationName ?? "",
                    LogoURL = l.OrganizationLogoURL,
                    IsVerified = l.OrganizationIsVerified ?? false
                } : null,
                PrimaryCategory = l.PrimaryCategoryId != null ? new CategoryListDto
                {
                    Id = l.PrimaryCategoryId,
                    Name = l.PrimaryCategoryName ?? ""
                } : null,
                FeaturedLevel = l.FeaturedLevel ?? 0,
                ViewCount = l.ViewCount ?? 0,
                CreatedAt = l.CreatedAt,
                UpdatedAt = l.UpdatedAt
            }).ToList();

            return ApiResponse<List<ListingDto>>.SuccessResponse(listingDtos);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<ListingDto>>.ErrorResponse("Failed to get trending listings", ex.Message);
        }
    }

    public async Task<ApiResponse<RecommendationsDto>> GetRecommendationsAsync(Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get user's favorite categories and tags for recommendations
            var userInterestsQuery = @"
                SELECT DISTINCT c.ID as CategoryId, c.Name as CategoryName
                FROM UserFavorites uf
                INNER JOIN Listings l ON uf.ListingID = l.ID
                INNER JOIN ListingCategories lc ON l.ID = lc.ListingID
                INNER JOIN Categories c ON lc.CategoryID = c.ID
                WHERE uf.UserID = @UserId";

            var userInterests = await connection.ExecuteQuery<dynamic>(userInterestsQuery,
                new[] { (userId).ToSqlParam("@UserId") });

            var sections = new List<RecommendationSectionDto>();

            // Based on interests section
            if (userInterests.Any())
            {
                var categoryIds = userInterests.Select(ui => ui.CategoryId).ToList();
                var categoryIdsParam = string.Join(",", categoryIds.Select(id => $"'{id}'"));

                var interestBasedQuery = $@"
                    SELECT TOP 5
                        l.ID, l.Title, l.ShortDescription, l.LogoURL, l.PlatformType,
                        l.SupportedPlatforms, l.PricingModel, l.Price, l.Currency,
                        l.IslamicComplianceStatus, l.FeaturedLevel, l.ViewCount,
                        l.CreatedAt, l.UpdatedAt,
                        ISNULL(r.AverageRating, 0) as AverageRating,
                        ISNULL(r.ReviewCount, 0) as ReviewCount
                    FROM Listings l
                    LEFT JOIN (
                        SELECT ListingID, AVG(CAST(Rating AS FLOAT)) as AverageRating, COUNT(*) as ReviewCount
                        FROM Reviews WHERE Status = 'Approved' GROUP BY ListingID
                    ) r ON l.ID = r.ListingID
                    WHERE l.Status = 'Approved'
                    AND EXISTS (SELECT 1 FROM ListingCategories lc WHERE lc.ListingID = l.ID AND lc.CategoryID IN ({categoryIdsParam}))
                    AND NOT EXISTS (SELECT 1 FROM UserFavorites uf WHERE uf.ListingID = l.ID AND uf.UserID = @UserId)
                    ORDER BY ISNULL(r.AverageRating, 0) DESC, l.ViewCount DESC";

                var interestBasedListings = await connection.ExecuteQuery<dynamic>(interestBasedQuery,
                    new[] { (userId).ToSqlParam("@UserId") });

                if (interestBasedListings.Any())
                {
                    sections.Add(new RecommendationSectionDto
                    {
                        Title = "Based on your interests",
                        Listings = MapToListingDtos(interestBasedListings)
                    });
                }
            }

            // Popular listings section
            var popularQuery = @"
                SELECT TOP 5
                    l.ID, l.Title, l.ShortDescription, l.LogoURL, l.PlatformType,
                    l.SupportedPlatforms, l.PricingModel, l.Price, l.Currency,
                    l.IslamicComplianceStatus, l.FeaturedLevel, l.ViewCount,
                    l.CreatedAt, l.UpdatedAt,
                    ISNULL(r.AverageRating, 0) as AverageRating,
                    ISNULL(r.ReviewCount, 0) as ReviewCount
                FROM Listings l
                LEFT JOIN (
                    SELECT ListingID, AVG(CAST(Rating AS FLOAT)) as AverageRating, COUNT(*) as ReviewCount
                    FROM Reviews WHERE Status = 'Approved' GROUP BY ListingID
                ) r ON l.ID = r.ListingID
                WHERE l.Status = 'Approved'
                ORDER BY l.ViewCount DESC, ISNULL(r.AverageRating, 0) DESC";

            var popularListings = await connection.ExecuteQuery<dynamic>(popularQuery);

            sections.Add(new RecommendationSectionDto
            {
                Title = "Popular listings",
                Listings = MapToListingDtos(popularListings)
            });

            var recommendations = new RecommendationsDto { Sections = sections };
            return ApiResponse<RecommendationsDto>.SuccessResponse(recommendations);
        }
        catch (Exception ex)
        {
            return ApiResponse<RecommendationsDto>.ErrorResponse("Failed to get recommendations", ex.Message);
        }
    }

    public async Task<ApiResponse<ListingDetailDto>> GetListingByIdAsync(Guid id, Guid? userId = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get main listing details
            var listingQuery = @"
                SELECT
                    l.*,
                    ISNULL(r.AverageRating, 0) as AverageRating,
                    ISNULL(r.ReviewCount, 0) as ReviewCount,
                    ISNULL(r.IslamicComplianceAverage, 0) as IslamicComplianceAverage,
                    o.ID as OrganizationId,
                    o.Name as OrganizationName,
                    o.LogoURL as OrganizationLogoURL,
                    o.IsVerified as OrganizationIsVerified,
                    o.Website as OrganizationWebsite,
                    o.Email as OrganizationEmail
                FROM Listings l
                LEFT JOIN Organizations o ON l.OrganizationID = o.ID
                LEFT JOIN (
                    SELECT
                        ListingID,
                        AVG(CAST(Rating AS FLOAT)) as AverageRating,
                        COUNT(*) as ReviewCount,
                        AVG(CAST(ISNULL(IslamicComplianceRating, Rating) AS FLOAT)) as IslamicComplianceAverage
                    FROM Reviews
                    WHERE Status = 'Approved'
                    GROUP BY ListingID
                ) r ON l.ID = r.ListingID
                WHERE l.ID = @ListingId AND l.Status = 'Approved'";

            var listingResult = await connection.ExecuteQuery<dynamic>(listingQuery,
                new[] { (id).ToSqlParam("@ListingId") });

            var listing = listingResult.FirstOrDefault();
            if (listing == null)
            {
                return ApiResponse<ListingDetailDto>.ErrorResponse("Listing not found", "LISTING_NOT_FOUND");
            }

            // Get categories
            var categoriesQuery = @"
                SELECT c.ID, c.Name, lc.IsPrimary
                FROM ListingCategories lc
                INNER JOIN Categories c ON lc.CategoryID = c.ID
                WHERE lc.ListingID = @ListingId";

            var categories = await connection.ExecuteQuery<dynamic>(categoriesQuery,
                new[] { (id).ToSqlParam("@ListingId") });

            // Get tags
            var tagsQuery = @"
                SELECT t.ID, t.Name
                FROM ListingTags lt
                INNER JOIN Tags t ON lt.TagID = t.ID
                WHERE lt.ListingID = @ListingId";

            var tags = await connection.ExecuteQuery<dynamic>(tagsQuery,
                new[] { (id).ToSqlParam("@ListingId") });

            // Get media
            var mediaQuery = @"
                SELECT ID, MediaType, MediaURL, ThumbnailURL, SortOrder
                FROM ListingMedia
                WHERE ListingID = @ListingId
                ORDER BY SortOrder";

            var media = await connection.ExecuteQuery<dynamic>(mediaQuery,
                new[] { (id).ToSqlParam("@ListingId") });

            // Check if favorited by user
            bool isFavorited = false;
            if (userId.HasValue)
            {
                var favoriteQuery = @"
                    SELECT COUNT(1)
                    FROM UserFavorites
                    WHERE UserID = @UserId AND ListingID = @ListingId";

                var favoriteResult = await connection.ExecuteQuery<int>(favoriteQuery,
                    new[] {
                        (userId.Value).ToSqlParam("@UserId"),
                        (id).ToSqlParam("@ListingId")
                    });
                isFavorited = favoriteResult.First() > 0;
            }

            var listingDetail = new ListingDetailDto
            {
                Id = listing.ID,
                Title = listing.Title ?? "",
                ShortDescription = listing.ShortDescription,
                FullDescription = listing.FullDescription,
                LogoURL = listing.LogoURL,
                Website = listing.Website,
                PlatformType = listing.PlatformType,
                SupportedPlatforms = ParseJsonArray(listing.SupportedPlatforms),
                AppStoreURL = listing.AppStoreURL,
                PlayStoreURL = listing.PlayStoreURL,
                WebsiteURL = listing.WebsiteURL,
                PricingModel = listing.PricingModel,
                Price = listing.Price,
                Currency = listing.Currency,
                IslamicComplianceStatus = listing.IslamicComplianceStatus ?? "",
                ComplianceNotes = listing.ComplianceNotes,
                Rating = new RatingDto
                {
                    Average = listing.AverageRating ?? 0,
                    Count = listing.ReviewCount ?? 0,
                    IslamicComplianceAverage = listing.IslamicComplianceAverage ?? 0
                },
                Organization = listing.OrganizationId != null ? new OrganizationListDto
                {
                    Id = listing.OrganizationId,
                    Name = listing.OrganizationName ?? "",
                    LogoURL = listing.OrganizationLogoURL,
                    IsVerified = listing.OrganizationIsVerified ?? false
                } : null,
                Categories = categories.Select(c => new ListingCategoryDto
                {
                    Id = c.ID,
                    Name = c.Name ?? "",
                    IsPrimary = c.IsPrimary ?? false
                }).ToList(),
                Tags = tags.Select(t => new TagListDto
                {
                    Id = t.ID,
                    Name = t.Name ?? ""
                }).ToList(),
                Media = media.Select(m => new ListingMediaDto
                {
                    Id = m.ID,
                    MediaType = m.MediaType ?? "",
                    MediaURL = m.MediaURL ?? "",
                    ThumbnailURL = m.ThumbnailURL,
                    SortOrder = m.SortOrder ?? 0
                }).ToList(),
                PrimaryLanguage = listing.PrimaryLanguage,
                SupportedLanguages = ParseJsonArray(listing.SupportedLanguages),
                MetaTitle = listing.MetaTitle,
                MetaDescription = listing.MetaDescription,
                FeaturedLevel = listing.FeaturedLevel ?? 0,
                ViewCount = listing.ViewCount ?? 0,
                IsFavorited = isFavorited,
                CreatedAt = listing.CreatedAt,
                UpdatedAt = listing.UpdatedAt,
                RelatedListings = new List<ListingDto>() // TODO: Implement related listings logic
            };

            return ApiResponse<ListingDetailDto>.SuccessResponse(listingDetail);
        }
        catch (Exception ex)
        {
            return ApiResponse<ListingDetailDto>.ErrorResponse("Failed to get listing details", ex.Message);
        }
    }

    private List<ListingDto> MapToListingDtos(IEnumerable<dynamic> listings)
    {
        return listings.Select(l => new ListingDto
        {
            Id = l.ID,
            Title = l.Title ?? "",
            ShortDescription = l.ShortDescription,
            LogoURL = l.LogoURL,
            PlatformType = l.PlatformType,
            SupportedPlatforms = ParseJsonArray(l.SupportedPlatforms),
            PricingModel = l.PricingModel,
            Price = l.Price,
            Currency = l.Currency,
            IslamicComplianceStatus = l.IslamicComplianceStatus ?? "",
            Rating = new RatingDto
            {
                Average = l.AverageRating ?? 0,
                Count = l.ReviewCount ?? 0
            },
            FeaturedLevel = l.FeaturedLevel ?? 0,
            ViewCount = l.ViewCount ?? 0,
            CreatedAt = l.CreatedAt,
            UpdatedAt = l.UpdatedAt
        }).ToList();
    }

    public async Task<ApiResponse<ListingDto>> CreateListingAsync(CreateListingDto createDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var listingId = Guid.NewGuid();
            var slugUrl = GenerateSlugUrl(createDto.Title);

            // Insert main listing
            var insertListingQuery = @"
                INSERT INTO Listings (
                    ID, Title, ShortDescription, FullDescription, LogoURL, Website,
                    PlatformType, SupportedPlatforms, AppStoreURL, PlayStoreURL, WebsiteURL,
                    PricingModel, Price, Currency, OrganizationID, SubmittedBy, Status,
                    SlugURL, MetaTitle, MetaDescription, PrimaryLanguage, SupportedLanguages,
                    CreatedAt, UpdatedAt
                ) VALUES (
                    @ID, @Title, @ShortDescription, @FullDescription, @LogoURL, @Website,
                    @PlatformType, @SupportedPlatforms, @AppStoreURL, @PlayStoreURL, @WebsiteURL,
                    @PricingModel, @Price, @Currency, @OrganizationID, @SubmittedBy, @Status,
                    @SlugURL, @MetaTitle, @MetaDescription, @PrimaryLanguage, @SupportedLanguages,
                    @CreatedAt, @UpdatedAt
                )";

            var parameters = new List<SqlParameter>
            {
                (listingId).ToSqlParam("@ID"),
                (createDto.Title).ToSqlParam("@Title"),
                (createDto.ShortDescription).ToSqlParam("@ShortDescription"),
                (createDto.FullDescription).ToSqlParam("@FullDescription"),
                (createDto.LogoURL).ToSqlParam("@LogoURL"),
                (createDto.Website).ToSqlParam("@Website"),
                (createDto.PlatformType).ToSqlParam("@PlatformType"),
                (JsonSerializer.Serialize(createDto.SupportedPlatforms)).ToSqlParam("@SupportedPlatforms"),
                (createDto.AppStoreURL).ToSqlParam("@AppStoreURL"),
                (createDto.PlayStoreURL).ToSqlParam("@PlayStoreURL"),
                (createDto.WebsiteURL).ToSqlParam("@WebsiteURL"),
                (createDto.PricingModel).ToSqlParam("@PricingModel"),
                (createDto.Price).ToSqlParam("@Price"),
                (createDto.Currency ?? "USD").ToSqlParam("@Currency"),
                (createDto.OrganizationId == Guid.Empty ? (object)DBNull.Value : createDto.OrganizationId).ToSqlParam("@OrganizationID"),
                (userId).ToSqlParam("@SubmittedBy"),
                ("Pending").ToSqlParam("@Status"),
                (slugUrl).ToSqlParam("@SlugURL"),
                (createDto.MetaTitle).ToSqlParam("@MetaTitle"),
                (createDto.MetaDescription).ToSqlParam("@MetaDescription"),
                (createDto.PrimaryLanguage ?? "EN").ToSqlParam("@PrimaryLanguage"),
                (JsonSerializer.Serialize(createDto.SupportedLanguages)).ToSqlParam("@SupportedLanguages"),
                (DateTime.UtcNow).ToSqlParam("@CreatedAt"),
                (DateTime.UtcNow).ToSqlParam("@UpdatedAt")
            };

            await connection.ExecuteNonQueryText(insertListingQuery, parameters.ToArray());

            // Insert categories
            foreach (var categoryId in createDto.CategoryIds)
            {
                var insertCategoryQuery = @"
                    INSERT INTO ListingCategories (ID, ListingID, CategoryID, IsPrimary, CreatedAt)
                    VALUES (@ID, @ListingID, @CategoryID, @IsPrimary, @CreatedAt)";

                var categoryParams = new[]
                {
                    (Guid.NewGuid()).ToSqlParam("@ID"),
                    (listingId).ToSqlParam("@ListingID"),
                    (categoryId).ToSqlParam("@CategoryID"),
                    (categoryId == createDto.PrimaryCategoryId).ToSqlParam("@IsPrimary"),
                    (DateTime.UtcNow).ToSqlParam("@CreatedAt")
                };

                await connection.ExecuteNonQueryText(insertCategoryQuery, categoryParams);
            }

            // Insert tags
            foreach (var tagId in createDto.TagIds)
            {
                var insertTagQuery = @"
                    INSERT INTO ListingTags (ID, ListingID, TagID, CreatedAt)
                    VALUES (@ID, @ListingID, @TagID, @CreatedAt)";

                var tagParams = new[]
                {
                    (Guid.NewGuid()).ToSqlParam("@ID"),
                    (listingId).ToSqlParam("@ListingID"),
                    (tagId).ToSqlParam("@TagID"),
                    (DateTime.UtcNow).ToSqlParam("@CreatedAt")
                };

                await connection.ExecuteNonQueryText(insertTagQuery, tagParams);
            }

            // Insert media
            foreach (var media in createDto.Media)
            {
                var insertMediaQuery = @"
                    INSERT INTO ListingMedia (ID, ListingID, MediaType, MediaURL, ThumbnailURL, SortOrder, CreatedAt)
                    VALUES (@ID, @ListingID, @MediaType, @MediaURL, @ThumbnailURL, @SortOrder, @CreatedAt)";

                var mediaParams = new[]
                {
                    (Guid.NewGuid()).ToSqlParam("@ID"),
                    (listingId).ToSqlParam("@ListingID"),
                    (media.MediaType).ToSqlParam("@MediaType"),
                    (media.MediaURL).ToSqlParam("@MediaURL"),
                    (media.ThumbnailURL).ToSqlParam("@ThumbnailURL"),
                    (media.SortOrder).ToSqlParam("@SortOrder"),
                    (DateTime.UtcNow).ToSqlParam("@CreatedAt")
                };

                await connection.ExecuteNonQueryText(insertMediaQuery, mediaParams);
            }

            // Return a simple success response with the listing ID
            var resultDto = new ListingDto
            {
                Id = listingId,
                Title = createDto.Title,
                ShortDescription = createDto.ShortDescription,
                LogoURL = createDto.LogoURL,
                PlatformType = createDto.PlatformType,
                SupportedPlatforms = createDto.SupportedPlatforms,
                PricingModel = createDto.PricingModel,
                Price = createDto.Price,
                Currency = createDto.Currency ?? "USD",
                IslamicComplianceStatus = "Under Review",
                Rating = new RatingDto(),
                FeaturedLevel = 0,
                ViewCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            return ApiResponse<ListingDto>.SuccessResponse(resultDto);
        }
        catch (Exception ex)
        {
            return ApiResponse<ListingDto>.ErrorResponse("Failed to create listing", ex.Message);
        }
    }

    public async Task<ApiResponse<ListingDto>> UpdateListingAsync(Guid id, UpdateListingDto updateDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user has permission to update this listing
            var permissionQuery = @"
                SELECT COUNT(1)
                FROM Listings l
                LEFT JOIN OrganizationUsers ou ON l.OrganizationID = ou.OrganizationID
                WHERE l.ID = @ListingId
                AND (l.SubmittedBy = @UserId OR (ou.UserID = @UserId AND ou.Role IN ('Owner', 'Admin')))";

            var hasPermissionResult = await connection.ExecuteQuery<int>(permissionQuery,
                new[] {
                    (id).ToSqlParam("@ListingId"),
                    (userId).ToSqlParam("@UserId")
                });

            if (hasPermissionResult.First() == 0)
            {
                return ApiResponse<ListingDto>.ErrorResponse("Unauthorized to update this listing", "UNAUTHORIZED");
            }

            // Build dynamic update query
            var updateFields = new List<string>();
            var parameters = new List<SqlParameter>();

            if (!string.IsNullOrWhiteSpace(updateDto.Title))
            {
                updateFields.Add("Title = @Title");
                parameters.Add((updateDto.Title).ToSqlParam("@Title"));
            }

            if (updateDto.ShortDescription != null)
            {
                updateFields.Add("ShortDescription = @ShortDescription");
                parameters.Add((updateDto.ShortDescription).ToSqlParam("@ShortDescription"));
            }

            if (updateDto.FullDescription != null)
            {
                updateFields.Add("FullDescription = @FullDescription");
                parameters.Add((updateDto.FullDescription).ToSqlParam("@FullDescription"));
            }

            if (updateFields.Any())
            {
                updateFields.Add("UpdatedAt = @UpdatedAt");
                parameters.Add((DateTime.UtcNow).ToSqlParam("@UpdatedAt"));
                parameters.Add((id).ToSqlParam("@ListingId"));

                var updateQuery = $@"
                    UPDATE Listings
                    SET {string.Join(", ", updateFields)}
                    WHERE ID = @ListingId";

                await connection.ExecuteNonQueryText(updateQuery, parameters.ToArray());
            }

            // Return a simple success response
            var resultDto = new ListingDto
            {
                Id = id,
                Title = updateDto.Title ?? "",
                ShortDescription = updateDto.ShortDescription,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            return ApiResponse<ListingDto>.SuccessResponse(resultDto);
        }
        catch (Exception ex)
        {
            return ApiResponse<ListingDto>.ErrorResponse("Failed to update listing", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> DeleteListingAsync(Guid id, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user has permission to delete this listing (organization owners only)
            var permissionQuery = @"
                SELECT COUNT(1)
                FROM Listings l
                LEFT JOIN OrganizationUsers ou ON l.OrganizationID = ou.OrganizationID
                WHERE l.ID = @ListingId
                AND ou.UserID = @UserId AND ou.Role = 'Owner'";

            var hasPermissionResult = await connection.ExecuteQuery<int>(permissionQuery,
                new[] {
                    (id).ToSqlParam("@ListingId"),
                    (userId).ToSqlParam("@UserId")
                });

            if (hasPermissionResult.First() == 0)
            {
                return ApiResponse<bool>.ErrorResponse("Unauthorized to delete this listing", "UNAUTHORIZED");
            }

            // Delete the listing (cascade will handle related records)
            var deleteQuery = "DELETE FROM Listings WHERE ID = @ListingId";
            await connection.ExecuteNonQueryText(deleteQuery, new[] { (id).ToSqlParam("@ListingId") });

            return ApiResponse<bool>.SuccessResponse(true);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to delete listing", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> TrackViewAsync(Guid id, TrackViewDto trackDto)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Update view count
            var updateViewQuery = "UPDATE Listings SET ViewCount = ViewCount + 1 WHERE ID = @ListingId";
            await connection.ExecuteNonQueryText(updateViewQuery, new[] { (id).ToSqlParam("@ListingId") });

            // TODO: Add detailed analytics tracking if needed
            // This could include storing view details in a separate analytics table

            return ApiResponse<bool>.SuccessResponse(true);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to track view", ex.Message);
        }
    }

    public async Task<ApiResponse<ListingAnalyticsDto>> GetListingAnalyticsAsync(
        Guid id, Guid userId, DateTime? startDate = null, DateTime? endDate = null,
        string granularity = "day")
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user has permission to view analytics
            var permissionQuery = @"
                SELECT COUNT(1)
                FROM Listings l
                LEFT JOIN OrganizationUsers ou ON l.OrganizationID = ou.OrganizationID
                WHERE l.ID = @ListingId
                AND (l.SubmittedBy = @UserId OR (ou.UserID = @UserId AND ou.Role IN ('Owner', 'Admin')))";

            var hasPermissionResult = await connection.ExecuteQuery<int>(permissionQuery,
                new[] {
                    (id).ToSqlParam("@ListingId"),
                    (userId).ToSqlParam("@UserId")
                });

            if (hasPermissionResult.First() == 0)
            {
                return ApiResponse<ListingAnalyticsDto>.ErrorResponse("Unauthorized to view analytics", "UNAUTHORIZED");
            }

            // Get summary data
            var summaryQuery = @"
                SELECT
                    l.ViewCount as TotalViews,
                    COUNT(r.ID) as TotalReviews,
                    ISNULL(AVG(CAST(r.Rating AS FLOAT)), 0) as AverageRating,
                    COUNT(uf.ID) as FavoriteCount
                FROM Listings l
                LEFT JOIN Reviews r ON l.ID = r.ListingID AND r.Status = 'Approved'
                LEFT JOIN UserFavorites uf ON l.ID = uf.ListingID
                WHERE l.ID = @ListingId
                GROUP BY l.ViewCount";

            var summaryResult = await connection.ExecuteQuery<dynamic>(summaryQuery,
                new[] { (id).ToSqlParam("@ListingId") });

            var summary = summaryResult.FirstOrDefault();
            var analyticsDto = new ListingAnalyticsDto
            {
                Summary = new ListingAnalyticsSummaryDto
                {
                    TotalViews = summary?.TotalViews ?? 0,
                    TotalReviews = summary?.TotalReviews ?? 0,
                    AverageRating = summary?.AverageRating ?? 0,
                    FavoriteCount = summary?.FavoriteCount ?? 0
                },
                ViewsOverTime = new List<AnalyticsDataPointDto>(),
                ReviewsOverTime = new List<AnalyticsDataPointDto>(),
                FavoritesOverTime = new List<AnalyticsDataPointDto>()
            };

            return ApiResponse<ListingAnalyticsDto>.SuccessResponse(analyticsDto);
        }
        catch (Exception ex)
        {
            return ApiResponse<ListingAnalyticsDto>.ErrorResponse("Failed to get analytics", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> AddToFavoritesAsync(Guid listingId, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if already favorited
            var existsQuery = @"
                SELECT COUNT(1)
                FROM UserFavorites
                WHERE UserID = @UserId AND ListingID = @ListingId";

            var existsResult = await connection.ExecuteQuery<int>(existsQuery,
                new[] {
                    (userId).ToSqlParam("@UserId"),
                    (listingId).ToSqlParam("@ListingId")
                });

            if (existsResult.First() > 0)
            {
                return ApiResponse<bool>.ErrorResponse("Listing already in favorites", "ALREADY_FAVORITED");
            }

            // Add to favorites
            var insertQuery = @"
                INSERT INTO UserFavorites (ID, UserID, ListingID, CreatedAt)
                VALUES (@ID, @UserId, @ListingId, @CreatedAt)";

            var parameters = new[]
            {
                (Guid.NewGuid()).ToSqlParam("@ID"),
                (userId).ToSqlParam("@UserId"),
                (listingId).ToSqlParam("@ListingId"),
                (DateTime.UtcNow).ToSqlParam("@CreatedAt")
            };

            await connection.ExecuteNonQueryText(insertQuery, parameters);

            return ApiResponse<bool>.SuccessResponse(true);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to add to favorites", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> RemoveFromFavoritesAsync(Guid listingId, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var deleteQuery = @"
                DELETE FROM UserFavorites
                WHERE UserID = @UserId AND ListingID = @ListingId";

            await connection.ExecuteNonQueryText(deleteQuery,
                new[] {
                    (userId).ToSqlParam("@UserId"),
                    (listingId).ToSqlParam("@ListingId")
                });

            return ApiResponse<bool>.SuccessResponse(true);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to remove from favorites", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> CheckIsFavoritedAsync(Guid listingId, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var query = @"
                SELECT COUNT(1)
                FROM UserFavorites
                WHERE UserID = @UserId AND ListingID = @ListingId";

            var result = await connection.ExecuteQuery<int>(query,
                new[] {
                    (userId).ToSqlParam("@UserId"),
                    (listingId).ToSqlParam("@ListingId")
                });

            var isFavorited = result.First() > 0;
            return ApiResponse<bool>.SuccessResponse(isFavorited);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to check favorite status", ex.Message);
        }
    }

    public async Task<ApiResponse<PaginatedResult<ListingDto>>> GetUserFavoritesAsync(
        Guid userId, int page = 1, int limit = 20, string? sortBy = null, string? search = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var offset = (page - 1) * limit;
            var whereConditions = new List<string> { "l.Status = 'Approved'" };
            var parameters = new List<SqlParameter> { (userId).ToSqlParam("@UserId") };

            if (!string.IsNullOrWhiteSpace(search))
            {
                whereConditions.Add("(l.Title LIKE @Search OR l.ShortDescription LIKE @Search)");
                parameters.Add(($"%{search}%").ToSqlParam("@Search"));
            }

            var whereClause = whereConditions.Count > 0 ? $"AND {string.Join(" AND ", whereConditions)}" : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(1)
                FROM UserFavorites uf
                INNER JOIN Listings l ON uf.ListingID = l.ID
                WHERE uf.UserID = @UserId {whereClause}";

            var totalCountResult = await connection.ExecuteQuery<int>(countQuery, parameters.ToArray());
            var totalCount = totalCountResult.First();

            // Build ORDER BY clause
            var orderBy = sortBy?.ToLower() switch
            {
                "newest" => "uf.CreatedAt DESC",
                "oldest" => "uf.CreatedAt ASC",
                "title" => "l.Title ASC",
                _ => "uf.CreatedAt DESC"
            };

            // Get favorites
            var query = $@"
                SELECT
                    l.ID,
                    l.Title,
                    l.ShortDescription,
                    l.LogoURL,
                    l.PlatformType,
                    l.SupportedPlatforms,
                    l.PricingModel,
                    l.Price,
                    l.Currency,
                    l.IslamicComplianceStatus,
                    l.FeaturedLevel,
                    l.ViewCount,
                    l.CreatedAt,
                    l.UpdatedAt,
                    ISNULL(r.AverageRating, 0) as AverageRating,
                    ISNULL(r.ReviewCount, 0) as ReviewCount,
                    o.ID as OrganizationId,
                    o.Name as OrganizationName,
                    o.LogoURL as OrganizationLogoURL,
                    o.IsVerified as OrganizationIsVerified,
                    pc.ID as PrimaryCategoryId,
                    pc.Name as PrimaryCategoryName
                FROM UserFavorites uf
                INNER JOIN Listings l ON uf.ListingID = l.ID
                LEFT JOIN Organizations o ON l.OrganizationID = o.ID
                LEFT JOIN (
                    SELECT ListingID, AVG(CAST(Rating AS FLOAT)) as AverageRating, COUNT(*) as ReviewCount
                    FROM Reviews
                    WHERE Status = 'Approved'
                    GROUP BY ListingID
                ) r ON l.ID = r.ListingID
                LEFT JOIN ListingCategories lc ON l.ID = lc.ListingID AND lc.IsPrimary = 1
                LEFT JOIN Categories pc ON lc.CategoryID = pc.ID
                WHERE uf.UserID = @UserId {whereClause}
                ORDER BY {orderBy}
                OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY";

            parameters.Add((offset).ToSqlParam("@Offset"));
            parameters.Add((limit).ToSqlParam("@Limit"));

            var favorites = await connection.ExecuteQuery<dynamic>(query, parameters.ToArray());

            var listingDtos = favorites.Select(l => new ListingDto
            {
                Id = l.ID,
                Title = l.Title ?? "",
                ShortDescription = l.ShortDescription,
                LogoURL = l.LogoURL,
                PlatformType = l.PlatformType,
                SupportedPlatforms = ParseJsonArray(l.SupportedPlatforms),
                PricingModel = l.PricingModel,
                Price = l.Price,
                Currency = l.Currency,
                IslamicComplianceStatus = l.IslamicComplianceStatus ?? "",
                Rating = new RatingDto
                {
                    Average = l.AverageRating ?? 0,
                    Count = l.ReviewCount ?? 0
                },
                Organization = l.OrganizationId != null ? new OrganizationListDto
                {
                    Id = l.OrganizationId,
                    Name = l.OrganizationName ?? "",
                    LogoURL = l.OrganizationLogoURL,
                    IsVerified = l.OrganizationIsVerified ?? false
                } : null,
                PrimaryCategory = l.PrimaryCategoryId != null ? new CategoryListDto
                {
                    Id = l.PrimaryCategoryId,
                    Name = l.PrimaryCategoryName ?? ""
                } : null,
                FeaturedLevel = l.FeaturedLevel ?? 0,
                ViewCount = l.ViewCount ?? 0,
                CreatedAt = l.CreatedAt,
                UpdatedAt = l.UpdatedAt
            }).ToList();

            var result = new PaginatedResult<ListingDto>
            {
                Items = listingDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<ListingDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<ListingDto>>.ErrorResponse("Failed to get user favorites", ex.Message);
        }
    }

    private string GenerateSlugUrl(string title)
    {
        var slug = title.ToLowerInvariant()
            .Replace(" ", "-")
            .Replace("&", "and")
            .Replace("'", "")
            .Replace("\"", "");

        // Remove special characters
        slug = System.Text.RegularExpressions.Regex.Replace(slug, @"[^a-z0-9\-]", "");

        // Remove multiple dashes
        slug = System.Text.RegularExpressions.Regex.Replace(slug, @"-+", "-");

        // Remove leading/trailing dashes
        slug = slug.Trim('-');

        return $"{slug}-{Guid.NewGuid().ToString("N")[..8]}";
    }

    private List<string> ParseJsonArray(string? jsonArray)
    {
        if (string.IsNullOrWhiteSpace(jsonArray))
            return new List<string>();

        try
        {
            return JsonSerializer.Deserialize<List<string>>(jsonArray) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }
}
