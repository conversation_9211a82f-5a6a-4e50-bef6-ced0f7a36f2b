using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Content;
using MuslimDirectory.API.Models.Common;
using XGENO.DBHelpers.Core;
using System.Data.SqlClient;
using System.Text;

namespace MuslimDirectory.API.Services.Implementations;

public class ContentService(IConfiguration configuration) : IContentService
{
    private readonly string _connectionString = configuration.GetConnectionString("DefaultConnection") 
        ?? throw new ArgumentNullException("Connection string not found");

    public async Task<ApiResponse<PaginatedResult<ContentPageDto>>> GetContentPagesAsync(
        string? search = null, bool? isPublished = null, int page = 1, int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            // Build WHERE clause
            if (!string.IsNullOrEmpty(search))
            {
                whereConditions.Add("(cp.Title LIKE @Search OR cp.Content LIKE @Search OR cp.Slug LIKE @Search)");
                parameters.Add($"%{search}%".ToSqlParam("@Search"));
            }

            if (isPublished.HasValue)
            {
                whereConditions.Add("cp.IsPublished = @IsPublished");
                parameters.Add(isPublished.Value.ToSqlParam("@IsPublished"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*) 
                FROM ContentPages cp
                {whereClause}";

            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get paginated data
            var offset = (page - 1) * limit;
            var dataQuery = $@"
                SELECT cp.ID, cp.Slug, cp.Title, cp.Content, cp.MetaTitle, cp.MetaDescription, 
                       cp.MetaKeywords, cp.IsPublished, cp.SortOrder, cp.CreatedAt, cp.UpdatedAt,
                       cu.FirstName + ' ' + cu.LastName as CreatedBy,
                       uu.FirstName + ' ' + uu.LastName as UpdatedBy
                FROM ContentPages cp
                LEFT JOIN Users cu ON cp.CreatedBy = cu.ID
                LEFT JOIN Users uu ON cp.UpdatedBy = uu.ID
                {whereClause}
                ORDER BY cp.SortOrder ASC, cp.CreatedAt DESC
                OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY";

            var pages = await connection.ExecuteQuery<dynamic>(dataQuery, parameters.ToArray());

            var pageDtos = pages.Select(p => new ContentPageDto
            {
                Id = p.ID,
                Slug = p.Slug?.ToString() ?? "",
                Title = p.Title?.ToString() ?? "",
                Content = p.Content?.ToString() ?? "",
                MetaTitle = p.MetaTitle?.ToString(),
                MetaDescription = p.MetaDescription?.ToString(),
                MetaKeywords = p.MetaKeywords?.ToString(),
                IsPublished = p.IsPublished != null && (bool)p.IsPublished,
                SortOrder = p.SortOrder != null ? (int)p.SortOrder : 0,
                CreatedAt = p.CreatedAt != null ? (DateTime)p.CreatedAt : DateTime.MinValue,
                UpdatedAt = p.UpdatedAt as DateTime?,
                CreatedBy = p.CreatedBy?.ToString(),
                UpdatedBy = p.UpdatedBy?.ToString()
            }).ToList();

            var result = new PaginatedResult<ContentPageDto>
            {
                Items = pageDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<ContentPageDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<ContentPageDto>>.ErrorResponse(
                "GET_CONTENT_PAGES_FAILED", "Failed to get content pages", ex.Message);
        }
    }

    public async Task<ApiResponse<ContentPageDto>> GetContentPageBySlugAsync(string slug)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var query = @"
                SELECT cp.ID, cp.Slug, cp.Title, cp.Content, cp.MetaTitle, cp.MetaDescription, 
                       cp.MetaKeywords, cp.IsPublished, cp.SortOrder, cp.CreatedAt, cp.UpdatedAt,
                       cu.FirstName + ' ' + cu.LastName as CreatedBy,
                       uu.FirstName + ' ' + uu.LastName as UpdatedBy
                FROM ContentPages cp
                LEFT JOIN Users cu ON cp.CreatedBy = cu.ID
                LEFT JOIN Users uu ON cp.UpdatedBy = uu.ID
                WHERE cp.Slug = @Slug AND cp.IsPublished = 1";

            var pages = await connection.ExecuteQuery<dynamic>(query, slug.ToSqlParam("@Slug"));

            if (!pages.Any())
            {
                return ApiResponse<ContentPageDto>.ErrorResponse("CONTENT_PAGE_NOT_FOUND", "Content page not found");
            }

            var page = pages.First();
            var pageDto = new ContentPageDto
            {
                Id = page.ID,
                Slug = page.Slug?.ToString() ?? "",
                Title = page.Title?.ToString() ?? "",
                Content = page.Content?.ToString() ?? "",
                MetaTitle = page.MetaTitle?.ToString(),
                MetaDescription = page.MetaDescription?.ToString(),
                MetaKeywords = page.MetaKeywords?.ToString(),
                IsPublished = page.IsPublished != null && (bool)page.IsPublished,
                SortOrder = page.SortOrder != null ? (int)page.SortOrder : 0,
                CreatedAt = page.CreatedAt != null ? (DateTime)page.CreatedAt : DateTime.MinValue,
                UpdatedAt = page.UpdatedAt as DateTime?,
                CreatedBy = page.CreatedBy?.ToString(),
                UpdatedBy = page.UpdatedBy?.ToString()
            };

            return ApiResponse<ContentPageDto>.SuccessResponse(pageDto);
        }
        catch (Exception ex)
        {
            return ApiResponse<ContentPageDto>.ErrorResponse(
                "GET_CONTENT_PAGE_FAILED", "Failed to get content page", ex.Message);
        }
    }

    public async Task<ApiResponse<ContentPageDto>> CreateContentPageAsync(CreateContentPageDto createDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if slug already exists
            var slugExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM ContentPages WHERE Slug = @Slug",
                createDto.Slug.ToSqlParam("@Slug"));

            if (slugExists.Any() && slugExists.First() > 0)
            {
                return ApiResponse<ContentPageDto>.ErrorResponse("SLUG_ALREADY_EXISTS", "A page with this slug already exists");
            }

            var id = Guid.NewGuid();
            var now = DateTime.UtcNow;

            var parameters = new List<SqlParameter>
            {
                id.ToSqlParam("@Id"),
                createDto.Slug.ToSqlParam("@Slug"),
                createDto.Title.ToSqlParam("@Title"),
                createDto.Content.ToSqlParam("@Content"),
                createDto.IsPublished.ToSqlParam("@IsPublished"),
                createDto.SortOrder.ToSqlParam("@SortOrder"),
                now.ToSqlParam("@CreatedAt"),
                userId.ToSqlParam("@CreatedBy")
            };

            var insertQuery = new StringBuilder(@"
                INSERT INTO ContentPages (ID, Slug, Title, Content, IsPublished, SortOrder, CreatedAt, CreatedBy");

            var valuesQuery = new StringBuilder(@"
                VALUES (@Id, @Slug, @Title, @Content, @IsPublished, @SortOrder, @CreatedAt, @CreatedBy");

            if (!string.IsNullOrEmpty(createDto.MetaTitle))
            {
                insertQuery.Append(", MetaTitle");
                valuesQuery.Append(", @MetaTitle");
                parameters.Add(createDto.MetaTitle.ToSqlParam("@MetaTitle"));
            }

            if (!string.IsNullOrEmpty(createDto.MetaDescription))
            {
                insertQuery.Append(", MetaDescription");
                valuesQuery.Append(", @MetaDescription");
                parameters.Add(createDto.MetaDescription.ToSqlParam("@MetaDescription"));
            }

            if (!string.IsNullOrEmpty(createDto.MetaKeywords))
            {
                insertQuery.Append(", MetaKeywords");
                valuesQuery.Append(", @MetaKeywords");
                parameters.Add(createDto.MetaKeywords.ToSqlParam("@MetaKeywords"));
            }

            insertQuery.Append(") ");
            valuesQuery.Append(")");

            await connection.ExecuteNonQueryText(insertQuery.ToString() + valuesQuery.ToString(), parameters.ToArray());

            // Get the created page
            var createdPageResult = await GetContentPageByIdAsync(connection, id);
            if (createdPageResult == null)
            {
                return ApiResponse<ContentPageDto>.ErrorResponse("CREATE_FAILED", "Failed to retrieve created content page");
            }

            return ApiResponse<ContentPageDto>.SuccessResponse(createdPageResult, "Content page created successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<ContentPageDto>.ErrorResponse(
                "CREATE_CONTENT_PAGE_FAILED", "Failed to create content page", ex.Message);
        }
    }

    public async Task<ApiResponse<ContentPageDto>> UpdateContentPageAsync(Guid id, UpdateContentPageDto updateDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if page exists
            var pageExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM ContentPages WHERE ID = @Id",
                id.ToSqlParam("@Id"));

            if (!pageExists.Any() || pageExists.First() == 0)
            {
                return ApiResponse<ContentPageDto>.ErrorResponse("CONTENT_PAGE_NOT_FOUND", "Content page not found");
            }

            // Check if slug already exists (if updating slug)
            if (!string.IsNullOrEmpty(updateDto.Slug))
            {
                var slugExists = await connection.ExecuteQuery<int>(@"
                    SELECT COUNT(*) FROM ContentPages WHERE Slug = @Slug AND ID != @Id",
                    updateDto.Slug.ToSqlParam("@Slug"), id.ToSqlParam("@Id"));

                if (slugExists.Any() && slugExists.First() > 0)
                {
                    return ApiResponse<ContentPageDto>.ErrorResponse("SLUG_ALREADY_EXISTS", "A page with this slug already exists");
                }
            }

            var updateFields = new List<string>();
            var parameters = new List<SqlParameter>
            {
                id.ToSqlParam("@Id"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"),
                userId.ToSqlParam("@UpdatedBy")
            };

            if (!string.IsNullOrEmpty(updateDto.Slug))
            {
                updateFields.Add("Slug = @Slug");
                parameters.Add(updateDto.Slug.ToSqlParam("@Slug"));
            }

            if (!string.IsNullOrEmpty(updateDto.Title))
            {
                updateFields.Add("Title = @Title");
                parameters.Add(updateDto.Title.ToSqlParam("@Title"));
            }

            if (!string.IsNullOrEmpty(updateDto.Content))
            {
                updateFields.Add("Content = @Content");
                parameters.Add(updateDto.Content.ToSqlParam("@Content"));
            }

            if (updateDto.MetaTitle != null)
            {
                updateFields.Add("MetaTitle = @MetaTitle");
                parameters.Add((updateDto.MetaTitle ?? "").ToSqlParam("@MetaTitle"));
            }

            if (updateDto.MetaDescription != null)
            {
                updateFields.Add("MetaDescription = @MetaDescription");
                parameters.Add((updateDto.MetaDescription ?? "").ToSqlParam("@MetaDescription"));
            }

            if (updateDto.MetaKeywords != null)
            {
                updateFields.Add("MetaKeywords = @MetaKeywords");
                parameters.Add((updateDto.MetaKeywords ?? "").ToSqlParam("@MetaKeywords"));
            }

            if (updateDto.IsPublished.HasValue)
            {
                updateFields.Add("IsPublished = @IsPublished");
                parameters.Add(updateDto.IsPublished.Value.ToSqlParam("@IsPublished"));
            }

            if (updateDto.SortOrder.HasValue)
            {
                updateFields.Add("SortOrder = @SortOrder");
                parameters.Add(updateDto.SortOrder.Value.ToSqlParam("@SortOrder"));
            }

            if (updateFields.Count == 0)
            {
                return ApiResponse<ContentPageDto>.ErrorResponse("NO_UPDATES", "No fields to update");
            }

            updateFields.Add("UpdatedAt = @UpdatedAt");
            updateFields.Add("UpdatedBy = @UpdatedBy");

            var updateQuery = $@"
                UPDATE ContentPages
                SET {string.Join(", ", updateFields)}
                WHERE ID = @Id";

            await connection.ExecuteNonQueryText(updateQuery, parameters.ToArray());

            // Get the updated page
            var updatedPageResult = await GetContentPageByIdAsync(connection, id);
            if (updatedPageResult == null)
            {
                return ApiResponse<ContentPageDto>.ErrorResponse("UPDATE_FAILED", "Failed to retrieve updated content page");
            }

            return ApiResponse<ContentPageDto>.SuccessResponse(updatedPageResult, "Content page updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<ContentPageDto>.ErrorResponse(
                "UPDATE_CONTENT_PAGE_FAILED", "Failed to update content page", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> DeleteContentPageAsync(Guid id)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if page exists
            var pageExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM ContentPages WHERE ID = @Id",
                id.ToSqlParam("@Id"));

            if (!pageExists.Any() || pageExists.First() == 0)
            {
                return ApiResponse<bool>.ErrorResponse("CONTENT_PAGE_NOT_FOUND", "Content page not found");
            }

            await connection.ExecuteNonQueryText(@"
                DELETE FROM ContentPages WHERE ID = @Id",
                id.ToSqlParam("@Id"));

            return ApiResponse<bool>.SuccessResponse(true, "Content page deleted successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "DELETE_CONTENT_PAGE_FAILED", "Failed to delete content page", ex.Message);
        }
    }

    public async Task<ApiResponse<PaginatedResult<FaqDto>>> GetFaqsAsync(
        string? search = null, string? category = null, bool? isPublished = null, int page = 1, int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            // Build WHERE clause
            if (!string.IsNullOrEmpty(search))
            {
                whereConditions.Add("(f.Question LIKE @Search OR f.Answer LIKE @Search)");
                parameters.Add($"%{search}%".ToSqlParam("@Search"));
            }

            if (!string.IsNullOrEmpty(category))
            {
                whereConditions.Add("f.Category = @Category");
                parameters.Add(category.ToSqlParam("@Category"));
            }

            if (isPublished.HasValue)
            {
                whereConditions.Add("f.IsPublished = @IsPublished");
                parameters.Add(isPublished.Value.ToSqlParam("@IsPublished"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*)
                FROM Faqs f
                {whereClause}";

            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get paginated data
            var offset = (page - 1) * limit;
            var dataQuery = $@"
                SELECT f.ID, f.Question, f.Answer, f.Category, f.IsPublished, f.SortOrder,
                       f.ViewCount, f.HelpfulCount, f.CreatedAt, f.UpdatedAt
                FROM Faqs f
                {whereClause}
                ORDER BY f.SortOrder ASC, f.CreatedAt DESC
                OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY";

            var faqs = await connection.ExecuteQuery<dynamic>(dataQuery, parameters.ToArray());

            var faqDtos = faqs.Select(f => new FaqDto
            {
                Id = f.ID,
                Question = f.Question?.ToString() ?? "",
                Answer = f.Answer?.ToString() ?? "",
                Category = f.Category?.ToString() ?? "",
                IsPublished = f.IsPublished != null && (bool)f.IsPublished,
                SortOrder = f.SortOrder != null ? (int)f.SortOrder : 0,
                ViewCount = f.ViewCount != null ? (int)f.ViewCount : 0,
                HelpfulCount = f.HelpfulCount != null ? (int)f.HelpfulCount : 0,
                CreatedAt = f.CreatedAt != null ? (DateTime)f.CreatedAt : DateTime.MinValue,
                UpdatedAt = f.UpdatedAt as DateTime?,
                IsHelpful = false // This would be determined per user in a real implementation
            }).ToList();

            var result = new PaginatedResult<FaqDto>
            {
                Items = faqDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<FaqDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<FaqDto>>.ErrorResponse(
                "GET_FAQS_FAILED", "Failed to get FAQs", ex.Message);
        }
    }

    public async Task<ApiResponse<List<FaqCategoryDto>>> GetFaqCategoriesAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var query = @"
                SELECT f.Category as Name,
                       COUNT(*) as FaqCount,
                       MIN(f.SortOrder) as SortOrder
                FROM Faqs f
                WHERE f.IsPublished = 1
                GROUP BY f.Category
                ORDER BY MIN(f.SortOrder), f.Category";

            var categories = await connection.ExecuteQuery<dynamic>(query);

            var categoryDtos = categories.Select(c => new FaqCategoryDto
            {
                Name = c.Name?.ToString() ?? "",
                Description = null, // Could be added to database if needed
                FaqCount = c.FaqCount != null ? (int)c.FaqCount : 0,
                SortOrder = c.SortOrder != null ? (int)c.SortOrder : 0
            }).ToList();

            return ApiResponse<List<FaqCategoryDto>>.SuccessResponse(categoryDtos);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<FaqCategoryDto>>.ErrorResponse(
                "GET_FAQ_CATEGORIES_FAILED", "Failed to get FAQ categories", ex.Message);
        }
    }

    public async Task<ApiResponse<FaqDto>> GetFaqByIdAsync(Guid id)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Increment view count
            await connection.ExecuteNonQueryText(@"
                UPDATE Faqs SET ViewCount = ViewCount + 1 WHERE ID = @Id",
                id.ToSqlParam("@Id"));

            var query = @"
                SELECT f.ID, f.Question, f.Answer, f.Category, f.IsPublished, f.SortOrder,
                       f.ViewCount, f.HelpfulCount, f.CreatedAt, f.UpdatedAt
                FROM Faqs f
                WHERE f.ID = @Id";

            var faqs = await connection.ExecuteQuery<dynamic>(query, id.ToSqlParam("@Id"));

            if (!faqs.Any())
            {
                return ApiResponse<FaqDto>.ErrorResponse("FAQ_NOT_FOUND", "FAQ not found");
            }

            var faq = faqs.First();
            var faqDto = new FaqDto
            {
                Id = faq.ID,
                Question = faq.Question?.ToString() ?? "",
                Answer = faq.Answer?.ToString() ?? "",
                Category = faq.Category?.ToString() ?? "",
                IsPublished = faq.IsPublished != null && (bool)faq.IsPublished,
                SortOrder = faq.SortOrder != null ? (int)faq.SortOrder : 0,
                ViewCount = faq.ViewCount != null ? (int)faq.ViewCount : 0,
                HelpfulCount = faq.HelpfulCount != null ? (int)faq.HelpfulCount : 0,
                CreatedAt = faq.CreatedAt != null ? (DateTime)faq.CreatedAt : DateTime.MinValue,
                UpdatedAt = faq.UpdatedAt as DateTime?,
                IsHelpful = false // This would be determined per user in a real implementation
            };

            return ApiResponse<FaqDto>.SuccessResponse(faqDto);
        }
        catch (Exception ex)
        {
            return ApiResponse<FaqDto>.ErrorResponse(
                "GET_FAQ_FAILED", "Failed to get FAQ", ex.Message);
        }
    }

    public async Task<ApiResponse<FaqDto>> CreateFaqAsync(CreateFaqDto createDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var id = Guid.NewGuid();
            var now = DateTime.UtcNow;

            var parameters = new List<SqlParameter>
            {
                id.ToSqlParam("@Id"),
                createDto.Question.ToSqlParam("@Question"),
                createDto.Answer.ToSqlParam("@Answer"),
                createDto.Category.ToSqlParam("@Category"),
                createDto.IsPublished.ToSqlParam("@IsPublished"),
                createDto.SortOrder.ToSqlParam("@SortOrder"),
                0.ToSqlParam("@ViewCount"),
                0.ToSqlParam("@HelpfulCount"),
                now.ToSqlParam("@CreatedAt"),
                userId.ToSqlParam("@CreatedBy")
            };

            await connection.ExecuteNonQueryText(@"
                INSERT INTO Faqs (ID, Question, Answer, Category, IsPublished, SortOrder,
                                  ViewCount, HelpfulCount, CreatedAt, CreatedBy)
                VALUES (@Id, @Question, @Answer, @Category, @IsPublished, @SortOrder,
                        @ViewCount, @HelpfulCount, @CreatedAt, @CreatedBy)", parameters.ToArray());

            // Get the created FAQ
            var createdFaqResult = await GetFaqByIdInternalAsync(connection, id);
            if (createdFaqResult == null)
            {
                return ApiResponse<FaqDto>.ErrorResponse("CREATE_FAILED", "Failed to retrieve created FAQ");
            }

            return ApiResponse<FaqDto>.SuccessResponse(createdFaqResult, "FAQ created successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<FaqDto>.ErrorResponse(
                "CREATE_FAQ_FAILED", "Failed to create FAQ", ex.Message);
        }
    }

    public async Task<ApiResponse<FaqDto>> UpdateFaqAsync(Guid id, UpdateFaqDto updateDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if FAQ exists
            var faqExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM Faqs WHERE ID = @Id",
                id.ToSqlParam("@Id"));

            if (!faqExists.Any() || faqExists.First() == 0)
            {
                return ApiResponse<FaqDto>.ErrorResponse("FAQ_NOT_FOUND", "FAQ not found");
            }

            var updateFields = new List<string>();
            var parameters = new List<SqlParameter>
            {
                id.ToSqlParam("@Id"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"),
                userId.ToSqlParam("@UpdatedBy")
            };

            if (!string.IsNullOrEmpty(updateDto.Question))
            {
                updateFields.Add("Question = @Question");
                parameters.Add(updateDto.Question.ToSqlParam("@Question"));
            }

            if (!string.IsNullOrEmpty(updateDto.Answer))
            {
                updateFields.Add("Answer = @Answer");
                parameters.Add(updateDto.Answer.ToSqlParam("@Answer"));
            }

            if (!string.IsNullOrEmpty(updateDto.Category))
            {
                updateFields.Add("Category = @Category");
                parameters.Add(updateDto.Category.ToSqlParam("@Category"));
            }

            if (updateDto.IsPublished.HasValue)
            {
                updateFields.Add("IsPublished = @IsPublished");
                parameters.Add(updateDto.IsPublished.Value.ToSqlParam("@IsPublished"));
            }

            if (updateDto.SortOrder.HasValue)
            {
                updateFields.Add("SortOrder = @SortOrder");
                parameters.Add(updateDto.SortOrder.Value.ToSqlParam("@SortOrder"));
            }

            if (updateFields.Count == 0)
            {
                return ApiResponse<FaqDto>.ErrorResponse("NO_UPDATES", "No fields to update");
            }

            updateFields.Add("UpdatedAt = @UpdatedAt");
            updateFields.Add("UpdatedBy = @UpdatedBy");

            var updateQuery = $@"
                UPDATE Faqs
                SET {string.Join(", ", updateFields)}
                WHERE ID = @Id";

            await connection.ExecuteNonQueryText(updateQuery, parameters.ToArray());

            // Get the updated FAQ
            var updatedFaqResult = await GetFaqByIdInternalAsync(connection, id);
            if (updatedFaqResult == null)
            {
                return ApiResponse<FaqDto>.ErrorResponse("UPDATE_FAILED", "Failed to retrieve updated FAQ");
            }

            return ApiResponse<FaqDto>.SuccessResponse(updatedFaqResult, "FAQ updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<FaqDto>.ErrorResponse(
                "UPDATE_FAQ_FAILED", "Failed to update FAQ", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> DeleteFaqAsync(Guid id)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if FAQ exists
            var faqExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM Faqs WHERE ID = @Id",
                id.ToSqlParam("@Id"));

            if (!faqExists.Any() || faqExists.First() == 0)
            {
                return ApiResponse<bool>.ErrorResponse("FAQ_NOT_FOUND", "FAQ not found");
            }

            // Delete related helpful records first
            await connection.ExecuteNonQueryText(@"
                DELETE FROM FaqHelpful WHERE FaqID = @Id",
                id.ToSqlParam("@Id"));

            // Delete the FAQ
            await connection.ExecuteNonQueryText(@"
                DELETE FROM Faqs WHERE ID = @Id",
                id.ToSqlParam("@Id"));

            return ApiResponse<bool>.SuccessResponse(true, "FAQ deleted successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "DELETE_FAQ_FAILED", "Failed to delete FAQ", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> MarkFaqAsHelpfulAsync(Guid faqId, bool isHelpful, Guid? userId = null, string? ipAddress = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if FAQ exists
            var faqExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM Faqs WHERE ID = @FaqId",
                faqId.ToSqlParam("@FaqId"));

            if (!faqExists.Any() || faqExists.First() == 0)
            {
                return ApiResponse<bool>.ErrorResponse("FAQ_NOT_FOUND", "FAQ not found");
            }

            // Check if user/IP already voted
            var existingVote = await connection.ExecuteQuery<dynamic>(@"
                SELECT ID, IsHelpful FROM FaqHelpful
                WHERE FaqID = @FaqId AND (UserID = @UserId OR IPAddress = @IpAddress)",
                faqId.ToSqlParam("@FaqId"),
                (userId ?? Guid.Empty).ToSqlParam("@UserId"),
                (ipAddress ?? "").ToSqlParam("@IpAddress"));

            if (existingVote.Any())
            {
                var vote = existingVote.First();
                var currentVote = vote.IsHelpful != null && (bool)vote.IsHelpful;

                if (currentVote == isHelpful)
                {
                    return ApiResponse<bool>.ErrorResponse("ALREADY_VOTED", "You have already marked this FAQ as " + (isHelpful ? "helpful" : "not helpful"));
                }

                // Update existing vote
                await connection.ExecuteNonQueryText(@"
                    UPDATE FaqHelpful SET IsHelpful = @IsHelpful WHERE ID = @VoteId",
                    isHelpful.ToSqlParam("@IsHelpful"),
                    ((Guid)vote.ID).ToSqlParam("@VoteId"));

                // Update FAQ helpful count
                var countChange = isHelpful ? (currentVote ? 0 : 1) : (currentVote ? -1 : 0);
                if (countChange != 0)
                {
                    await connection.ExecuteNonQueryText(@"
                        UPDATE Faqs SET HelpfulCount = HelpfulCount + @CountChange WHERE ID = @FaqId",
                        countChange.ToSqlParam("@CountChange"),
                        faqId.ToSqlParam("@FaqId"));
                }
            }
            else
            {
                // Create new vote
                var voteId = Guid.NewGuid();
                await connection.ExecuteNonQueryText(@"
                    INSERT INTO FaqHelpful (ID, FaqID, UserID, IPAddress, IsHelpful, CreatedAt)
                    VALUES (@Id, @FaqId, @UserId, @IpAddress, @IsHelpful, @CreatedAt)",
                    voteId.ToSqlParam("@Id"),
                    faqId.ToSqlParam("@FaqId"),
                    userId.ToSqlParam("@UserId"),
                    ipAddress.ToSqlParam("@IpAddress"),
                    isHelpful.ToSqlParam("@IsHelpful"),
                    DateTime.UtcNow.ToSqlParam("@CreatedAt"));

                // Update FAQ helpful count
                if (isHelpful)
                {
                    await connection.ExecuteNonQueryText(@"
                        UPDATE Faqs SET HelpfulCount = HelpfulCount + 1 WHERE ID = @FaqId",
                        faqId.ToSqlParam("@FaqId"));
                }
            }

            return ApiResponse<bool>.SuccessResponse(true, "FAQ feedback recorded successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "MARK_FAQ_HELPFUL_FAILED", "Failed to record FAQ feedback", ex.Message);
        }
    }

    // Helper methods
    private async Task<ContentPageDto?> GetContentPageByIdAsync(SqlConnection connection, Guid id)
    {
        var query = @"
            SELECT cp.ID, cp.Slug, cp.Title, cp.Content, cp.MetaTitle, cp.MetaDescription,
                   cp.MetaKeywords, cp.IsPublished, cp.SortOrder, cp.CreatedAt, cp.UpdatedAt,
                   cu.FirstName + ' ' + cu.LastName as CreatedBy,
                   uu.FirstName + ' ' + uu.LastName as UpdatedBy
            FROM ContentPages cp
            LEFT JOIN Users cu ON cp.CreatedBy = cu.ID
            LEFT JOIN Users uu ON cp.UpdatedBy = uu.ID
            WHERE cp.ID = @Id";

        var pages = await connection.ExecuteQuery<dynamic>(query, id.ToSqlParam("@Id"));

        if (!pages.Any()) return null;

        var page = pages.First();
        return new ContentPageDto
        {
            Id = page.ID,
            Slug = page.Slug?.ToString() ?? "",
            Title = page.Title?.ToString() ?? "",
            Content = page.Content?.ToString() ?? "",
            MetaTitle = page.MetaTitle?.ToString(),
            MetaDescription = page.MetaDescription?.ToString(),
            MetaKeywords = page.MetaKeywords?.ToString(),
            IsPublished = page.IsPublished != null && (bool)page.IsPublished,
            SortOrder = page.SortOrder != null ? (int)page.SortOrder : 0,
            CreatedAt = page.CreatedAt != null ? (DateTime)page.CreatedAt : DateTime.MinValue,
            UpdatedAt = page.UpdatedAt as DateTime?,
            CreatedBy = page.CreatedBy?.ToString(),
            UpdatedBy = page.UpdatedBy?.ToString()
        };
    }

    private async Task<FaqDto?> GetFaqByIdInternalAsync(SqlConnection connection, Guid id)
    {
        var query = @"
            SELECT f.ID, f.Question, f.Answer, f.Category, f.IsPublished, f.SortOrder,
                   f.ViewCount, f.HelpfulCount, f.CreatedAt, f.UpdatedAt
            FROM Faqs f
            WHERE f.ID = @Id";

        var faqs = await connection.ExecuteQuery<dynamic>(query, id.ToSqlParam("@Id"));

        if (!faqs.Any()) return null;

        var faq = faqs.First();
        return new FaqDto
        {
            Id = faq.ID,
            Question = faq.Question?.ToString() ?? "",
            Answer = faq.Answer?.ToString() ?? "",
            Category = faq.Category?.ToString() ?? "",
            IsPublished = faq.IsPublished != null && (bool)faq.IsPublished,
            SortOrder = faq.SortOrder != null ? (int)faq.SortOrder : 0,
            ViewCount = faq.ViewCount != null ? (int)faq.ViewCount : 0,
            HelpfulCount = faq.HelpfulCount != null ? (int)faq.HelpfulCount : 0,
            CreatedAt = faq.CreatedAt != null ? (DateTime)faq.CreatedAt : DateTime.MinValue,
            UpdatedAt = faq.UpdatedAt as DateTime?,
            IsHelpful = false
        };
    }

    public async Task<ApiResponse<PaginatedResult<ContactSubmissionDto>>> GetContactSubmissionsAsync(
        string? search = null, string? status = null, string? priority = null, string? category = null,
        DateTime? createdFrom = null, DateTime? createdTo = null, int page = 1, int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            // Build WHERE clause
            if (!string.IsNullOrEmpty(search))
            {
                whereConditions.Add("(cs.Name LIKE @Search OR cs.Email LIKE @Search OR cs.Subject LIKE @Search OR cs.Message LIKE @Search)");
                parameters.Add($"%{search}%".ToSqlParam("@Search"));
            }

            if (!string.IsNullOrEmpty(status))
            {
                whereConditions.Add("cs.Status = @Status");
                parameters.Add(status.ToSqlParam("@Status"));
            }

            if (!string.IsNullOrEmpty(priority))
            {
                whereConditions.Add("cs.Priority = @Priority");
                parameters.Add(priority.ToSqlParam("@Priority"));
            }

            if (!string.IsNullOrEmpty(category))
            {
                whereConditions.Add("cs.Category = @Category");
                parameters.Add(category.ToSqlParam("@Category"));
            }

            if (createdFrom.HasValue)
            {
                whereConditions.Add("cs.CreatedAt >= @CreatedFrom");
                parameters.Add(createdFrom.Value.ToSqlParam("@CreatedFrom"));
            }

            if (createdTo.HasValue)
            {
                whereConditions.Add("cs.CreatedAt <= @CreatedTo");
                parameters.Add(createdTo.Value.ToSqlParam("@CreatedTo"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*)
                FROM ContactSubmissions cs
                {whereClause}";

            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get paginated data
            var offset = (page - 1) * limit;
            var dataQuery = $@"
                SELECT cs.ID, cs.Name, cs.Email, cs.PhoneNumber, cs.Subject, cs.Message,
                       cs.Status, cs.Priority, cs.Category, cs.Response, cs.InternalNotes,
                       cs.CreatedAt, cs.RespondedAt, cs.UpdatedAt,
                       ru.FirstName + ' ' + ru.LastName as RespondedBy
                FROM ContactSubmissions cs
                LEFT JOIN Users ru ON cs.RespondedBy = ru.ID
                {whereClause}
                ORDER BY
                    CASE cs.Priority
                        WHEN 'Urgent' THEN 1
                        WHEN 'High' THEN 2
                        WHEN 'Medium' THEN 3
                        WHEN 'Low' THEN 4
                        ELSE 5
                    END,
                    cs.CreatedAt DESC
                OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY";

            var submissions = await connection.ExecuteQuery<dynamic>(dataQuery, parameters.ToArray());

            var submissionDtos = submissions.Select(s => new ContactSubmissionDto
            {
                Id = s.ID,
                Name = s.Name?.ToString() ?? "",
                Email = s.Email?.ToString() ?? "",
                PhoneNumber = s.PhoneNumber?.ToString(),
                Subject = s.Subject?.ToString() ?? "",
                Message = s.Message?.ToString() ?? "",
                Status = s.Status?.ToString() ?? "",
                Priority = s.Priority?.ToString() ?? "",
                Category = s.Category?.ToString(),
                Response = s.Response?.ToString(),
                InternalNotes = s.InternalNotes?.ToString(),
                CreatedAt = s.CreatedAt != null ? (DateTime)s.CreatedAt : DateTime.MinValue,
                RespondedAt = s.RespondedAt as DateTime?,
                RespondedBy = s.RespondedBy?.ToString()
            }).ToList();

            var result = new PaginatedResult<ContactSubmissionDto>
            {
                Items = submissionDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<ContactSubmissionDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<ContactSubmissionDto>>.ErrorResponse(
                "GET_CONTACT_SUBMISSIONS_FAILED", "Failed to get contact submissions", ex.Message);
        }
    }

    public async Task<ApiResponse<ContactSubmissionDto>> CreateContactSubmissionAsync(CreateContactSubmissionDto createDto)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var id = Guid.NewGuid();
            var now = DateTime.UtcNow;

            var parameters = new List<SqlParameter>
            {
                id.ToSqlParam("@Id"),
                createDto.Name.ToSqlParam("@Name"),
                createDto.Email.ToSqlParam("@Email"),
                createDto.Subject.ToSqlParam("@Subject"),
                createDto.Message.ToSqlParam("@Message"),
                "New".ToSqlParam("@Status"),
                "Medium".ToSqlParam("@Priority"),
                now.ToSqlParam("@CreatedAt")
            };

            var insertQuery = new StringBuilder(@"
                INSERT INTO ContactSubmissions (ID, Name, Email, Subject, Message, Status, Priority, CreatedAt");

            var valuesQuery = new StringBuilder(@"
                VALUES (@Id, @Name, @Email, @Subject, @Message, @Status, @Priority, @CreatedAt");

            if (!string.IsNullOrEmpty(createDto.PhoneNumber))
            {
                insertQuery.Append(", PhoneNumber");
                valuesQuery.Append(", @PhoneNumber");
                parameters.Add(createDto.PhoneNumber.ToSqlParam("@PhoneNumber"));
            }

            if (!string.IsNullOrEmpty(createDto.Category))
            {
                insertQuery.Append(", Category");
                valuesQuery.Append(", @Category");
                parameters.Add(createDto.Category.ToSqlParam("@Category"));
            }

            insertQuery.Append(") ");
            valuesQuery.Append(")");

            await connection.ExecuteNonQueryText(insertQuery.ToString() + valuesQuery.ToString(), parameters.ToArray());

            // Get the created submission
            var createdSubmissionResult = await GetContactSubmissionByIdAsync(connection, id);
            if (createdSubmissionResult == null)
            {
                return ApiResponse<ContactSubmissionDto>.ErrorResponse("CREATE_FAILED", "Failed to retrieve created contact submission");
            }

            return ApiResponse<ContactSubmissionDto>.SuccessResponse(createdSubmissionResult, "Contact submission created successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<ContactSubmissionDto>.ErrorResponse(
                "CREATE_CONTACT_SUBMISSION_FAILED", "Failed to create contact submission", ex.Message);
        }
    }

    public async Task<ApiResponse<ContactSubmissionDto>> UpdateContactSubmissionAsync(Guid id, UpdateContactSubmissionDto updateDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if submission exists
            var submissionExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM ContactSubmissions WHERE ID = @Id",
                id.ToSqlParam("@Id"));

            if (!submissionExists.Any() || submissionExists.First() == 0)
            {
                return ApiResponse<ContactSubmissionDto>.ErrorResponse("CONTACT_SUBMISSION_NOT_FOUND", "Contact submission not found");
            }

            var updateFields = new List<string>();
            var parameters = new List<SqlParameter>
            {
                id.ToSqlParam("@Id"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"),
                userId.ToSqlParam("@UpdatedBy")
            };

            updateFields.Add("Status = @Status");
            parameters.Add(updateDto.Status.ToSqlParam("@Status"));

            updateFields.Add("Priority = @Priority");
            parameters.Add(updateDto.Priority.ToSqlParam("@Priority"));

            if (updateDto.Response != null)
            {
                updateFields.Add("Response = @Response");
                updateFields.Add("RespondedAt = @RespondedAt");
                updateFields.Add("RespondedBy = @RespondedBy");
                parameters.Add((updateDto.Response ?? "").ToSqlParam("@Response"));
                parameters.Add(DateTime.UtcNow.ToSqlParam("@RespondedAt"));
                parameters.Add(userId.ToSqlParam("@RespondedBy"));
            }

            if (updateDto.InternalNotes != null)
            {
                updateFields.Add("InternalNotes = @InternalNotes");
                parameters.Add((updateDto.InternalNotes ?? "").ToSqlParam("@InternalNotes"));
            }

            if (updateDto.Category != null)
            {
                updateFields.Add("Category = @Category");
                parameters.Add((updateDto.Category ?? "").ToSqlParam("@Category"));
            }

            updateFields.Add("UpdatedAt = @UpdatedAt");
            updateFields.Add("UpdatedBy = @UpdatedBy");

            var updateQuery = $@"
                UPDATE ContactSubmissions
                SET {string.Join(", ", updateFields)}
                WHERE ID = @Id";

            await connection.ExecuteNonQueryText(updateQuery, parameters.ToArray());

            // Get the updated submission
            var updatedSubmissionResult = await GetContactSubmissionByIdAsync(connection, id);
            if (updatedSubmissionResult == null)
            {
                return ApiResponse<ContactSubmissionDto>.ErrorResponse("UPDATE_FAILED", "Failed to retrieve updated contact submission");
            }

            return ApiResponse<ContactSubmissionDto>.SuccessResponse(updatedSubmissionResult, "Contact submission updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<ContactSubmissionDto>.ErrorResponse(
                "UPDATE_CONTACT_SUBMISSION_FAILED", "Failed to update contact submission", ex.Message);
        }
    }

    public async Task<ApiResponse<ContentStatsDto>> GetContentStatsAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var stats = new ContentStatsDto();

            // Content Pages stats
            var pageStats = await connection.ExecuteQuery<dynamic>(@"
                SELECT
                    COUNT(*) as TotalPages,
                    SUM(CASE WHEN IsPublished = 1 THEN 1 ELSE 0 END) as PublishedPages,
                    SUM(CASE WHEN IsPublished = 0 THEN 1 ELSE 0 END) as DraftPages,
                    MAX(UpdatedAt) as LastContentUpdate
                FROM ContentPages");

            if (pageStats.Any())
            {
                var pageStat = pageStats.First();
                stats.TotalPages = pageStat.TotalPages != null ? (int)pageStat.TotalPages : 0;
                stats.PublishedPages = pageStat.PublishedPages != null ? (int)pageStat.PublishedPages : 0;
                stats.DraftPages = pageStat.DraftPages != null ? (int)pageStat.DraftPages : 0;
                stats.LastContentUpdate = pageStat.LastContentUpdate != null ? (DateTime)pageStat.LastContentUpdate : DateTime.MinValue;
            }

            // FAQ stats
            var faqStats = await connection.ExecuteQuery<dynamic>(@"
                SELECT
                    COUNT(*) as TotalFaqs,
                    SUM(CASE WHEN IsPublished = 1 THEN 1 ELSE 0 END) as PublishedFaqs,
                    COUNT(DISTINCT Category) as FaqCategories
                FROM Faqs");

            if (faqStats.Any())
            {
                var faqStat = faqStats.First();
                stats.TotalFaqs = faqStat.TotalFaqs != null ? (int)faqStat.TotalFaqs : 0;
                stats.PublishedFaqs = faqStat.PublishedFaqs != null ? (int)faqStat.PublishedFaqs : 0;
                stats.FaqCategories = faqStat.FaqCategories != null ? (int)faqStat.FaqCategories : 0;
            }

            // Most viewed FAQ
            var mostViewedFaq = await connection.ExecuteQuery<dynamic>(@"
                SELECT TOP 1 ID, Question, ViewCount
                FROM Faqs
                WHERE IsPublished = 1
                ORDER BY ViewCount DESC");

            if (mostViewedFaq.Any())
            {
                var faq = mostViewedFaq.First();
                stats.MostViewedFaqId = faq.ID != null ? (int)faq.ID.GetHashCode() : 0; // Simplified for demo
                stats.MostViewedFaqQuestion = faq.Question?.ToString();
            }

            // Contact submission stats
            var contactStats = await connection.ExecuteQuery<dynamic>(@"
                SELECT
                    COUNT(*) as TotalContactSubmissions,
                    SUM(CASE WHEN Status = 'New' THEN 1 ELSE 0 END) as NewContactSubmissions,
                    SUM(CASE WHEN Status = 'Resolved' THEN 1 ELSE 0 END) as ResolvedContactSubmissions
                FROM ContactSubmissions");

            if (contactStats.Any())
            {
                var contactStat = contactStats.First();
                stats.TotalContactSubmissions = contactStat.TotalContactSubmissions != null ? (int)contactStat.TotalContactSubmissions : 0;
                stats.NewContactSubmissions = contactStat.NewContactSubmissions != null ? (int)contactStat.NewContactSubmissions : 0;
                stats.ResolvedContactSubmissions = contactStat.ResolvedContactSubmissions != null ? (int)contactStat.ResolvedContactSubmissions : 0;
            }

            return ApiResponse<ContentStatsDto>.SuccessResponse(stats);
        }
        catch (Exception ex)
        {
            return ApiResponse<ContentStatsDto>.ErrorResponse(
                "GET_CONTENT_STATS_FAILED", "Failed to get content statistics", ex.Message);
        }
    }

    // Helper method for contact submissions
    private async Task<ContactSubmissionDto?> GetContactSubmissionByIdAsync(SqlConnection connection, Guid id)
    {
        var query = @"
            SELECT cs.ID, cs.Name, cs.Email, cs.PhoneNumber, cs.Subject, cs.Message,
                   cs.Status, cs.Priority, cs.Category, cs.Response, cs.InternalNotes,
                   cs.CreatedAt, cs.RespondedAt, cs.UpdatedAt,
                   ru.FirstName + ' ' + ru.LastName as RespondedBy
            FROM ContactSubmissions cs
            LEFT JOIN Users ru ON cs.RespondedBy = ru.ID
            WHERE cs.ID = @Id";

        var submissions = await connection.ExecuteQuery<dynamic>(query, id.ToSqlParam("@Id"));

        if (!submissions.Any()) return null;

        var submission = submissions.First();
        return new ContactSubmissionDto
        {
            Id = submission.ID,
            Name = submission.Name?.ToString() ?? "",
            Email = submission.Email?.ToString() ?? "",
            PhoneNumber = submission.PhoneNumber?.ToString(),
            Subject = submission.Subject?.ToString() ?? "",
            Message = submission.Message?.ToString() ?? "",
            Status = submission.Status?.ToString() ?? "",
            Priority = submission.Priority?.ToString() ?? "",
            Category = submission.Category?.ToString(),
            Response = submission.Response?.ToString(),
            InternalNotes = submission.InternalNotes?.ToString(),
            CreatedAt = submission.CreatedAt != null ? (DateTime)submission.CreatedAt : DateTime.MinValue,
            RespondedAt = submission.RespondedAt as DateTime?,
            RespondedBy = submission.RespondedBy?.ToString()
        };
    }
}
