using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Review;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;
using MuslimDirectory.API.Models.Entities;
using XGENO.DBHelpers.Core;
using System.Data.SqlClient;

namespace MuslimDirectory.API.Services.Implementations;

public class ReviewService(IConfiguration configuration) : IReviewService
{
    private readonly string _connectionString = configuration.GetConnectionString("DefaultConnection") 
        ?? throw new ArgumentNullException("Connection string not found");

    public async Task<ApiResponse<PaginatedResult<ReviewDto>>> GetReviewsAsync(
        Guid listingId, int page = 1, int limit = 20, string? sortBy = null, 
        int? rating = null, bool? hasResponse = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var offset = (page - 1) * limit;
            var orderBy = sortBy?.ToLower() switch
            {
                "oldest" => "r.CreatedAt ASC",
                "rating_high" => "r.Rating DESC, r.CreatedAt DESC",
                "rating_low" => "r.Rating ASC, r.CreatedAt DESC",
                "helpful" => "r.HelpfulCount DESC, r.CreatedAt DESC",
                _ => "r.CreatedAt DESC"
            };

            var whereConditions = new List<string> { "r.ListingID = @ListingID", "r.Status = 'Approved'" };
            var parameters = new List<SqlParameter> { listingId.ToSqlParam("@ListingID") };

            if (rating.HasValue)
            {
                whereConditions.Add("r.Rating = @Rating");
                parameters.Add(rating.Value.ToSqlParam("@Rating"));
            }

            if (hasResponse.HasValue)
            {
                if (hasResponse.Value)
                    whereConditions.Add("rr.ID IS NOT NULL");
                else
                    whereConditions.Add("rr.ID IS NULL");
            }

            var whereClause = string.Join(" AND ", whereConditions);

            // Get total count
            var countQuery = $@"
                SELECT COUNT(DISTINCT r.ID)
                FROM Reviews r
                LEFT JOIN ReviewResponses rr ON r.ID = rr.ReviewID
                WHERE {whereClause}";

            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get reviews
            var query = $@"
                SELECT DISTINCT
                    r.ID, r.ListingID, r.Rating, r.Title, r.ReviewText, 
                    r.IslamicComplianceRating, r.Status, r.HelpfulCount, 
                    r.CreatedAt, r.UpdatedAt,
                    u.ID as AuthorId, u.FirstName, u.LastName, u.ProfilePicture,
                    l.Title as ListingTitle,
                    rr.ID as ResponseId, rr.ResponseText, rr.CreatedAt as ResponseCreatedAt,
                    CASE 
                        WHEN o.ID IS NOT NULL THEN o.Name
                        ELSE CONCAT(ru.FirstName, ' ', ru.LastName)
                    END as ResponderName,
                    CASE 
                        WHEN o.ID IS NOT NULL THEN 'Organization'
                        ELSE 'User'
                    END as ResponderType,
                    o.LogoURL as ResponderLogo
                FROM Reviews r
                INNER JOIN Users u ON r.UserID = u.ID
                INNER JOIN Listings l ON r.ListingID = l.ID
                LEFT JOIN ReviewResponses rr ON r.ID = rr.ReviewID
                LEFT JOIN Users ru ON rr.RespondedBy = ru.ID
                LEFT JOIN OrganizationUsers ou ON ru.ID = ou.UserID
                LEFT JOIN Organizations o ON ou.OrganizationID = o.ID
                WHERE {whereClause}
                ORDER BY {orderBy}
                OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY";

            parameters.Add(offset.ToSqlParam("@Offset"));
            parameters.Add(limit.ToSqlParam("@Limit"));

            var reviewData = await connection.ExecuteQuery<dynamic>(query, parameters.ToArray());

            var reviews = reviewData.Select(r => new ReviewDto
            {
                Id = (Guid)r.ID,
                ListingID = (Guid)r.ListingID,
                ListingTitle = r.ListingTitle?.ToString(),
                Rating = (int)r.Rating,
                Title = r.Title?.ToString(),
                ReviewText = r.ReviewText?.ToString(),
                IslamicComplianceRating = r.IslamicComplianceRating as int?,
                Status = r.Status?.ToString() ?? "",
                HelpfulCount = (int)r.HelpfulCount,
                Author = new ReviewAuthorDto
                {
                    Id = (Guid)r.AuthorId,
                    DisplayName = $"{r.FirstName} {r.LastName}".Trim(),
                    ProfilePicture = r.ProfilePicture?.ToString()
                },
                Response = r.ResponseId != null ? new ReviewResponseDto
                {
                    Id = (Guid)r.ResponseId,
                    ResponseText = r.ResponseText?.ToString() ?? "",
                    RespondedBy = new ReviewResponderDto
                    {
                        Name = r.ResponderName?.ToString() ?? "",
                        Type = r.ResponderType?.ToString() ?? "",
                        LogoURL = r.ResponderLogo?.ToString()
                    },
                    CreatedAt = (DateTime)r.ResponseCreatedAt
                } : null,
                CreatedAt = (DateTime)r.CreatedAt,
                UpdatedAt = (DateTime)r.UpdatedAt
            }).ToList();

            var result = new PaginatedResult<ReviewDto>
            {
                Items = reviews,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<ReviewDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<ReviewDto>>.ErrorResponse(
                "GET_REVIEWS_FAILED", "Failed to get reviews", ex.Message);
        }
    }

    public async Task<ApiResponse<ReviewCreateResponseDto>> CreateReviewAsync(CreateReviewDto createDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user can review this listing
            var canReview = await CanUserReviewListingAsync(userId, createDto.ListingId);
            if (!canReview.Success || !canReview.Data)
            {
                return ApiResponse<ReviewCreateResponseDto>.ErrorResponse(
                    "REVIEW_NOT_ALLOWED", "You cannot review this listing");
            }

            // Check if user already reviewed this listing
            var existingReviews = await connection.ExecuteQuery<Review>(
                "SELECT * FROM Reviews WHERE UserID = @UserID AND ListingID = @ListingID",
                userId.ToSqlParam("@UserID"),
                createDto.ListingId.ToSqlParam("@ListingID"));

            if (existingReviews.Any())
            {
                return ApiResponse<ReviewCreateResponseDto>.ErrorResponse(
                    "REVIEW_ALREADY_EXISTS", "You have already reviewed this listing");
            }

            // Create review
            var reviewId = Guid.NewGuid();
            await connection.ExecuteNonQueryText(@"
                INSERT INTO Reviews (ID, ListingID, UserID, Rating, Title, ReviewText, 
                    IslamicComplianceRating, Status, CreatedAt, UpdatedAt)
                VALUES (@ID, @ListingID, @UserID, @Rating, @Title, @ReviewText, 
                    @IslamicComplianceRating, @Status, @CreatedAt, @UpdatedAt)",
                reviewId.ToSqlParam("@ID"),
                createDto.ListingId.ToSqlParam("@ListingID"),
                userId.ToSqlParam("@UserID"),
                createDto.Rating.ToSqlParam("@Rating"),
                createDto.Title.ToSqlParam("@Title"),
                createDto.ReviewText.ToSqlParam("@ReviewText"),
                createDto.IslamicComplianceRating.ToSqlParam("@IslamicComplianceRating"),
                "Pending".ToSqlParam("@Status"),
                DateTime.UtcNow.ToSqlParam("@CreatedAt"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"));

            var response = new ReviewCreateResponseDto
            {
                Id = reviewId,
                Status = "Pending",
                Message = "Review submitted successfully and is under moderation. You will be notified once it's approved."
            };

            return ApiResponse<ReviewCreateResponseDto>.SuccessResponse(response, "Review created successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<ReviewCreateResponseDto>.ErrorResponse(
                "CREATE_REVIEW_FAILED", "Failed to create review", ex.Message);
        }
    }

    public async Task<ApiResponse<ReviewDto>> UpdateReviewAsync(Guid reviewId, UpdateReviewDto updateDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user can edit this review
            var canEdit = await CanUserEditReviewAsync(userId, reviewId);
            if (!canEdit.Success || !canEdit.Data)
            {
                return ApiResponse<ReviewDto>.ErrorResponse(
                    "EDIT_NOT_ALLOWED", "You cannot edit this review");
            }

            // Update review
            await connection.ExecuteNonQueryText(@"
                UPDATE Reviews
                SET Rating = @Rating, Title = @Title, ReviewText = @ReviewText,
                    IslamicComplianceRating = @IslamicComplianceRating, UpdatedAt = @UpdatedAt
                WHERE ID = @ID AND UserID = @UserID",
                updateDto.Rating.ToSqlParam("@Rating"),
                updateDto.Title.ToSqlParam("@Title"),
                updateDto.ReviewText.ToSqlParam("@ReviewText"),
                updateDto.IslamicComplianceRating.ToSqlParam("@IslamicComplianceRating"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"),
                reviewId.ToSqlParam("@ID"),
                userId.ToSqlParam("@UserID"));

            // Get updated review
            var updatedReview = await GetReviewByIdAsync(reviewId);
            if (!updatedReview.Success)
            {
                return ApiResponse<ReviewDto>.ErrorResponse(
                    "REVIEW_NOT_FOUND", "Updated review not found");
            }

            return ApiResponse<ReviewDto>.SuccessResponse(updatedReview.Data!, "Review updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<ReviewDto>.ErrorResponse(
                "UPDATE_REVIEW_FAILED", "Failed to update review", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> DeleteReviewAsync(Guid reviewId, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if review exists and belongs to user
            var reviews = await connection.ExecuteQuery<Review>(
                "SELECT * FROM Reviews WHERE ID = @ID AND UserID = @UserID",
                reviewId.ToSqlParam("@ID"),
                userId.ToSqlParam("@UserID"));

            var review = reviews.FirstOrDefault();
            if (review == null)
            {
                return ApiResponse<bool>.ErrorResponse(
                    "REVIEW_NOT_FOUND", "Review not found or you don't have permission to delete it");
            }

            // Only allow deletion if review is not approved or within edit window
            if (review.Status == "Approved" && review.CreatedAt < DateTime.UtcNow.AddHours(-24))
            {
                return ApiResponse<bool>.ErrorResponse(
                    "DELETE_NOT_ALLOWED", "Cannot delete approved reviews after 24 hours");
            }

            // Delete review (cascade will handle related records)
            await connection.ExecuteNonQueryText(
                "DELETE FROM Reviews WHERE ID = @ID",
                reviewId.ToSqlParam("@ID"));

            return ApiResponse<bool>.SuccessResponse(true, "Review deleted successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "DELETE_REVIEW_FAILED", "Failed to delete review", ex.Message);
        }
    }

    public async Task<ApiResponse<ReviewHelpfulResponseDto>> MarkReviewHelpfulAsync(
        Guid reviewId, ReviewHelpfulDto helpfulDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if review exists
            var reviews = await connection.ExecuteQuery<Review>(
                "SELECT * FROM Reviews WHERE ID = @ID AND Status = 'Approved'",
                reviewId.ToSqlParam("@ID"));

            if (!reviews.Any())
            {
                return ApiResponse<ReviewHelpfulResponseDto>.ErrorResponse(
                    "REVIEW_NOT_FOUND", "Review not found");
            }

            // Check if user already voted
            var existingVotes = await connection.ExecuteQuery<ReviewHelpful>(
                "SELECT * FROM ReviewHelpful WHERE ReviewID = @ReviewID AND UserID = @UserID",
                reviewId.ToSqlParam("@ReviewID"),
                userId.ToSqlParam("@UserID"));

            var existingVote = existingVotes.FirstOrDefault();

            if (existingVote != null)
            {
                // Update existing vote
                await connection.ExecuteNonQueryText(
                    "UPDATE ReviewHelpful SET IsHelpful = @IsHelpful WHERE ID = @ID",
                    helpfulDto.IsHelpful.ToSqlParam("@IsHelpful"),
                    existingVote.Id.ToSqlParam("@ID"));
            }
            else
            {
                // Create new vote
                await connection.ExecuteNonQueryText(@"
                    INSERT INTO ReviewHelpful (ID, ReviewID, UserID, IsHelpful, CreatedAt)
                    VALUES (@ID, @ReviewID, @UserID, @IsHelpful, @CreatedAt)",
                    Guid.NewGuid().ToSqlParam("@ID"),
                    reviewId.ToSqlParam("@ReviewID"),
                    userId.ToSqlParam("@UserID"),
                    helpfulDto.IsHelpful.ToSqlParam("@IsHelpful"),
                    DateTime.UtcNow.ToSqlParam("@CreatedAt"));
            }

            // Update helpful count on review
            await connection.ExecuteNonQueryText(@"
                UPDATE Reviews
                SET HelpfulCount = (
                    SELECT COUNT(*) FROM ReviewHelpful
                    WHERE ReviewID = @ReviewID AND IsHelpful = 1
                )
                WHERE ID = @ReviewID",
                reviewId.ToSqlParam("@ReviewID"));

            // Get updated count
            var helpfulCount = (await connection.ExecuteQuery<int>(
                "SELECT HelpfulCount FROM Reviews WHERE ID = @ID",
                reviewId.ToSqlParam("@ID"))).First();

            var response = new ReviewHelpfulResponseDto
            {
                HelpfulCount = helpfulCount,
                UserVote = helpfulDto.IsHelpful
            };

            return ApiResponse<ReviewHelpfulResponseDto>.SuccessResponse(response, "Vote recorded successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<ReviewHelpfulResponseDto>.ErrorResponse(
                "MARK_HELPFUL_FAILED", "Failed to mark review as helpful", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> ReportReviewAsync(Guid reviewId, ReportReviewDto reportDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if review exists
            var reviews = await connection.ExecuteQuery<Review>(
                "SELECT * FROM Reviews WHERE ID = @ID",
                reviewId.ToSqlParam("@ID"));

            if (!reviews.Any())
            {
                return ApiResponse<bool>.ErrorResponse("REVIEW_NOT_FOUND", "Review not found");
            }

            // Check if user already reported this review
            var existingReports = await connection.ExecuteQuery<ReviewReport>(
                "SELECT * FROM ReviewReports WHERE ReviewID = @ReviewID AND UserID = @UserID",
                reviewId.ToSqlParam("@ReviewID"),
                userId.ToSqlParam("@UserID"));

            if (existingReports.Any())
            {
                return ApiResponse<bool>.ErrorResponse(
                    "ALREADY_REPORTED", "You have already reported this review");
            }

            // Create report
            await connection.ExecuteNonQueryText(@"
                INSERT INTO ReviewReports (ID, ReviewID, UserID, ReasonCode, ReasonText, Status, CreatedAt)
                VALUES (@ID, @ReviewID, @UserID, @ReasonCode, @ReasonText, @Status, @CreatedAt)",
                Guid.NewGuid().ToSqlParam("@ID"),
                reviewId.ToSqlParam("@ReviewID"),
                userId.ToSqlParam("@UserID"),
                reportDto.ReasonCode.ToSqlParam("@ReasonCode"),
                reportDto.ReasonText.ToSqlParam("@ReasonText"),
                "Pending".ToSqlParam("@Status"),
                DateTime.UtcNow.ToSqlParam("@CreatedAt"));

            // Update report count on review
            await connection.ExecuteNonQueryText(@"
                UPDATE Reviews
                SET ReportCount = (SELECT COUNT(*) FROM ReviewReports WHERE ReviewID = @ReviewID)
                WHERE ID = @ReviewID",
                reviewId.ToSqlParam("@ReviewID"));

            return ApiResponse<bool>.SuccessResponse(true, "Review reported successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "REPORT_REVIEW_FAILED", "Failed to report review", ex.Message);
        }
    }

    public async Task<ApiResponse<ReviewResponseDto>> RespondToReviewAsync(
        Guid reviewId, ReviewResponseCreateDto responseDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if review exists
            var reviews = await connection.ExecuteQuery<Review>(
                "SELECT * FROM Reviews WHERE ID = @ID AND Status = 'Approved'",
                reviewId.ToSqlParam("@ID"));

            var review = reviews.FirstOrDefault();
            if (review == null)
            {
                return ApiResponse<ReviewResponseDto>.ErrorResponse(
                    "REVIEW_NOT_FOUND", "Review not found");
            }

            // Check if user has permission to respond (organization member for the listing)
            var hasPermission = await connection.ExecuteQuery<bool>(@"
                SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
                FROM Listings l
                INNER JOIN OrganizationUsers ou ON l.OrganizationID = ou.OrganizationID
                WHERE l.ID = @ListingID AND ou.UserID = @UserID",
                review.ListingID.ToSqlParam("@ListingID"),
                userId.ToSqlParam("@UserID"));

            if (!hasPermission.Any() || !hasPermission.First())
            {
                return ApiResponse<ReviewResponseDto>.ErrorResponse(
                    "PERMISSION_DENIED", "You don't have permission to respond to this review");
            }

            // Check if response already exists
            var existingResponses = await connection.ExecuteQuery<ReviewResponse>(
                "SELECT * FROM ReviewResponses WHERE ReviewID = @ReviewID",
                reviewId.ToSqlParam("@ReviewID"));

            if (existingResponses.Any())
            {
                return ApiResponse<ReviewResponseDto>.ErrorResponse(
                    "RESPONSE_EXISTS", "A response already exists for this review");
            }

            // Create response
            var responseId = Guid.NewGuid();
            await connection.ExecuteNonQueryText(@"
                INSERT INTO ReviewResponses (ID, ReviewID, RespondedBy, ResponseText, CreatedAt, UpdatedAt)
                VALUES (@ID, @ReviewID, @RespondedBy, @ResponseText, @CreatedAt, @UpdatedAt)",
                responseId.ToSqlParam("@ID"),
                reviewId.ToSqlParam("@ReviewID"),
                userId.ToSqlParam("@RespondedBy"),
                responseDto.ResponseText.ToSqlParam("@ResponseText"),
                DateTime.UtcNow.ToSqlParam("@CreatedAt"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"));

            // Get response details
            var responseDetails = await connection.ExecuteQuery<dynamic>(@"
                SELECT rr.ID, rr.ResponseText, rr.CreatedAt,
                       CASE
                           WHEN o.ID IS NOT NULL THEN o.Name
                           ELSE CONCAT(u.FirstName, ' ', u.LastName)
                       END as ResponderName,
                       CASE
                           WHEN o.ID IS NOT NULL THEN 'Organization'
                           ELSE 'User'
                       END as ResponderType,
                       o.LogoURL as ResponderLogo
                FROM ReviewResponses rr
                INNER JOIN Users u ON rr.RespondedBy = u.ID
                LEFT JOIN OrganizationUsers ou ON u.ID = ou.UserID
                LEFT JOIN Organizations o ON ou.OrganizationID = o.ID
                WHERE rr.ID = @ID",
                responseId.ToSqlParam("@ID"));

            var responseDetail = responseDetails.First();
            var response = new ReviewResponseDto
            {
                Id = (Guid)responseDetail.ID,
                ResponseText = responseDetail.ResponseText?.ToString() ?? "",
                RespondedBy = new ReviewResponderDto
                {
                    Name = responseDetail.ResponderName?.ToString() ?? "",
                    Type = responseDetail.ResponderType?.ToString() ?? "",
                    LogoURL = responseDetail.ResponderLogo?.ToString()
                },
                CreatedAt = (DateTime)responseDetail.CreatedAt
            };

            return ApiResponse<ReviewResponseDto>.SuccessResponse(response, "Response created successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<ReviewResponseDto>.ErrorResponse(
                "RESPOND_TO_REVIEW_FAILED", "Failed to respond to review", ex.Message);
        }
    }

    public async Task<ApiResponse<PaginatedResult<ReviewDto>>> GetUserReviewsAsync(
        Guid userId, int page = 1, int limit = 20, string? status = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var offset = (page - 1) * limit;
            var whereConditions = new List<string> { "r.UserID = @UserID" };
            var parameters = new List<SqlParameter> { userId.ToSqlParam("@UserID") };
            var newParameters = new List<SqlParameter> { userId.ToSqlParam("@UserID") };

            if (!string.IsNullOrEmpty(status))
            {
                whereConditions.Add("r.Status = @Status");
                parameters.Add(status.ToSqlParam("@Status"));
                newParameters.Add(status.ToSqlParam("@Status"));
            }

            var whereClause = string.Join(" AND ", whereConditions);

            // Get total count
            var countQuery = $"SELECT COUNT(*) FROM Reviews r WHERE {whereClause}";
            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get reviews
            var query = $@"
                SELECT r.ID, r.ListingID, r.Rating, r.Title, r.ReviewText,
                       r.IslamicComplianceRating, r.Status, r.HelpfulCount,
                       r.CreatedAt, r.UpdatedAt,
                       l.Title as ListingTitle,
                       rr.ID as ResponseId, rr.ResponseText, rr.CreatedAt as ResponseCreatedAt
                FROM Reviews r
                INNER JOIN Listings l ON r.ListingID = l.ID
                LEFT JOIN ReviewResponses rr ON r.ID = rr.ReviewID
                WHERE {whereClause}
                ORDER BY r.CreatedAt DESC
                OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY";

            parameters.Add(offset.ToSqlParam("@Offset"));
            parameters.Add(limit.ToSqlParam("@Limit"));
            newParameters.Add(offset.ToSqlParam("@Offset"));
            newParameters.Add(limit.ToSqlParam("@Limit"));
            

            var reviewData = await connection.ExecuteQuery<dynamic>(query, newParameters.ToArray());

            var reviews = reviewData.Select(r => new ReviewDto
            {
                Id = (Guid)r.ID,
                ListingID = (Guid)r.ListingID,
                ListingTitle = r.ListingTitle?.ToString(),
                Rating = (int)r.Rating,
                Title = r.Title?.ToString(),
                ReviewText = r.ReviewText?.ToString(),
                IslamicComplianceRating = r.IslamicComplianceRating as int?,
                Status = r.Status?.ToString() ?? "",
                HelpfulCount = (int)r.HelpfulCount,
                Response = r.ResponseId != null ? new ReviewResponseDto
                {
                    Id = (Guid)r.ResponseId,
                    ResponseText = r.ResponseText?.ToString() ?? "",
                    CreatedAt = (DateTime)r.ResponseCreatedAt
                } : null,
                CreatedAt = (DateTime)r.CreatedAt,
                UpdatedAt = (DateTime)r.UpdatedAt
            }).ToList();

            var result = new PaginatedResult<ReviewDto>
            {
                Items = reviews,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<ReviewDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<ReviewDto>>.ErrorResponse(
                "GET_USER_REVIEWS_FAILED", "Failed to get user reviews", ex.Message);
        }
    }

    // Helper methods
    public async Task<ApiResponse<ReviewSummaryDto>> GetReviewSummaryAsync(Guid listingId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var summaryData = await connection.ExecuteQuery<dynamic>(@"
                SELECT
                    AVG(CAST(Rating as FLOAT)) as AverageRating,
                    AVG(CAST(IslamicComplianceRating as FLOAT)) as IslamicComplianceAverage,
                    COUNT(*) as TotalReviews
                FROM Reviews
                WHERE ListingID = @ListingID AND Status = 'Approved'",
                listingId.ToSqlParam("@ListingID"));

            var summary = summaryData.First();

            // Get rating distribution
            var distributionData = await connection.ExecuteQuery<dynamic>(@"
                SELECT Rating, COUNT(*) as Count
                FROM Reviews
                WHERE ListingID = @ListingID AND Status = 'Approved'
                GROUP BY Rating",
                listingId.ToSqlParam("@ListingID"));

            var distribution = distributionData.ToDictionary(
                d => (int)d.Rating,
                d => (int)d.Count);

            var result = new ReviewSummaryDto
            {
                AverageRating = summary.AverageRating != null ? (decimal)summary.AverageRating : 0,
                IslamicComplianceAverage = summary.IslamicComplianceAverage != null ? (decimal)summary.IslamicComplianceAverage : 0,
                TotalReviews = (int)summary.TotalReviews,
                RatingDistribution = distribution
            };

            return ApiResponse<ReviewSummaryDto>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<ReviewSummaryDto>.ErrorResponse(
                "GET_REVIEW_SUMMARY_FAILED", "Failed to get review summary", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> CanUserReviewListingAsync(Guid userId, Guid listingId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if listing exists and is active
            var listings = await connection.ExecuteQuery<dynamic>(
                "SELECT Status FROM Listings WHERE ID = @ID",
                listingId.ToSqlParam("@ID"));

            var listing = listings.FirstOrDefault();
            if (listing == null || listing.Status?.ToString() != "Active")
            {
                return ApiResponse<bool>.SuccessResponse(false);
            }

            // User can review if they haven't already reviewed this listing
            var existingReviews = await connection.ExecuteQuery<Review>(
                "SELECT * FROM Reviews WHERE UserID = @UserID AND ListingID = @ListingID",
                userId.ToSqlParam("@UserID"),
                listingId.ToSqlParam("@ListingID"));

            return ApiResponse<bool>.SuccessResponse(!existingReviews.Any());
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "CHECK_REVIEW_PERMISSION_FAILED", "Failed to check review permission", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> CanUserEditReviewAsync(Guid userId, Guid reviewId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var reviews = await connection.ExecuteQuery<Review>(
                "SELECT * FROM Reviews WHERE ID = @ID AND UserID = @UserID",
                reviewId.ToSqlParam("@ID"),
                userId.ToSqlParam("@UserID"));

            var review = reviews.FirstOrDefault();
            if (review == null)
            {
                return ApiResponse<bool>.SuccessResponse(false);
            }

            // Can edit if review is pending or within 24 hours of creation
            var canEdit = review.Status == "Pending" ||
                         review.CreatedAt > DateTime.UtcNow.AddHours(-24);

            return ApiResponse<bool>.SuccessResponse(canEdit);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "CHECK_EDIT_PERMISSION_FAILED", "Failed to check edit permission", ex.Message);
        }
    }

    private async Task<ApiResponse<ReviewDto>> GetReviewByIdAsync(Guid reviewId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var reviewData = await connection.ExecuteQuery<dynamic>(@"
                SELECT r.ID, r.ListingID, r.Rating, r.Title, r.ReviewText,
                       r.IslamicComplianceRating, r.Status, r.HelpfulCount,
                       r.CreatedAt, r.UpdatedAt,
                       u.ID as AuthorId, u.FirstName, u.LastName, u.ProfilePicture,
                       l.Title as ListingTitle
                FROM Reviews r
                INNER JOIN Users u ON r.UserID = u.ID
                INNER JOIN Listings l ON r.ListingID = l.ID
                WHERE r.ID = @ID",
                reviewId.ToSqlParam("@ID"));

            var data = reviewData.FirstOrDefault();
            if (data == null)
            {
                return ApiResponse<ReviewDto>.ErrorResponse("REVIEW_NOT_FOUND", "Review not found");
            }

            var review = new ReviewDto
            {
                Id = (Guid)data.ID,
                ListingID = (Guid)data.ListingID,
                ListingTitle = data.ListingTitle?.ToString(),
                Rating = (int)data.Rating,
                Title = data.Title?.ToString(),
                ReviewText = data.ReviewText?.ToString(),
                IslamicComplianceRating = data.IslamicComplianceRating as int?,
                Status = data.Status?.ToString() ?? "",
                HelpfulCount = (int)data.HelpfulCount,
                Author = new ReviewAuthorDto
                {
                    Id = (Guid)data.AuthorId,
                    DisplayName = $"{data.FirstName} {data.LastName}".Trim(),
                    ProfilePicture = data.ProfilePicture?.ToString()
                },
                CreatedAt = (DateTime)data.CreatedAt,
                UpdatedAt = (DateTime)data.UpdatedAt
            };

            return ApiResponse<ReviewDto>.SuccessResponse(review);
        }
        catch (Exception ex)
        {
            return ApiResponse<ReviewDto>.ErrorResponse(
                "GET_REVIEW_FAILED", "Failed to get review", ex.Message);
        }
    }
}
