using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Search;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;
using MuslimDirectory.API.Models.Entities;
using XGENO.DBHelpers.Core;
using System.Data.SqlClient;
using System.Diagnostics;

namespace MuslimDirectory.API.Services.Implementations;

public class SearchService(IConfiguration configuration) : ISearchService
{
    private readonly string _connectionString = configuration.GetConnectionString("DefaultConnection") 
        ?? throw new ArgumentNullException("Connection string not found");

    public async Task<ApiResponse<PaginatedResult<SearchResultDto>>> SearchAsync(
        SearchDto searchDto, Guid? userId = null, string? sessionId = null)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var results = new List<SearchResultDto>();
            var totalCount = 0;

            // Build search conditions
            var searchConditions = BuildSearchConditions(searchDto);
            var parameters = BuildSearchParameters(searchDto);

            // Search listings
            var listingResults = await SearchListingsAsync(connection, searchConditions, parameters, searchDto);
            results.AddRange(listingResults);

            // Search organizations
            var organizationResults = await SearchOrganizationsAsync(connection, searchConditions, parameters, searchDto);
            results.AddRange(organizationResults);

            // Search categories (if query matches category names)
            var categoryResults = await SearchCategoriesAsync(connection, searchConditions, parameters, searchDto);
            results.AddRange(categoryResults);

            // Apply sorting
            results = ApplySorting(results, searchDto.SortBy);

            // Apply pagination
            totalCount = results.Count;
            var offset = (searchDto.Page - 1) * searchDto.Limit;
            results = results.Skip(offset).Take(searchDto.Limit).ToList();

            stopwatch.Stop();

            // Log search history
            await LogSearchHistoryAsync(connection, searchDto, userId, sessionId, totalCount, (int)stopwatch.ElapsedMilliseconds);

            var paginatedResult = new PaginatedResult<SearchResultDto>
            {
                Items = results,
                TotalCount = totalCount,
                Page = searchDto.Page,
                PageSize = searchDto.Limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / searchDto.Limit)
            };

            return ApiResponse<PaginatedResult<SearchResultDto>>.SuccessResponse(paginatedResult);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<SearchResultDto>>.ErrorResponse(
                "SEARCH_FAILED", "Search operation failed", ex.Message);
        }
    }

    public async Task<ApiResponse<List<SearchSuggestionDto>>> GetSearchSuggestionsAsync(string query, int limit = 10)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var suggestions = new List<SearchSuggestionDto>();

            // Get listing suggestions
            var listingSuggestions = await connection.ExecuteQuery<dynamic>(@"
                SELECT TOP (@Limit) l.ID, l.Title, 'listing' as Type, COUNT(r.ID) as ReviewCount
                FROM Listings l
                LEFT JOIN Reviews r ON l.ID = r.ListingID AND r.Status = 'Approved'
                WHERE l.Status = 'Active' AND l.Title LIKE @Query
                GROUP BY l.ID, l.Title
                ORDER BY COUNT(r.ID) DESC",
                limit.ToSqlParam("@Limit"),
                $"%{query}%".ToSqlParam("@Query"));

            suggestions.AddRange(listingSuggestions.Select(s => new SearchSuggestionDto
            {
                Text = s.Title?.ToString() ?? "",
                Type = "listing",
                Count = s.ReviewCount != null ? (int)s.ReviewCount : 0,
                Id = (Guid)s.ID
            }));

            // Get category suggestions
            var categorySuggestions = await connection.ExecuteQuery<dynamic>(@"
                SELECT TOP (@Limit) c.ID, c.Name, 'category' as Type, COUNT(l.ID) as ListingCount
                FROM Categories c
                LEFT JOIN Listings l ON c.ID = l.CategoryID AND l.Status = 'Active'
                WHERE c.IsActive = 1 AND c.Name LIKE @Query
                GROUP BY c.ID, c.Name
                ORDER BY COUNT(l.ID) DESC",
                Math.Min(limit - suggestions.Count, 5).ToSqlParam("@Limit"),
                $"%{query}%".ToSqlParam("@Query"));

            suggestions.AddRange(categorySuggestions.Select(s => new SearchSuggestionDto
            {
                Text = s.Name?.ToString() ?? "",
                Type = "category",
                Count = s.ListingCount != null ? (int)s.ListingCount : 0,
                Id = (Guid)s.ID
            }));

            // Get organization suggestions
            if (suggestions.Count < limit)
            {
                var organizationSuggestions = await connection.ExecuteQuery<dynamic>(@"
                    SELECT TOP (@Limit) o.ID, o.Name, 'organization' as Type, COUNT(l.ID) as ListingCount
                    FROM Organizations o
                    LEFT JOIN Listings l ON o.ID = l.OrganizationID AND l.Status = 'Active'
                    WHERE o.IsActive = 1 AND o.Name LIKE @Query
                    GROUP BY o.ID, o.Name
                    ORDER BY COUNT(l.ID) DESC",
                    (limit - suggestions.Count).ToSqlParam("@Limit"),
                    $"%{query}%".ToSqlParam("@Query"));

                suggestions.AddRange(organizationSuggestions.Select(s => new SearchSuggestionDto
                {
                    Text = s.Name?.ToString() ?? "",
                    Type = "organization",
                    Count = s.ListingCount != null ? (int)s.ListingCount : 0,
                    Id = (Guid)s.ID
                }));
            }

            // Get popular search suggestions from search history
            if (suggestions.Count < limit)
            {
                var popularSuggestions = await connection.ExecuteQuery<dynamic>(@"
                    SELECT TOP (@Limit) Query, COUNT(*) as SearchCount, 'query' as Type
                    FROM SearchHistory
                    WHERE Query LIKE @Query AND Query != @ExactQuery
                    GROUP BY Query
                    ORDER BY COUNT(*) DESC",
                    (limit - suggestions.Count).ToSqlParam("@Limit"),
                    $"%{query}%".ToSqlParam("@Query"),
                    query.ToSqlParam("@ExactQuery"));

                suggestions.AddRange(popularSuggestions.Select(s => new SearchSuggestionDto
                {
                    Text = s.Query?.ToString() ?? "",
                    Type = "query",
                    Count = s.SearchCount != null ? (int)s.SearchCount : 0
                }));
            }

            return ApiResponse<List<SearchSuggestionDto>>.SuccessResponse(suggestions.Take(limit).ToList());
        }
        catch (Exception ex)
        {
            return ApiResponse<List<SearchSuggestionDto>>.ErrorResponse(
                "GET_SUGGESTIONS_FAILED", "Failed to get search suggestions", ex.Message);
        }
    }

    public async Task<ApiResponse<PaginatedResult<RecentSearchDto>>> GetRecentSearchesAsync(
        Guid userId, int page = 1, int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var offset = (page - 1) * limit;

            // Get total count
            var totalCount = (await connection.ExecuteQuery<int>(
                "SELECT COUNT(*) FROM SearchHistory WHERE UserID = @UserID",
                userId.ToSqlParam("@UserID"))).First();

            // Get recent searches
            var recentSearches = await connection.ExecuteQuery<SearchHistory>(@"
                SELECT ID, Query, SearchType, Category, Location, CreatedAt
                FROM SearchHistory
                WHERE UserID = @UserID
                ORDER BY CreatedAt DESC
                OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY",
                userId.ToSqlParam("@UserID"),
                offset.ToSqlParam("@Offset"),
                limit.ToSqlParam("@Limit"));

            var results = recentSearches.Select(s => new RecentSearchDto
            {
                Id = s.Id,
                Query = s.Query,
                Type = s.SearchType,
                Category = s.Category,
                Location = s.Location,
                SearchedAt = s.CreatedAt
            }).ToList();

            var paginatedResult = new PaginatedResult<RecentSearchDto>
            {
                Items = results,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<RecentSearchDto>>.SuccessResponse(paginatedResult);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<RecentSearchDto>>.ErrorResponse(
                "GET_RECENT_SEARCHES_FAILED", "Failed to get recent searches", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> ClearRecentSearchesAsync(Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            await connection.ExecuteNonQueryText(
                "DELETE FROM SearchHistory WHERE UserID = @UserID",
                userId.ToSqlParam("@UserID"));

            return ApiResponse<bool>.SuccessResponse(true, "Recent searches cleared successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "CLEAR_RECENT_SEARCHES_FAILED", "Failed to clear recent searches", ex.Message);
        }
    }

    public async Task<ApiResponse<SearchStatsDto>> GetSearchStatsAsync(SearchDto searchDto)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var stopwatch = Stopwatch.StartNew();

            // Get total counts for each type
            var searchConditions = BuildSearchConditions(searchDto);
            var parameters = BuildSearchParameters(searchDto);

            var listingCount = await GetListingSearchCountAsync(connection, searchConditions, parameters);
            var organizationCount = await GetOrganizationSearchCountAsync(connection, searchConditions, parameters);
            var categoryCount = await GetCategorySearchCountAsync(connection, searchConditions, parameters);

            stopwatch.Stop();

            var stats = new SearchStatsDto
            {
                TotalResults = listingCount + organizationCount + categoryCount,
                ListingResults = listingCount,
                OrganizationResults = organizationCount,
                CategoryResults = categoryCount,
                SearchDuration = stopwatch.Elapsed
            };

            return ApiResponse<SearchStatsDto>.SuccessResponse(stats);
        }
        catch (Exception ex)
        {
            return ApiResponse<SearchStatsDto>.ErrorResponse(
                "GET_SEARCH_STATS_FAILED", "Failed to get search statistics", ex.Message);
        }
    }

    public async Task<ApiResponse<List<PopularSearchDto>>> GetPopularSearchesAsync(int limit = 10)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var popularSearches = await connection.ExecuteQuery<dynamic>(@"
                SELECT TOP (@Limit) Query, COUNT(*) as SearchCount,
                       ISNULL(Category, '') as Category, MAX(CreatedAt) as LastSearched
                FROM SearchHistory
                WHERE CreatedAt >= DATEADD(day, -30, GETDATE())
                GROUP BY Query, Category
                ORDER BY COUNT(*) DESC",
                limit.ToSqlParam("@Limit"));

            var results = popularSearches.Select(s => new PopularSearchDto
            {
                Query = s.Query?.ToString() ?? "",
                SearchCount = s.SearchCount != null ? (int)s.SearchCount : 0,
                Category = s.Category?.ToString() ?? "",
                LastSearched = (DateTime)s.LastSearched
            }).ToList();

            return ApiResponse<List<PopularSearchDto>>.SuccessResponse(results);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<PopularSearchDto>>.ErrorResponse(
                "GET_POPULAR_SEARCHES_FAILED", "Failed to get popular searches", ex.Message);
        }
    }

    public async Task<ApiResponse<SearchFiltersDto>> GetSearchFiltersAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get available categories
            var categories = await connection.ExecuteQuery<string>(
                "SELECT Name FROM Categories WHERE IsActive = 1 ORDER BY Name");

            // Get available types (from listings)
            var types = await connection.ExecuteQuery<string>(@"
                SELECT DISTINCT Type FROM Listings
                WHERE Status = 'Active' AND Type IS NOT NULL
                ORDER BY Type");

            // Get available locations (cities/states)
            var locations = await connection.ExecuteQuery<string>(@"
                SELECT DISTINCT City FROM Listings
                WHERE Status = 'Active' AND City IS NOT NULL
                ORDER BY City");

            var filters = new SearchFiltersDto
            {
                Categories = categories.ToList(),
                Types = types.ToList(),
                Locations = locations.ToList(),
                MinRating = 1,
                MaxDistance = 50,
                IsVerified = null,
                IsFeatured = null,
                IsOpen = null
            };

            return ApiResponse<SearchFiltersDto>.SuccessResponse(filters);
        }
        catch (Exception ex)
        {
            return ApiResponse<SearchFiltersDto>.ErrorResponse(
                "GET_SEARCH_FILTERS_FAILED", "Failed to get search filters", ex.Message);
        }
    }

    // Private helper methods
    private string BuildSearchConditions(SearchDto searchDto)
    {
        var conditions = new List<string>();

        if (!string.IsNullOrEmpty(searchDto.Query))
        {
            conditions.Add("(Title LIKE @Query OR Description LIKE @Query)");
        }

        if (!string.IsNullOrEmpty(searchDto.Type))
        {
            conditions.Add("Type = @Type");
        }

        if (!string.IsNullOrEmpty(searchDto.Category))
        {
            conditions.Add("CategoryName = @Category");
        }

        if (!string.IsNullOrEmpty(searchDto.Location))
        {
            conditions.Add("(City LIKE @Location OR State LIKE @Location OR Country LIKE @Location)");
        }

        conditions.Add("Status = 'Active'");

        return conditions.Any() ? string.Join(" AND ", conditions) : "1=1";
    }

    private List<SqlParameter> BuildSearchParameters(SearchDto searchDto)
    {
        var parameters = new List<SqlParameter>();

        if (!string.IsNullOrEmpty(searchDto.Query))
        {
            parameters.Add($"%{searchDto.Query}%".ToSqlParam("@Query"));
        }

        if (!string.IsNullOrEmpty(searchDto.Type))
        {
            parameters.Add(searchDto.Type.ToSqlParam("@Type"));
        }

        if (!string.IsNullOrEmpty(searchDto.Category))
        {
            parameters.Add(searchDto.Category.ToSqlParam("@Category"));
        }

        if (!string.IsNullOrEmpty(searchDto.Location))
        {
            parameters.Add($"%{searchDto.Location}%".ToSqlParam("@Location"));
        }

        return parameters;
    }

    private async Task<List<SearchResultDto>> SearchListingsAsync(
        SqlConnection connection, string conditions, List<SqlParameter> parameters, SearchDto searchDto)
    {
        var query = $@"
            SELECT l.ID, l.Title, l.Description, l.ImageURL, l.Type, l.City, l.State, l.Country,
                   l.Latitude, l.Longitude, l.IsVerified, l.IsFeatured,
                   c.Name as CategoryName,
                   AVG(CAST(r.Rating as FLOAT)) as AverageRating,
                   COUNT(r.ID) as ReviewCount
            FROM Listings l
            LEFT JOIN Categories c ON l.CategoryID = c.ID
            LEFT JOIN Reviews r ON l.ID = r.ListingID AND r.Status = 'Approved'
            WHERE {conditions.Replace("CategoryName", "c.Name")}
            GROUP BY l.ID, l.Title, l.Description, l.ImageURL, l.Type, l.City, l.State, l.Country,
                     l.Latitude, l.Longitude, l.IsVerified, l.IsFeatured, c.Name";

        var results = await connection.ExecuteQuery<dynamic>(query, parameters.ToArray());

        return results.Select(r => new SearchResultDto
        {
            Type = "listing",
            Id = (Guid)r.ID,
            Title = r.Title?.ToString() ?? "",
            Description = r.Description?.ToString(),
            ImageUrl = r.ImageURL?.ToString(),
            Category = r.CategoryName?.ToString(),
            Location = $"{r.City}, {r.State}".Trim(' ', ','),
            Rating = r.AverageRating != null ? (decimal)r.AverageRating : null,
            ReviewCount = r.ReviewCount != null ? (int)r.ReviewCount : 0,
            IsVerified = r.IsVerified != null && (bool)r.IsVerified,
            IsFeatured = r.IsFeatured != null && (bool)r.IsFeatured,
            Distance = CalculateDistance(searchDto.Latitude, searchDto.Longitude,
                r.Latitude as decimal?, r.Longitude as decimal?)
        }).ToList();
    }

    private async Task<List<SearchResultDto>> SearchOrganizationsAsync(
        SqlConnection connection, string conditions, List<SqlParameter> parameters, SearchDto searchDto)
    {
        var query = $@"
            SELECT o.ID, o.Name as Title, o.Description, o.LogoURL as ImageURL,
                   o.City, o.State, o.Country, o.Latitude, o.Longitude, o.IsVerified,
                   COUNT(l.ID) as ListingCount
            FROM Organizations o
            LEFT JOIN Listings l ON o.ID = l.OrganizationID AND l.Status = 'Active'
            WHERE o.IsActive = 1 AND {conditions.Replace("Title", "o.Name").Replace("CategoryName", "''")}
            GROUP BY o.ID, o.Name, o.Description, o.LogoURL, o.City, o.State, o.Country,
                     o.Latitude, o.Longitude, o.IsVerified";

        var results = await connection.ExecuteQuery<dynamic>(query, parameters.ToArray());

        return results.Select(r => new SearchResultDto
        {
            Type = "organization",
            Id = (Guid)r.ID,
            Title = r.Title?.ToString() ?? "",
            Description = r.Description?.ToString(),
            ImageUrl = r.ImageURL?.ToString(),
            Location = $"{r.City}, {r.State}".Trim(' ', ','),
            IsVerified = r.IsVerified != null && (bool)r.IsVerified,
            Distance = CalculateDistance(searchDto.Latitude, searchDto.Longitude,
                r.Latitude as decimal?, r.Longitude as decimal?),
            AdditionalData = new Dictionary<string, object>
            {
                ["ListingCount"] = r.ListingCount != null ? (int)r.ListingCount : 0
            }
        }).ToList();
    }

    private async Task<List<SearchResultDto>> SearchCategoriesAsync(
        SqlConnection connection, string conditions, List<SqlParameter> parameters, SearchDto searchDto)
    {
        if (string.IsNullOrEmpty(searchDto.Query))
            return new List<SearchResultDto>();

        var query = @"
            SELECT c.ID, c.Name as Title, c.Description, c.IconURL as ImageURL,
                   COUNT(l.ID) as ListingCount
            FROM Categories c
            LEFT JOIN Listings l ON c.ID = l.CategoryID AND l.Status = 'Active'
            WHERE c.IsActive = 1 AND c.Name LIKE @Query
            GROUP BY c.ID, c.Name, c.Description, c.IconURL";

        var results = await connection.ExecuteQuery<dynamic>(query,
            $"%{searchDto.Query}%".ToSqlParam("@Query"));

        return results.Select(r => new SearchResultDto
        {
            Type = "category",
            Id = (Guid)r.ID,
            Title = r.Title?.ToString() ?? "",
            Description = r.Description?.ToString(),
            ImageUrl = r.ImageURL?.ToString(),
            AdditionalData = new Dictionary<string, object>
            {
                ["ListingCount"] = r.ListingCount != null ? (int)r.ListingCount : 0
            }
        }).ToList();
    }

    private List<SearchResultDto> ApplySorting(List<SearchResultDto> results, string? sortBy)
    {
        return sortBy?.ToLower() switch
        {
            "rating" => results.OrderByDescending(r => r.Rating ?? 0).ToList(),
            "distance" => results.OrderBy(r => r.Distance ?? decimal.MaxValue).ToList(),
            "reviews" => results.OrderByDescending(r => r.ReviewCount ?? 0).ToList(),
            "verified" => results.OrderByDescending(r => r.IsVerified).ToList(),
            "featured" => results.OrderByDescending(r => r.IsFeatured).ToList(),
            "alphabetical" => results.OrderBy(r => r.Title).ToList(),
            _ => results.OrderByDescending(r => r.IsFeatured)
                       .ThenByDescending(r => r.IsVerified)
                       .ThenByDescending(r => r.Rating ?? 0)
                       .ToList()
        };
    }

    private decimal? CalculateDistance(decimal? lat1, decimal? lon1, decimal? lat2, decimal? lon2)
    {
        if (!lat1.HasValue || !lon1.HasValue || !lat2.HasValue || !lon2.HasValue)
            return null;

        const double R = 6371; // Earth's radius in kilometers
        var dLat = ToRadians((double)(lat2 - lat1));
        var dLon = ToRadians((double)(lon2 - lon1));
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians((double)lat1)) * Math.Cos(ToRadians((double)lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return (decimal)(R * c);
    }

    private double ToRadians(double degrees) => degrees * Math.PI / 180;

    private async Task LogSearchHistoryAsync(SqlConnection connection, SearchDto searchDto,
        Guid? userId, string? sessionId, int resultsCount, int duration)
    {
        try
        {
            await connection.ExecuteNonQueryText(@"
                INSERT INTO SearchHistory (ID, UserID, SessionID, Query, SearchType, Category, Location,
                    Latitude, Longitude, Radius, ResultsCount, SearchDuration, CreatedAt)
                VALUES (@ID, @UserID, @SessionID, @Query, @SearchType, @Category, @Location,
                    @Latitude, @Longitude, @Radius, @ResultsCount, @SearchDuration, @CreatedAt)",
                Guid.NewGuid().ToSqlParam("@ID"),
                userId.ToSqlParam("@UserID"),
                sessionId.ToSqlParam("@SessionID"),
                searchDto.Query.ToSqlParam("@Query"),
                searchDto.Type.ToSqlParam("@SearchType"),
                searchDto.Category.ToSqlParam("@Category"),
                searchDto.Location.ToSqlParam("@Location"),
                searchDto.Latitude.ToSqlParam("@Latitude"),
                searchDto.Longitude.ToSqlParam("@Longitude"),
                searchDto.Radius.ToSqlParam("@Radius"),
                resultsCount.ToSqlParam("@ResultsCount"),
                duration.ToSqlParam("@SearchDuration"),
                DateTime.UtcNow.ToSqlParam("@CreatedAt"));
        }
        catch
        {
            // Ignore logging errors - don't fail the search
        }
    }

    private async Task<int> GetListingSearchCountAsync(SqlConnection connection, string conditions, List<SqlParameter> parameters)
    {
        var query = $@"
            SELECT COUNT(DISTINCT l.ID)
            FROM Listings l
            LEFT JOIN Categories c ON l.CategoryID = c.ID
            WHERE {conditions.Replace("CategoryName", "c.Name")}";

        var result = await connection.ExecuteQuery<int>(query, parameters.ToArray());
        return result.First();
    }

    private async Task<int> GetOrganizationSearchCountAsync(SqlConnection connection, string conditions, List<SqlParameter> parameters)
    {
        var query = $@"
            SELECT COUNT(DISTINCT o.ID)
            FROM Organizations o
            WHERE o.IsActive = 1 AND {conditions.Replace("Title", "o.Name").Replace("CategoryName", "''")}";

        var result = await connection.ExecuteQuery<int>(query, parameters.ToArray());
        return result.First();
    }

    private async Task<int> GetCategorySearchCountAsync(SqlConnection connection, string conditions, List<SqlParameter> parameters)
    {
        if (parameters.All(p => p.ParameterName != "@Query"))
            return 0;

        var query = @"
            SELECT COUNT(*)
            FROM Categories c
            WHERE c.IsActive = 1 AND c.Name LIKE @Query";

        var result = await connection.ExecuteQuery<int>(query,
            parameters.First(p => p.ParameterName == "@Query"));
        return result.First();
    }
}
