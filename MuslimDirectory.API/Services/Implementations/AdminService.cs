using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Admin;
using MuslimDirectory.API.Models.Common;
using XGENO.DBHelpers.Core;
using System.Data.SqlClient;
using System.Text;

namespace MuslimDirectory.API.Services.Implementations;

public class AdminService(IConfiguration configuration) : IAdminService
{
    private readonly string _connectionString = configuration.GetConnectionString("DefaultConnection") 
        ?? throw new ArgumentNullException("Connection string not found");

    public async Task<ApiResponse<AdminDashboardDto>> GetDashboardAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var dashboard = new AdminDashboardDto();

            // Get statistics
            dashboard.Stats = await GetAdminStatsAsync(connection);

            // Get recent activity
            dashboard.RecentActivity = await GetRecentActivityAsync(connection);

            // Get pending moderation items
            var moderationResult = await GetModerationQueueAsync("", "Pending", "", 1, 10);
            if (moderationResult.Success && moderationResult.Data != null)
            {
                dashboard.PendingModeration = moderationResult.Data.Items.ToList();
            }

            // Get system status
            dashboard.SystemStatus = await GetSystemStatusAsync(connection);

            // Get alerts
            dashboard.Alerts = await GetAlertsAsync(connection);

            return ApiResponse<AdminDashboardDto>.SuccessResponse(dashboard);
        }
        catch (Exception ex)
        {
            return ApiResponse<AdminDashboardDto>.ErrorResponse(
                "GET_DASHBOARD_FAILED", "Failed to get admin dashboard", ex.Message);
        }
    }

    public async Task<ApiResponse<PaginatedResult<AdminModerationItemDto>>> GetModerationQueueAsync(
        string? type = null, string? status = null, string? priority = null, 
        int page = 1, int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            // Build WHERE clause
            if (!string.IsNullOrEmpty(type))
            {
                whereConditions.Add("Type = @Type");
                parameters.Add(type.ToSqlParam("@Type"));
            }

            if (!string.IsNullOrEmpty(status))
            {
                whereConditions.Add("Status = @Status");
                parameters.Add(status.ToSqlParam("@Status"));
            }

            if (!string.IsNullOrEmpty(priority))
            {
                whereConditions.Add("Priority = @Priority");
                parameters.Add(priority.ToSqlParam("@Priority"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*) 
                FROM (
                    SELECT 'Listing' as Type, l.ID, l.Title, l.ShortDescription as Description, l.Status,
                           'Medium' as Priority, u.FirstName + ' ' + u.LastName as SubmittedBy,
                           l.CreatedAt as SubmittedAt, NULL as Reason
                    FROM Listings l
                    INNER JOIN Users u ON l.SubmittedBy = u.ID
                    WHERE l.Status = 'Pending'
                    
                    UNION ALL
                    
                    SELECT 'Review' as Type, r.ID, 'Review for ' + l.Title as Title, r.ReviewText as Description,
                           r.Status, 'Low' as Priority, u.FirstName + ' ' + u.LastName as SubmittedBy,
                           r.CreatedAt as SubmittedAt, NULL as Reason
                    FROM Reviews r
                    INNER JOIN Listings l ON r.ListingID = l.ID
                    INNER JOIN Users u ON r.UserID = u.ID
                    WHERE r.Status = 'Pending'
                    
                    UNION ALL
                    
                    SELECT 'Report' as Type, rp.ID, 'Report: ' + rp.Reason as Title, rp.Description, 
                           rp.Status, 'High' as Priority, u.FirstName + ' ' + u.LastName as SubmittedBy,
                           rp.CreatedAt as SubmittedAt, rp.Reason
                    FROM Reports rp
                    INNER JOIN Users u ON rp.ReporterID = u.ID
                    WHERE rp.Status IN ('Pending', 'Investigating')
                ) AS ModerationItems
                {whereClause}";

            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get paginated data
            var offset = (page - 1) * limit;
            var dataQuery = $@"
                SELECT Type, ID, Title, Description, Status, Priority, SubmittedBy, SubmittedAt, Reason
                FROM (
                    SELECT 'Listing' as Type, l.ID, l.Title, l.ShortDescription as Description, l.Status,
                           'Medium' as Priority, u.FirstName + ' ' + u.LastName as SubmittedBy,
                           l.CreatedAt as SubmittedAt, NULL as Reason
                    FROM Listings l
                    INNER JOIN Users u ON l.SubmittedBy = u.ID
                    WHERE l.Status = 'Pending'
                    
                    UNION ALL
                    
                    SELECT 'Review' as Type, r.ID, 'Review for ' + l.Title as Title, r.ReviewText as Description,
                           r.Status, 'Low' as Priority, u.FirstName + ' ' + u.LastName as SubmittedBy,
                           r.CreatedAt as SubmittedAt, NULL as Reason
                    FROM Reviews r
                    INNER JOIN Listings l ON r.ListingID = l.ID
                    INNER JOIN Users u ON r.UserID = u.ID
                    WHERE r.Status = 'Pending'
                    
                    UNION ALL
                    
                    SELECT 'Report' as Type, rp.ID, 'Report: ' + rp.Reason as Title, rp.Description, 
                           rp.Status, 'High' as Priority, u.FirstName + ' ' + u.LastName as SubmittedBy,
                           rp.CreatedAt as SubmittedAt, rp.Reason
                    FROM Reports rp
                    INNER JOIN Users u ON rp.ReporterID = u.ID
                    WHERE rp.Status IN ('Pending', 'Investigating')
                ) AS ModerationItems
                {whereClause}
                ORDER BY 
                    CASE Priority 
                        WHEN 'Critical' THEN 1 
                        WHEN 'High' THEN 2 
                        WHEN 'Medium' THEN 3 
                        WHEN 'Low' THEN 4 
                    END,
                    SubmittedAt DESC
                OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY";

            var items = await connection.ExecuteQuery<dynamic>(dataQuery, parameters.ToArray());

            var moderationItems = items.Select(item => new AdminModerationItemDto
            {
                Id = item.ID,
                Type = item.Type?.ToString() ?? "",
                Title = item.Title?.ToString() ?? "",
                Description = item.Description?.ToString(),
                Status = item.Status?.ToString() ?? "",
                Priority = item.Priority?.ToString() ?? "",
                SubmittedBy = item.SubmittedBy?.ToString() ?? "",
                SubmittedAt = item.SubmittedAt != null ? (DateTime)item.SubmittedAt : DateTime.MinValue,
                Reason = item.Reason?.ToString(),
                Details = new Dictionary<string, object>()
            }).ToList();

            var result = new PaginatedResult<AdminModerationItemDto>
            {
                Items = moderationItems,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<AdminModerationItemDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<AdminModerationItemDto>>.ErrorResponse(
                "GET_MODERATION_QUEUE_FAILED", "Failed to get moderation queue", ex.Message);
        }
    }

    public async Task<ApiResponse<PaginatedResult<AdminUserDto>>> GetUsersAsync(
        string? search = null, string? status = null, string? role = null,
        DateTime? createdFrom = null, DateTime? createdTo = null,
        int page = 1, int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            // Build WHERE clause
            if (!string.IsNullOrEmpty(search))
            {
                whereConditions.Add("(u.FirstName LIKE @Search OR u.LastName LIKE @Search OR u.Email LIKE @Search)");
                parameters.Add($"%{search}%".ToSqlParam("@Search"));
            }

            if (!string.IsNullOrEmpty(status))
            {
                whereConditions.Add("u.IsActive = @IsActive");
                parameters.Add((status == "Active" ? true : false).ToSqlParam("@IsActive"));
            }

            if (!string.IsNullOrEmpty(role))
            {
                whereConditions.Add("u.UserType = @UserType");
                parameters.Add(role.ToSqlParam("@UserType"));
            }

            if (createdFrom.HasValue)
            {
                whereConditions.Add("u.CreatedAt >= @CreatedFrom");
                parameters.Add(createdFrom.Value.ToSqlParam("@CreatedFrom"));
            }

            if (createdTo.HasValue)
            {
                whereConditions.Add("u.CreatedAt <= @CreatedTo");
                parameters.Add(createdTo.Value.ToSqlParam("@CreatedTo"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*)
                FROM Users u
                {whereClause}";

            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get paginated data
            var offset = (page - 1) * limit;
            var dataQuery = $@"
                SELECT u.ID, u.FirstName, u.LastName, u.Email, u.PhoneNumber,
                       CASE WHEN u.IsActive = 1 THEN 'Active' ELSE 'Inactive' END as Status,
                       u.IsEmailVerified, u.IsPhoneVerified, u.UserType as Role, u.CreatedAt, u.LastLoginAt,
                       NULL as SuspensionReason, NULL as SuspensionExpiresAt,
                       COUNT(DISTINCT l.ID) as ListingCount,
                       COUNT(DISTINCT r.ID) as ReviewCount,
                       COUNT(DISTINCT rp.ID) as ReportCount
                FROM Users u
                LEFT JOIN Listings l ON u.ID = l.SubmittedBy
                LEFT JOIN Reviews r ON u.ID = r.UserID
                LEFT JOIN Reports rp ON u.ID = rp.ReporterID
                {whereClause}
                GROUP BY u.ID, u.FirstName, u.LastName, u.Email, u.PhoneNumber, u.IsActive,
                         u.IsEmailVerified, u.IsPhoneVerified, u.UserType, u.CreatedAt, u.LastLoginAt
                ORDER BY u.CreatedAt DESC
                OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY";

            var users = await connection.ExecuteQuery<dynamic>(dataQuery, parameters.ToArray());

            var userDtos = users.Select(u => new AdminUserDto
            {
                Id = u.ID,
                FirstName = u.FirstName?.ToString() ?? "",
                LastName = u.LastName?.ToString() ?? "",
                Email = u.Email?.ToString() ?? "",
                PhoneNumber = u.PhoneNumber?.ToString(),
                Status = u.Status?.ToString() ?? "",
                IsEmailVerified = u.IsEmailVerified != null && (bool)u.IsEmailVerified,
                IsPhoneVerified = u.IsPhoneVerified != null && (bool)u.IsPhoneVerified,
                Role = u.Role?.ToString() ?? "",
                CreatedAt = u.CreatedAt != null ? (DateTime)u.CreatedAt : DateTime.MinValue,
                LastLoginAt = u.LastLoginAt as DateTime?,
                ListingCount = u.ListingCount != null ? (int)u.ListingCount : 0,
                ReviewCount = u.ReviewCount != null ? (int)u.ReviewCount : 0,
                ReportCount = u.ReportCount != null ? (int)u.ReportCount : 0,
                SuspensionReason = u.SuspensionReason?.ToString(),
                SuspensionExpiresAt = u.SuspensionExpiresAt as DateTime?
            }).ToList();

            var result = new PaginatedResult<AdminUserDto>
            {
                Items = userDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<AdminUserDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<AdminUserDto>>.ErrorResponse(
                "GET_USERS_FAILED", "Failed to get users", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> UpdateUserStatusAsync(Guid userId, UpdateUserStatusDto updateDto, Guid adminUserId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user exists
            var userExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM Users WHERE ID = @UserId",
                userId.ToSqlParam("@UserId"));

            if (!userExists.Any() || userExists.First() == 0)
            {
                return ApiResponse<bool>.ErrorResponse("USER_NOT_FOUND", "User not found");
            }

            // Update user status
            var parameters = new List<SqlParameter>
            {
                userId.ToSqlParam("@UserId"),
                updateDto.Status.ToSqlParam("@Status"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"),
                adminUserId.ToSqlParam("@UpdatedBy")
            };

            var updateQuery = new StringBuilder(@"
                UPDATE Users
                SET Status = @Status, UpdatedAt = @UpdatedAt, UpdatedBy = @UpdatedBy");

            if (!string.IsNullOrEmpty(updateDto.Reason))
            {
                updateQuery.Append(", SuspensionReason = @Reason");
                parameters.Add(updateDto.Reason.ToSqlParam("@Reason"));
            }

            if (updateDto.SuspensionExpiresAt.HasValue)
            {
                updateQuery.Append(", SuspensionExpiresAt = @SuspensionExpiresAt");
                parameters.Add(updateDto.SuspensionExpiresAt.Value.ToSqlParam("@SuspensionExpiresAt"));
            }

            updateQuery.Append(" WHERE ID = @UserId");

            await connection.ExecuteNonQueryText(updateQuery.ToString(), parameters.ToArray());

            // Log admin action
            await LogAdminActionAsync(connection, adminUserId, "UpdateUserStatus",
                $"Updated user {userId} status to {updateDto.Status}", userId.ToString());

            return ApiResponse<bool>.SuccessResponse(true, "User status updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "UPDATE_USER_STATUS_FAILED", "Failed to update user status", ex.Message);
        }
    }

    public async Task<ApiResponse<PaginatedResult<AdminListingDto>>> GetListingsAsync(
        string? search = null, string? status = null, string? category = null,
        bool? isVerified = null, bool? isFeatured = null,
        DateTime? createdFrom = null, DateTime? createdTo = null,
        int page = 1, int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            // Build WHERE clause
            if (!string.IsNullOrEmpty(search))
            {
                whereConditions.Add("(l.Title LIKE @Search OR l.ShortDescription LIKE @Search OR l.FullDescription LIKE @Search)");
                parameters.Add($"%{search}%".ToSqlParam("@Search"));
            }

            if (!string.IsNullOrEmpty(status))
            {
                whereConditions.Add("l.Status = @Status");
                parameters.Add(status.ToSqlParam("@Status"));
            }

            if (!string.IsNullOrEmpty(category))
            {
                whereConditions.Add("c.Name = @Category");
                parameters.Add(category.ToSqlParam("@Category"));
            }

            if (isVerified.HasValue)
            {
                whereConditions.Add("l.IsVerified = @IsVerified");
                parameters.Add(isVerified.Value.ToSqlParam("@IsVerified"));
            }

            if (isFeatured.HasValue)
            {
                whereConditions.Add("l.IsFeatured = @IsFeatured");
                parameters.Add(isFeatured.Value.ToSqlParam("@IsFeatured"));
            }

            if (createdFrom.HasValue)
            {
                whereConditions.Add("l.CreatedAt >= @CreatedFrom");
                parameters.Add(createdFrom.Value.ToSqlParam("@CreatedFrom"));
            }

            if (createdTo.HasValue)
            {
                whereConditions.Add("l.CreatedAt <= @CreatedTo");
                parameters.Add(createdTo.Value.ToSqlParam("@CreatedTo"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*)
                FROM Listings l
                INNER JOIN Users u ON l.UserID = u.ID
                INNER JOIN Categories c ON l.CategoryID = c.ID
                LEFT JOIN Organizations o ON l.OrganizationID = o.ID
                {whereClause}";

            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get paginated data
            var offset = (page - 1) * limit;
            var dataQuery = $@"
                SELECT l.ID, l.Title, l.ShortDescription as Description, l.Status,
                       CAST(0 as BIT) as IsVerified, CAST(0 as BIT) as IsFeatured,
                       l.CreatedAt, l.UpdatedAt, NULL as ModerationNotes,
                       u.FirstName + ' ' + u.LastName as OwnerName, u.Email as OwnerEmail,
                       COALESCE(pc.Name, 'Uncategorized') as Category, o.Name as OrganizationName,
                       AVG(CAST(r.Rating as FLOAT)) as Rating,
                       COUNT(DISTINCT r.ID) as ReviewCount,
                       COUNT(DISTINCT rp.ID) as ReportCount
                FROM Listings l
                INNER JOIN Users u ON l.SubmittedBy = u.ID
                LEFT JOIN ListingCategories lc ON l.ID = lc.ListingID AND lc.IsPrimary = 1
                LEFT JOIN Categories pc ON lc.CategoryID = pc.ID
                LEFT JOIN Organizations o ON l.OrganizationID = o.ID
                LEFT JOIN Reviews r ON l.ID = r.ListingID AND r.Status = 'Approved'
                LEFT JOIN Reports rp ON l.ID = rp.TargetID AND rp.Type = 'Listing'
                {whereClause}
                GROUP BY l.ID, l.Title, l.ShortDescription, l.Status,
                         l.CreatedAt, l.UpdatedAt,
                         u.FirstName, u.LastName, u.Email, pc.Name, o.Name
                ORDER BY l.CreatedAt DESC
                OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY";

            var listings = await connection.ExecuteQuery<dynamic>(dataQuery, parameters.ToArray());

            var listingDtos = listings.Select(l => new AdminListingDto
            {
                Id = l.ID,
                Title = l.Title?.ToString() ?? "",
                Description = l.Description?.ToString(),
                Category = l.Category?.ToString() ?? "",
                Status = l.Status?.ToString() ?? "",
                OwnerName = l.OwnerName?.ToString() ?? "",
                OwnerEmail = l.OwnerEmail?.ToString() ?? "",
                OrganizationName = l.OrganizationName?.ToString(),
                IsVerified = l.IsVerified != null && (bool)l.IsVerified,
                IsFeatured = l.IsFeatured != null && (bool)l.IsFeatured,
                Rating = l.Rating as double?,
                ReviewCount = l.ReviewCount != null ? (int)l.ReviewCount : 0,
                ReportCount = l.ReportCount != null ? (int)l.ReportCount : 0,
                CreatedAt = l.CreatedAt != null ? (DateTime)l.CreatedAt : DateTime.MinValue,
                LastModifiedAt = l.UpdatedAt as DateTime?,
                ModerationNotes = l.ModerationNotes?.ToString()
            }).ToList();

            var result = new PaginatedResult<AdminListingDto>
            {
                Items = listingDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<AdminListingDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<AdminListingDto>>.ErrorResponse(
                "GET_LISTINGS_FAILED", "Failed to get listings", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> UpdateListingStatusAsync(Guid listingId, UpdateListingStatusDto updateDto, Guid adminUserId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if listing exists
            var listingExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM Listings WHERE ID = @ListingId",
                listingId.ToSqlParam("@ListingId"));

            if (!listingExists.Any() || listingExists.First() == 0)
            {
                return ApiResponse<bool>.ErrorResponse("LISTING_NOT_FOUND", "Listing not found");
            }

            // Update listing status
            var parameters = new List<SqlParameter>
            {
                listingId.ToSqlParam("@ListingId"),
                updateDto.Status.ToSqlParam("@Status"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"),
                adminUserId.ToSqlParam("@UpdatedBy")
            };

            var updateQuery = new StringBuilder(@"
                UPDATE Listings
                SET Status = @Status, UpdatedAt = @UpdatedAt, UpdatedBy = @UpdatedBy");

            if (!string.IsNullOrEmpty(updateDto.ModerationNotes))
            {
                updateQuery.Append(", ModerationNotes = @ModerationNotes");
                parameters.Add(updateDto.ModerationNotes.ToSqlParam("@ModerationNotes"));
            }

            if (updateDto.IsVerified.HasValue)
            {
                updateQuery.Append(", IsVerified = @IsVerified");
                parameters.Add(updateDto.IsVerified.Value.ToSqlParam("@IsVerified"));
            }

            if (updateDto.IsFeatured.HasValue)
            {
                updateQuery.Append(", IsFeatured = @IsFeatured");
                parameters.Add(updateDto.IsFeatured.Value.ToSqlParam("@IsFeatured"));
            }

            updateQuery.Append(" WHERE ID = @ListingId");

            await connection.ExecuteNonQueryText(updateQuery.ToString(), parameters.ToArray());

            // Log admin action
            await LogAdminActionAsync(connection, adminUserId, "UpdateListingStatus",
                $"Updated listing {listingId} status to {updateDto.Status}", listingId.ToString());

            return ApiResponse<bool>.SuccessResponse(true, "Listing status updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "UPDATE_LISTING_STATUS_FAILED", "Failed to update listing status", ex.Message);
        }
    }

    public async Task<ApiResponse<PaginatedResult<AdminReviewDto>>> GetReviewsAsync(
        string? search = null, string? status = null,
        DateTime? createdFrom = null, DateTime? createdTo = null,
        int page = 1, int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            // Build WHERE clause
            if (!string.IsNullOrEmpty(search))
            {
                whereConditions.Add("(l.Title LIKE @Search OR r.Comment LIKE @Search OR u.FirstName LIKE @Search OR u.LastName LIKE @Search)");
                parameters.Add($"%{search}%".ToSqlParam("@Search"));
            }

            if (!string.IsNullOrEmpty(status))
            {
                whereConditions.Add("r.Status = @Status");
                parameters.Add(status.ToSqlParam("@Status"));
            }

            if (createdFrom.HasValue)
            {
                whereConditions.Add("r.CreatedAt >= @CreatedFrom");
                parameters.Add(createdFrom.Value.ToSqlParam("@CreatedFrom"));
            }

            if (createdTo.HasValue)
            {
                whereConditions.Add("r.CreatedAt <= @CreatedTo");
                parameters.Add(createdTo.Value.ToSqlParam("@CreatedTo"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*)
                FROM Reviews r
                INNER JOIN Listings l ON r.ListingID = l.ID
                INNER JOIN Users u ON r.UserID = u.ID
                {whereClause}";

            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get paginated data
            var offset = (page - 1) * limit;
            var dataQuery = $@"
                SELECT r.ID, r.Rating, r.Comment, r.Status, r.CreatedAt, r.ModerationNotes,
                       l.Title as ListingTitle,
                       u.FirstName + ' ' + u.LastName as ReviewerName, u.Email as ReviewerEmail,
                       COUNT(DISTINCT rh.ID) as HelpfulCount,
                       COUNT(DISTINCT rp.ID) as ReportCount,
                       CASE WHEN EXISTS(SELECT 1 FROM ReviewResponses rr WHERE rr.ReviewID = r.ID) THEN 1 ELSE 0 END as HasResponse
                FROM Reviews r
                INNER JOIN Listings l ON r.ListingID = l.ID
                INNER JOIN Users u ON r.UserID = u.ID
                LEFT JOIN ReviewHelpful rh ON r.ID = rh.ReviewID
                LEFT JOIN Reports rp ON r.ID = rp.TargetID AND rp.Type = 'Review'
                {whereClause}
                GROUP BY r.ID, r.Rating, r.Comment, r.Status, r.CreatedAt, r.ModerationNotes,
                         l.Title, u.FirstName, u.LastName, u.Email
                ORDER BY r.CreatedAt DESC
                OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY";

            var reviews = await connection.ExecuteQuery<dynamic>(dataQuery, parameters.ToArray());

            var reviewDtos = reviews.Select(r => new AdminReviewDto
            {
                Id = r.ID,
                ListingTitle = r.ListingTitle?.ToString() ?? "",
                ReviewerName = r.ReviewerName?.ToString() ?? "",
                ReviewerEmail = r.ReviewerEmail?.ToString() ?? "",
                Rating = r.Rating != null ? (int)r.Rating : 0,
                Comment = r.Comment?.ToString(),
                Status = r.Status?.ToString() ?? "",
                CreatedAt = r.CreatedAt != null ? (DateTime)r.CreatedAt : DateTime.MinValue,
                HelpfulCount = r.HelpfulCount != null ? (int)r.HelpfulCount : 0,
                ReportCount = r.ReportCount != null ? (int)r.ReportCount : 0,
                ModerationNotes = r.ModerationNotes?.ToString(),
                HasResponse = r.HasResponse != null && (int)r.HasResponse == 1
            }).ToList();

            var result = new PaginatedResult<AdminReviewDto>
            {
                Items = reviewDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<AdminReviewDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<AdminReviewDto>>.ErrorResponse(
                "GET_REVIEWS_FAILED", "Failed to get reviews", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> UpdateReviewStatusAsync(Guid reviewId, UpdateReviewStatusDto updateDto, Guid adminUserId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if review exists
            var reviewExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM Reviews WHERE ID = @ReviewId",
                reviewId.ToSqlParam("@ReviewId"));

            if (!reviewExists.Any() || reviewExists.First() == 0)
            {
                return ApiResponse<bool>.ErrorResponse("REVIEW_NOT_FOUND", "Review not found");
            }

            // Update review status
            var parameters = new List<SqlParameter>
            {
                reviewId.ToSqlParam("@ReviewId"),
                updateDto.Status.ToSqlParam("@Status"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"),
                adminUserId.ToSqlParam("@UpdatedBy")
            };

            var updateQuery = new StringBuilder(@"
                UPDATE Reviews
                SET Status = @Status, UpdatedAt = @UpdatedAt, UpdatedBy = @UpdatedBy");

            if (!string.IsNullOrEmpty(updateDto.ModerationNotes))
            {
                updateQuery.Append(", ModerationNotes = @ModerationNotes");
                parameters.Add(updateDto.ModerationNotes.ToSqlParam("@ModerationNotes"));
            }

            updateQuery.Append(" WHERE ID = @ReviewId");

            await connection.ExecuteNonQueryText(updateQuery.ToString(), parameters.ToArray());

            // Log admin action
            await LogAdminActionAsync(connection, adminUserId, "UpdateReviewStatus",
                $"Updated review {reviewId} status to {updateDto.Status}", reviewId.ToString());

            return ApiResponse<bool>.SuccessResponse(true, "Review status updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "UPDATE_REVIEW_STATUS_FAILED", "Failed to update review status", ex.Message);
        }
    }

    public async Task<ApiResponse<PaginatedResult<AdminReportDto>>> GetReportsAsync(
        string? type = null, string? status = null, string? priority = null,
        DateTime? createdFrom = null, DateTime? createdTo = null,
        int page = 1, int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            // Build WHERE clause
            if (!string.IsNullOrEmpty(type))
            {
                whereConditions.Add("rp.Type = @Type");
                parameters.Add(type.ToSqlParam("@Type"));
            }

            if (!string.IsNullOrEmpty(status))
            {
                whereConditions.Add("rp.Status = @Status");
                parameters.Add(status.ToSqlParam("@Status"));
            }

            if (!string.IsNullOrEmpty(priority))
            {
                whereConditions.Add("rp.Priority = @Priority");
                parameters.Add(priority.ToSqlParam("@Priority"));
            }

            if (createdFrom.HasValue)
            {
                whereConditions.Add("rp.CreatedAt >= @CreatedFrom");
                parameters.Add(createdFrom.Value.ToSqlParam("@CreatedFrom"));
            }

            if (createdTo.HasValue)
            {
                whereConditions.Add("rp.CreatedAt <= @CreatedTo");
                parameters.Add(createdTo.Value.ToSqlParam("@CreatedTo"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*)
                FROM Reports rp
                INNER JOIN Users u ON rp.ReporterID = u.ID
                {whereClause}";

            var totalCount = (await connection.ExecuteQuery<int>(countQuery, parameters.ToArray())).First();

            // Get paginated data
            var offset = (page - 1) * limit;
            var dataQuery = $@"
                SELECT rp.ID, rp.Type, rp.Reason, rp.Description, rp.Status, rp.Priority,
                       rp.CreatedAt, rp.ResolvedAt, rp.Resolution,
                       u.FirstName + ' ' + u.LastName as ReporterName, u.Email as ReporterEmail,
                       ru.FirstName + ' ' + ru.LastName as ResolvedBy,
                       CASE
                           WHEN rp.Type = 'Listing' THEN l.Title
                           WHEN rp.Type = 'Review' THEN 'Review on ' + l2.Title
                           WHEN rp.Type = 'User' THEN tu.FirstName + ' ' + tu.LastName
                           ELSE 'Unknown'
                       END as TargetTitle
                FROM Reports rp
                INNER JOIN Users u ON rp.ReporterID = u.ID
                LEFT JOIN Users ru ON rp.ResolvedBy = ru.ID
                LEFT JOIN Listings l ON rp.TargetID = l.ID AND rp.Type = 'Listing'
                LEFT JOIN Reviews r ON rp.TargetID = r.ID AND rp.Type = 'Review'
                LEFT JOIN Listings l2 ON r.ListingID = l2.ID
                LEFT JOIN Users tu ON rp.TargetID = tu.ID AND rp.Type = 'User'
                {whereClause}
                ORDER BY
                    CASE rp.Priority
                        WHEN 'Critical' THEN 1
                        WHEN 'High' THEN 2
                        WHEN 'Medium' THEN 3
                        WHEN 'Low' THEN 4
                    END,
                    rp.CreatedAt DESC
                OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY";

            var reports = await connection.ExecuteQuery<dynamic>(dataQuery, parameters.ToArray());

            var reportDtos = reports.Select(r => new AdminReportDto
            {
                Id = r.ID,
                Type = r.Type?.ToString() ?? "",
                TargetTitle = r.TargetTitle?.ToString() ?? "",
                ReporterName = r.ReporterName?.ToString() ?? "",
                ReporterEmail = r.ReporterEmail?.ToString() ?? "",
                Reason = r.Reason?.ToString() ?? "",
                Description = r.Description?.ToString(),
                Status = r.Status?.ToString() ?? "",
                Priority = r.Priority?.ToString() ?? "",
                CreatedAt = r.CreatedAt != null ? (DateTime)r.CreatedAt : DateTime.MinValue,
                ResolvedAt = r.ResolvedAt as DateTime?,
                ResolvedBy = r.ResolvedBy?.ToString(),
                Resolution = r.Resolution?.ToString()
            }).ToList();

            var result = new PaginatedResult<AdminReportDto>
            {
                Items = reportDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = limit,
                TotalPages = (int)Math.Ceiling((double)totalCount / limit)
            };

            return ApiResponse<PaginatedResult<AdminReportDto>>.SuccessResponse(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<PaginatedResult<AdminReportDto>>.ErrorResponse(
                "GET_REPORTS_FAILED", "Failed to get reports", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> UpdateReportStatusAsync(Guid reportId, UpdateReportStatusDto updateDto, Guid adminUserId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if report exists
            var reportExists = await connection.ExecuteQuery<int>(@"
                SELECT COUNT(*) FROM Reports WHERE ID = @ReportId",
                reportId.ToSqlParam("@ReportId"));

            if (!reportExists.Any() || reportExists.First() == 0)
            {
                return ApiResponse<bool>.ErrorResponse("REPORT_NOT_FOUND", "Report not found");
            }

            // Update report status
            var parameters = new List<SqlParameter>
            {
                reportId.ToSqlParam("@ReportId"),
                updateDto.Status.ToSqlParam("@Status"),
                DateTime.UtcNow.ToSqlParam("@UpdatedAt"),
                adminUserId.ToSqlParam("@ResolvedBy")
            };

            var updateQuery = new StringBuilder(@"
                UPDATE Reports
                SET Status = @Status, UpdatedAt = @UpdatedAt, ResolvedBy = @ResolvedBy");

            if (updateDto.Status == "Resolved" || updateDto.Status == "Dismissed")
            {
                updateQuery.Append(", ResolvedAt = @UpdatedAt");
            }

            if (!string.IsNullOrEmpty(updateDto.Resolution))
            {
                updateQuery.Append(", Resolution = @Resolution");
                parameters.Add(updateDto.Resolution.ToSqlParam("@Resolution"));
            }

            if (!string.IsNullOrEmpty(updateDto.Notes))
            {
                updateQuery.Append(", Notes = @Notes");
                parameters.Add(updateDto.Notes.ToSqlParam("@Notes"));
            }

            updateQuery.Append(" WHERE ID = @ReportId");

            await connection.ExecuteNonQueryText(updateQuery.ToString(), parameters.ToArray());

            // Log admin action
            await LogAdminActionAsync(connection, adminUserId, "UpdateReportStatus",
                $"Updated report {reportId} status to {updateDto.Status}", reportId.ToString());

            return ApiResponse<bool>.SuccessResponse(true, "Report status updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse(
                "UPDATE_REPORT_STATUS_FAILED", "Failed to update report status", ex.Message);
        }
    }

    // Helper methods
    private async Task<AdminStatsDto> GetAdminStatsAsync(SqlConnection connection)
    {
        var stats = new AdminStatsDto();

        // Get user statistics
        var userStats = await connection.ExecuteQuery<dynamic>(@"
            SELECT
                COUNT(*) as TotalUsers,
                SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveUsers,
                SUM(CASE WHEN CAST(CreatedAt as DATE) = CAST(GETUTCDATE() as DATE) THEN 1 ELSE 0 END) as NewUsersToday
            FROM Users");

        if (userStats.Any())
        {
            var userStat = userStats.First();
            stats.TotalUsers = userStat.TotalUsers != null ? (long)userStat.TotalUsers : 0;
            stats.ActiveUsers = userStat.ActiveUsers != null ? (long)userStat.ActiveUsers : 0;
            stats.NewUsersToday = userStat.NewUsersToday != null ? (long)userStat.NewUsersToday : 0;
        }

        // Get listing statistics
        var listingStats = await connection.ExecuteQuery<dynamic>(@"
            SELECT
                COUNT(*) as TotalListings,
                SUM(CASE WHEN Status = 'Active' THEN 1 ELSE 0 END) as ActiveListings,
                SUM(CASE WHEN Status = 'Pending' THEN 1 ELSE 0 END) as PendingListings
            FROM Listings");

        if (listingStats.Any())
        {
            var listingStat = listingStats.First();
            stats.TotalListings = listingStat.TotalListings != null ? (long)listingStat.TotalListings : 0;
            stats.ActiveListings = listingStat.ActiveListings != null ? (long)listingStat.ActiveListings : 0;
            stats.PendingListings = listingStat.PendingListings != null ? (long)listingStat.PendingListings : 0;
        }

        // Get review statistics
        var reviewStats = await connection.ExecuteQuery<dynamic>(@"
            SELECT
                COUNT(*) as TotalReviews,
                SUM(CASE WHEN Status = 'Pending' THEN 1 ELSE 0 END) as PendingReviews,
                AVG(CAST(Rating as FLOAT)) as AverageRating
            FROM Reviews");

        if (reviewStats.Any())
        {
            var reviewStat = reviewStats.First();
            stats.TotalReviews = reviewStat.TotalReviews != null ? (long)reviewStat.TotalReviews : 0;
            stats.PendingReviews = reviewStat.PendingReviews != null ? (long)reviewStat.PendingReviews : 0;
            stats.AverageRating = reviewStat.AverageRating != null ? (double)reviewStat.AverageRating : 0.0;
        }

        // Get report statistics
        var reportStats = await connection.ExecuteQuery<dynamic>(@"
            SELECT
                COUNT(*) as TotalReports,
                SUM(CASE WHEN Status IN ('Pending', 'Investigating') THEN 1 ELSE 0 END) as UnresolvedReports
            FROM Reports");

        if (reportStats.Any())
        {
            var reportStat = reportStats.First();
            stats.TotalReports = reportStat.TotalReports != null ? (long)reportStat.TotalReports : 0;
            stats.UnresolvedReports = reportStat.UnresolvedReports != null ? (long)reportStat.UnresolvedReports : 0;
        }

        // Get organization statistics
        var orgStats = await connection.ExecuteQuery<dynamic>(@"
            SELECT
                COUNT(*) as TotalOrganizations,
                SUM(CASE WHEN IsVerified = 1 THEN 1 ELSE 0 END) as VerifiedOrganizations
            FROM Organizations");

        if (orgStats.Any())
        {
            var orgStat = orgStats.First();
            stats.TotalOrganizations = orgStat.TotalOrganizations != null ? (long)orgStat.TotalOrganizations : 0;
            stats.VerifiedOrganizations = orgStat.VerifiedOrganizations != null ? (long)orgStat.VerifiedOrganizations : 0;
        }

        // Get search statistics
        var searchStats = await connection.ExecuteQuery<dynamic>(@"
            SELECT
                COUNT(*) as TotalSearches,
                SUM(CASE WHEN CAST(CreatedAt as DATE) = CAST(GETUTCDATE() as DATE) THEN 1 ELSE 0 END) as SearchesToday
            FROM SearchHistory");

        if (searchStats.Any())
        {
            var searchStat = searchStats.First();
            stats.TotalSearches = searchStat.TotalSearches != null ? (long)searchStat.TotalSearches : 0;
            stats.SearchesToday = searchStat.SearchesToday != null ? (long)searchStat.SearchesToday : 0;
        }

        return stats;
    }

    private async Task<List<AdminRecentActivityDto>> GetRecentActivityAsync(SqlConnection connection)
    {
        var activities = new List<AdminRecentActivityDto>();

        var query = @"
            SELECT TOP 20 'User' as Type, 'Created' as Action,
                   u.FirstName + ' ' + u.LastName as Title,
                   'New user registration' as Description,
                   u.FirstName + ' ' + u.LastName as UserName, u.Email as UserEmail,
                   u.CreatedAt, u.Status
            FROM Users u
            WHERE u.CreatedAt >= DATEADD(day, -7, GETUTCDATE())

            UNION ALL

            SELECT TOP 20 'Listing' as Type, 'Created' as Action,
                   l.Title, l.ShortDescription as Description,
                   u.FirstName + ' ' + u.LastName as UserName, u.Email as UserEmail,
                   l.CreatedAt, l.Status
            FROM Listings l
            INNER JOIN Users u ON l.SubmittedBy = u.ID
            WHERE l.CreatedAt >= DATEADD(day, -7, GETUTCDATE())

            UNION ALL

            SELECT TOP 20 'Review' as Type, 'Created' as Action,
                   'Review for ' + lt.Title as Title, r.Comment as Description,
                   u.FirstName + ' ' + u.LastName as UserName, u.Email as UserEmail,
                   r.CreatedAt, r.Status
            FROM Reviews r
            INNER JOIN Users u ON r.UserID = u.ID
            INNER JOIN Listings lt ON r.ListingID = lt.ID
            WHERE r.CreatedAt >= DATEADD(day, -7, GETUTCDATE())

            ORDER BY CreatedAt DESC";

        var results = await connection.ExecuteQuery<dynamic>(query);

        foreach (var result in results.Take(20))
        {
            activities.Add(new AdminRecentActivityDto
            {
                Id = Guid.NewGuid(), // Generate a temporary ID for display
                Type = result.Type?.ToString() ?? "",
                Action = result.Action?.ToString() ?? "",
                Title = result.Title?.ToString() ?? "",
                Description = result.Description?.ToString(),
                UserName = result.UserName?.ToString() ?? "",
                UserEmail = result.UserEmail?.ToString() ?? "",
                CreatedAt = result.CreatedAt != null ? (DateTime)result.CreatedAt : DateTime.MinValue,
                Status = result.Status?.ToString() ?? "",
                Metadata = new Dictionary<string, object>()
            });
        }

        return activities;
    }

    private async Task<AdminSystemStatusDto> GetSystemStatusAsync(SqlConnection connection)
    {
        // Simulate system metrics - in a real application, these would come from actual monitoring
        return new AdminSystemStatusDto
        {
            Status = "Healthy",
            CpuUsage = 15.5,
            MemoryUsage = 68.2,
            DiskUsage = 45.8,
            ActiveConnections = 25,
            ResponseTime = 125.0,
            LastUpdated = DateTime.UtcNow
        };
    }

    private async Task<List<AdminAlertDto>> GetAlertsAsync(SqlConnection connection)
    {
        var alerts = new List<AdminAlertDto>();

        // Check for high priority reports
        var highPriorityReports = await connection.ExecuteQuery<int>(@"
            SELECT COUNT(*) FROM Reports
            WHERE Status IN ('Pending', 'Investigating') AND Priority IN ('High', 'Critical')");

        if (highPriorityReports.Any() && highPriorityReports.First() > 0)
        {
            alerts.Add(new AdminAlertDto
            {
                Id = Guid.NewGuid(),
                Type = "Warning",
                Title = "High Priority Reports",
                Message = $"You have {highPriorityReports.First()} high priority reports pending review",
                IsRead = false,
                CreatedAt = DateTime.UtcNow,
                ActionUrl = "/admin/reports?priority=High,Critical",
                Data = new Dictionary<string, object> { { "count", highPriorityReports.First() } }
            });
        }

        // Check for pending moderation items
        var pendingModeration = await connection.ExecuteQuery<int>(@"
            SELECT COUNT(*) FROM (
                SELECT ID FROM Listings WHERE Status = 'Pending'
                UNION ALL
                SELECT ID FROM Reviews WHERE Status = 'Pending'
            ) AS PendingItems");

        if (pendingModeration.Any() && pendingModeration.First() > 10)
        {
            alerts.Add(new AdminAlertDto
            {
                Id = Guid.NewGuid(),
                Type = "Info",
                Title = "Pending Moderation",
                Message = $"You have {pendingModeration.First()} items pending moderation",
                IsRead = false,
                CreatedAt = DateTime.UtcNow,
                ActionUrl = "/admin/moderation-queue",
                Data = new Dictionary<string, object> { { "count", pendingModeration.First() } }
            });
        }

        return alerts;
    }

    private async Task LogAdminActionAsync(SqlConnection connection, Guid adminUserId, string action, string description, string? targetId = null)
    {
        try
        {
            var parameters = new List<SqlParameter>
            {
                Guid.NewGuid().ToSqlParam("@Id"),
                adminUserId.ToSqlParam("@AdminUserId"),
                action.ToSqlParam("@Action"),
                description.ToSqlParam("@Description"),
                DateTime.UtcNow.ToSqlParam("@CreatedAt")
            };

            var query = @"
                INSERT INTO AdminActionLogs (ID, AdminUserId, Action, Description, CreatedAt";

            if (!string.IsNullOrEmpty(targetId))
            {
                query += ", TargetId";
                parameters.Add(targetId.ToSqlParam("@TargetId"));
            }

            query += ") VALUES (@Id, @AdminUserId, @Action, @Description, @CreatedAt";

            if (!string.IsNullOrEmpty(targetId))
            {
                query += ", @TargetId";
            }

            query += ")";

            await connection.ExecuteNonQueryText(query, parameters.ToArray());
        }
        catch
        {
            // Log admin actions are not critical - don't fail the main operation if logging fails
        }
    }
}
