using System.Data.SqlClient;
using System.Text.Json;
using MuslimDirectory.API.Models.DTOs.User;
using MuslimDirectory.API.Models.DTOs.Listing;
using MuslimDirectory.API.Models.DTOs.Organization;
using MuslimDirectory.API.Models.DTOs.Category;
using MuslimDirectory.API.Models.Entities;
using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Implementations;

public class UserService(IConfiguration configuration, IPasswordService passwordService) : IUserService
{
    private readonly string _connectionString = configuration.GetConnectionString("DefaultConnection") ?? throw new InvalidOperationException("Connection string not found");

    public async Task<ApiResponse<UserProfileWrapperResponse>> GetUserProfileAsync(Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get user details
            var users = await connection.ExecuteQuery<User>(
                "SELECT * FROM [Users] WHERE [ID] = @UserId AND IsActive = 1",
                userId.ToSqlParam("@UserId")
            );

            var user = users.FirstOrDefault();
            if (user == null)
            {
                return ApiResponse<UserProfileWrapperResponse>.ErrorResponse("USER_NOT_FOUND", "User not found");
            }

            // Get user stats - reviews count
            var reviewsCountResult = await connection.ExecuteQuery<int>(
                "SELECT COUNT(*) FROM [Reviews] WHERE UserID = @UserId",
                userId.ToSqlParam("@UserId")
            );
            var reviewsCount = reviewsCountResult.FirstOrDefault();

            // Get user stats - favorites count
            var favoritesCountResult = await connection.ExecuteQuery<int>(
                "SELECT COUNT(*) FROM [UserFavorites] WHERE UserID = @UserId",
                userId.ToSqlParam("@UserId")
            );
            var favoritesCount = favoritesCountResult.FirstOrDefault();

            // Get user's submitted listings
            var userListingsQuery = @"
                SELECT 
                    l.ID, l.Title, l.ShortDescription, l.LogoURL, l.PlatformType,
                    l.SupportedPlatforms, l.PricingModel, l.Price, l.Currency,
                    l.IslamicComplianceStatus, l.FeaturedLevel, l.ViewCount,
                    l.CreatedAt, l.UpdatedAt,
                    ISNULL(r.AverageRating, 0) as AverageRating,
                    ISNULL(r.ReviewCount, 0) as ReviewCount,
                    o.ID as OrganizationId, o.Name as OrganizationName,
                    o.LogoURL as OrganizationLogoURL, o.IsVerified as OrganizationIsVerified,
                    pc.ID as PrimaryCategoryId, pc.Name as PrimaryCategoryName
                FROM Listings l
                LEFT JOIN Organizations o ON l.OrganizationID = o.ID
                LEFT JOIN (
                    SELECT ListingID, AVG(CAST(Rating AS FLOAT)) as AverageRating, COUNT(*) as ReviewCount
                    FROM Reviews WHERE Status = 'Approved' GROUP BY ListingID
                ) r ON l.ID = r.ListingID
                LEFT JOIN ListingCategories lc ON l.ID = lc.ListingID AND lc.IsPrimary = 1
                LEFT JOIN Categories pc ON lc.CategoryID = pc.ID
                WHERE l.SubmittedBy = @UserId
                ORDER BY l.CreatedAt DESC";

            var userListings = await connection.ExecuteQuery<dynamic>(userListingsQuery,
                userId.ToSqlParam("@UserId"));

            var listings = userListings.Select(l => new ListingDto
            {
                Id = l.ID,
                Title = l.Title ?? "",
                ShortDescription = l.ShortDescription,
                LogoURL = l.LogoURL,
                PlatformType = l.PlatformType,
                SupportedPlatforms = !string.IsNullOrEmpty(l.SupportedPlatforms) 
                    ? JsonSerializer.Deserialize<List<string>>(l.SupportedPlatforms) ?? new List<string>()
                    : new List<string>(),
                PricingModel = l.PricingModel,
                Price = l.Price,
                Currency = l.Currency,
                IslamicComplianceStatus = l.IslamicComplianceStatus ?? "Under Review",
                Rating = new RatingDto
                {
                    Average = l.AverageRating ?? 0,
                    Count = l.ReviewCount ?? 0
                },
                Organization = l.OrganizationId != null ? new OrganizationListDto
                {
                    Id = l.OrganizationId,
                    Name = l.OrganizationName ?? "",
                    LogoURL = l.OrganizationLogoURL,
                    IsVerified = l.OrganizationIsVerified ?? false
                } : null,
                PrimaryCategory = l.PrimaryCategoryId != null ? new CategoryListDto
                {
                    Id = l.PrimaryCategoryId,
                    Name = l.PrimaryCategoryName ?? ""
                } : null,
                FeaturedLevel = l.FeaturedLevel ?? 0,
                ViewCount = l.ViewCount ?? 0,
                CreatedAt = l.CreatedAt,
                UpdatedAt = l.UpdatedAt
            }).ToList();

            var userProfile = new UserProfileResponse
            {
                Id = user.Id,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                ProfilePicture = user.ProfilePicture,
                Gender = user.Gender,
                Country = user.Country,
                City = user.City,
                PhoneNumber = user.PhoneNumber,
                PreferredLanguage = user.PreferredLanguage,
                IsEmailVerified = user.IsEmailVerified,
                IsPhoneVerified = user.IsPhoneVerified,
                IsTermsAccepted = user.IsTermsAccepted,
                UserType = user.UserType,
                MemberSince = user.CreatedAt,
                Stats = new UserStatsResponse
                {
                    ReviewsCount = reviewsCount,
                    FavoritesCount = favoritesCount
                },
                LastLoginAt = user.LastLoginAt,
                Listings = listings
            };

            var response = new UserProfileWrapperResponse
            {
                User = userProfile
            };

            return ApiResponse<UserProfileWrapperResponse>.SuccessResponse(response);
        }
        catch (Exception ex)
        {
            return ApiResponse<UserProfileWrapperResponse>.ErrorResponse("INTERNAL_ERROR", $"An error occurred: {ex.Message}");
        }
    }

    public async Task<ApiResponse<UserProfileWrapperResponse>> UpdateUserProfileAsync(Guid userId, UpdateUserProfileRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user exists
            var userExistsResult = await connection.ExecuteQuery<int>(
                "SELECT COUNT(*) FROM [Users] WHERE [ID] = @UserId AND IsActive = 1",
                userId.ToSqlParam("@UserId")
            );
            var userExists = userExistsResult.Count > 0;

            if (!userExists)
            {
                return ApiResponse<UserProfileWrapperResponse>.ErrorResponse("USER_NOT_FOUND", "User not found");
            }

            // Build dynamic update query
            var updateFields = new List<string>();
            var parameters = new List<SqlParameter> { userId.ToSqlParam("@UserId") };

            if (!string.IsNullOrEmpty(request.FirstName))
            {
                updateFields.Add("FirstName = @FirstName");
                parameters.Add(request.FirstName.ToSqlParam("@FirstName"));
            }

            if (!string.IsNullOrEmpty(request.LastName))
            {
                updateFields.Add("LastName = @LastName");
                parameters.Add(request.LastName.ToSqlParam("@LastName"));
            }

            if (!string.IsNullOrEmpty(request.PhoneNumber))
            {
                updateFields.Add("PhoneNumber = @PhoneNumber");
                parameters.Add(request.PhoneNumber.ToSqlParam("@PhoneNumber"));
            }

            if (!string.IsNullOrEmpty(request.Country))
            {
                updateFields.Add("Country = @Country");
                parameters.Add(request.Country.ToSqlParam("@Country"));
            }

            if (!string.IsNullOrEmpty(request.City))
            {
                updateFields.Add("City = @City");
                parameters.Add(request.City.ToSqlParam("@City"));
            }

            if (!string.IsNullOrEmpty(request.PreferredLanguage))
            {
                updateFields.Add("PreferredLanguage = @PreferredLanguage");
                parameters.Add(request.PreferredLanguage.ToSqlParam("@PreferredLanguage"));
            }

            if (!string.IsNullOrEmpty(request.Gender))
            {
                updateFields.Add("Gender = @Gender");
                parameters.Add(request.Gender.ToSqlParam("@Gender"));
            }

            if (!string.IsNullOrEmpty(request.ProfilePicture))
            {
                updateFields.Add("ProfilePicture = @ProfilePicture");
                parameters.Add(request.ProfilePicture.ToSqlParam("@ProfilePicture"));
            }

            if (updateFields.Count == 0)
            {
                return ApiResponse<UserProfileWrapperResponse>.ErrorResponse("NO_CHANGES", "No fields to update");
            }

            updateFields.Add("UpdatedAt = GETUTCDATE()");

            var query = $"UPDATE [Users] SET {string.Join(", ", updateFields)} WHERE [ID] = @UserId";

            await connection.ExecuteNonQueryText(query, parameters.ToArray());

            // Get the updated user profile to return
            var updatedProfileResult = await GetUserProfileAsync(userId);
            if (!updatedProfileResult.Success)
            {
                return ApiResponse<UserProfileWrapperResponse>.ErrorResponse("PROFILE_FETCH_ERROR", "Profile updated but failed to fetch updated data");
            }

            // Update the message to indicate successful update
            updatedProfileResult.Message = "Profile updated successfully";
            return updatedProfileResult;
        }
        catch (Exception ex)
        {
            return ApiResponse<UserProfileWrapperResponse>.ErrorResponse("INTERNAL_ERROR", $"An error occurred: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> ChangePasswordAsync(Guid userId, ChangePasswordRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get user's current password hash
            var users = await connection.ExecuteQuery<User>(
                "SELECT * FROM [Users] WHERE [ID] = @UserId AND IsActive = 1",
                userId.ToSqlParam("@UserId")
            );

            var user = users.FirstOrDefault();
            if (user == null)
            {
                return ApiResponse<string>.ErrorResponse("USER_NOT_FOUND", "User not found");
            }

            if (string.IsNullOrEmpty(user.PasswordHash))
            {
                return ApiResponse<string>.ErrorResponse("SOCIAL_LOGIN_USER", "Cannot change password for social login users");
            }

            // Verify current password
            if (!passwordService.VerifyPassword(request.CurrentPassword, user.PasswordHash))
            {
                return ApiResponse<string>.ErrorResponse("INVALID_PASSWORD", "Current password is incorrect", null, "currentPassword");
            }

            // Hash new password
            var newPasswordHash = passwordService.HashPassword(request.NewPassword);

            // Update password
            await connection.ExecuteNonQueryText(
                "UPDATE [Users] SET PasswordHash = @PasswordHash, UpdatedAt = GETUTCDATE() WHERE [ID] = @UserId",
                newPasswordHash.ToSqlParam("@PasswordHash"),
                userId.ToSqlParam("@UserId")
            );

            return ApiResponse<string>.SuccessResponse("Password changed successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<string>.ErrorResponse("INTERNAL_ERROR", $"An error occurred: {ex.Message}");
        }
    }

    public async Task<ApiResponse<UserPreferencesResponse>> GetUserPreferencesAsync(Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user exists
            var userExistsResult = await connection.ExecuteQuery<int>(
                "SELECT COUNT(*) FROM [Users] WHERE [ID] = @UserId AND IsActive = 1",
                userId.ToSqlParam("@UserId")
            );
            var userExists = userExistsResult.FirstOrDefault();

            if (userExists == 0)
            {
                return ApiResponse<UserPreferencesResponse>.ErrorResponse("USER_NOT_FOUND", "User not found");
            }

            // Get user preferences
            var preferences = await connection.ExecuteQuery<UserPreference>(
                "SELECT * FROM [UserPreferences] WHERE UserID = @UserId",
                userId.ToSqlParam("@UserId")
            );

            var response = new UserPreferencesResponse();

            // Parse preferences from database
            foreach (var pref in preferences)
            {
                switch (pref.PreferenceKey.ToLower())
                {
                    case "language":
                        response.Language = pref.PreferenceValue ?? "EN";
                        break;
                    case "notifications.newapps":
                        response.Notifications.NewApps = bool.Parse(pref.PreferenceValue ?? "true");
                        break;
                    case "notifications.reviewresponses":
                        response.Notifications.ReviewResponses = bool.Parse(pref.PreferenceValue ?? "true");
                        break;
                    case "notifications.weeklyrecommendations":
                        response.Notifications.WeeklyRecommendations = bool.Parse(pref.PreferenceValue ?? "false");
                        break;
                    case "privacy.profilevisibility":
                        response.Privacy.ProfileVisibility = pref.PreferenceValue ?? "public";
                        break;
                    case "privacy.reviewdisplayname":
                        response.Privacy.ReviewDisplayName = pref.PreferenceValue ?? "firstName";
                        break;
                    case "app.defaultsortorder":
                        response.AppPreferences.DefaultSortOrder = pref.PreferenceValue ?? "rating";
                        break;
                    case "app.preferredplatforms":
                        if (!string.IsNullOrEmpty(pref.PreferenceValue))
                        {
                            response.AppPreferences.PreferredPlatforms = JsonSerializer.Deserialize<List<string>>(pref.PreferenceValue) ?? new List<string> { "iOS", "Android" };
                        }
                        break;
                }
            }

            return ApiResponse<UserPreferencesResponse>.SuccessResponse(response);
        }
        catch (Exception ex)
        {
            return ApiResponse<UserPreferencesResponse>.ErrorResponse("INTERNAL_ERROR", $"An error occurred: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> UpdateUserPreferencesAsync(Guid userId, UpdateUserPreferencesRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user exists
            var userExistsResult = await connection.ExecuteQuery<int>(
                "SELECT COUNT(*) FROM [Users] WHERE [ID] = @UserId AND IsActive = 1",
                userId.ToSqlParam("@UserId")
            );
            var userExists = userExistsResult.FirstOrDefault();

            if (userExists == 0)
            {
                return ApiResponse<string>.ErrorResponse("USER_NOT_FOUND", "User not found");
            }

            // Prepare preferences to update
            var preferencesToUpdate = new Dictionary<string, string?>();

            if (!string.IsNullOrEmpty(request.Language))
            {
                preferencesToUpdate["language"] = request.Language;
            }

            if (request.Notifications != null)
            {
                preferencesToUpdate["notifications.newapps"] = request.Notifications.NewApps.ToString().ToLower();
                preferencesToUpdate["notifications.reviewresponses"] = request.Notifications.ReviewResponses.ToString().ToLower();
                preferencesToUpdate["notifications.weeklyrecommendations"] = request.Notifications.WeeklyRecommendations.ToString().ToLower();
            }

            if (request.Privacy != null)
            {
                preferencesToUpdate["privacy.profilevisibility"] = request.Privacy.ProfileVisibility;
                preferencesToUpdate["privacy.reviewdisplayname"] = request.Privacy.ReviewDisplayName;
            }

            if (request.AppPreferences != null)
            {
                preferencesToUpdate["app.defaultsortorder"] = request.AppPreferences.DefaultSortOrder;
                preferencesToUpdate["app.preferredplatforms"] = JsonSerializer.Serialize(request.AppPreferences.PreferredPlatforms);
            }

            if (preferencesToUpdate.Count == 0)
            {
                return ApiResponse<string>.ErrorResponse("NO_CHANGES", "No preferences to update");
            }

            // Update or insert preferences
            foreach (var pref in preferencesToUpdate)
            {
                await connection.ExecuteNonQueryText(@"
                    MERGE [UserPreferences] AS target
                    USING (SELECT @UserId AS UserID, @PreferenceKey AS PreferenceKey, @PreferenceValue AS PreferenceValue) AS source
                    ON target.UserID = source.UserID AND target.PreferenceKey = source.PreferenceKey
                    WHEN MATCHED THEN
                        UPDATE SET PreferenceValue = source.PreferenceValue, UpdatedAt = GETUTCDATE()
                    WHEN NOT MATCHED THEN
                        INSERT (UserID, PreferenceKey, PreferenceValue, CreatedAt, UpdatedAt)
                        VALUES (source.UserID, source.PreferenceKey, source.PreferenceValue, GETUTCDATE(), GETUTCDATE());",
                    userId.ToSqlParam("@UserId"),
                    pref.Key.ToSqlParam("@PreferenceKey"),
                    pref.Value.ToSqlParam("@PreferenceValue")
                );
            }

            return ApiResponse<string>.SuccessResponse("Preferences updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<string>.ErrorResponse("INTERNAL_ERROR", $"An error occurred: {ex.Message}");
        }
    }
}
