using MuslimDirectory.API.Models.DTOs.Tag;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;
using MuslimDirectory.API.Services.Interfaces;
using System.Data;
using System.Data.SqlClient;
using XGENO.DBHelpers.Core;

namespace MuslimDirectory.API.Services.Implementations;

public class TagService : ITagService
{
    private readonly string _connectionString;

    public TagService(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("Connection string not found");
    }

    public async Task<ApiResponse<List<TagDto>>> GetTagsAsync(
        int page = 1, int pageSize = 10, string? search = null, bool includeInactive = false,
        string? sortBy = null, string? sortOrder = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var offset = (page - 1) * pageSize;
            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            if (!includeInactive)
            {
                whereConditions.Add("t.IsActive = 1");
            }

            if (!string.IsNullOrWhiteSpace(search))
            {
                whereConditions.Add("(t.Name LIKE @Search OR t.Description LIKE @Search)");
                parameters.Add(($"%{search}%").ToSqlParam("@Search"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Determine sort order
            var orderBy = "ORDER BY COUNT(lt.TagID) DESC, t.Name";
            if (!string.IsNullOrWhiteSpace(sortBy))
            {
                var direction = sortOrder?.ToUpper() == "DESC" ? "DESC" : "ASC";
                orderBy = sortBy.ToLower() switch
                {
                    "name" => $"ORDER BY t.Name {direction}",
                    "created" => $"ORDER BY t.CreatedAt {direction}",
                    "usage" => $"ORDER BY COUNT(lt.TagID) {direction}, t.Name",
                    _ => orderBy
                };
            }

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*)
                FROM Tags t
                {whereClause}";

            var totalCountResult = await connection.ExecuteQuery<int>(countQuery, parameters.ToArray());
            var totalCount = totalCountResult.First();

            // Get tags
            var query = $@"
                SELECT 
                    t.ID,
                    t.Name,
                    t.Description,
                    t.IsActive,
                    t.CreatedAt,
                    COUNT(lt.TagID) as UsageCount
                FROM Tags t
                LEFT JOIN ListingTags lt ON t.ID = lt.TagID
                {whereClause}
                GROUP BY t.ID, t.Name, t.Description, t.IsActive, t.CreatedAt
                {orderBy}
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            parameters.Add((offset).ToSqlParam("@Offset"));
            parameters.Add((pageSize).ToSqlParam("@PageSize"));

            var tags = await connection.ExecuteQuery<TagDto>(query, parameters.ToArray());

            return ApiResponse<List<TagDto>>.SuccessResponse(tags.ToList());
        }
        catch (Exception ex)
        {
            return ApiResponse<List<TagDto>>.ErrorResponse("TAGS_FETCH_ERROR", ex.Message);
        }
    }

    public async Task<ApiResponse<TagDto>> GetTagByIdAsync(Guid id)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var query = @"
                SELECT 
                    t.ID,
                    t.Name,
                    t.Description,
                    t.IsActive,
                    t.CreatedAt,
                    COUNT(lt.TagID) as UsageCount
                FROM Tags t
                LEFT JOIN ListingTags lt ON t.ID = lt.TagID
                WHERE t.ID = @ID
                GROUP BY t.ID, t.Name, t.Description, t.IsActive, t.CreatedAt";

            var parameters = new[] { (id).ToSqlParam("@ID") };
            var tags = await connection.ExecuteQuery<TagDto>(query, parameters);

            var tag = tags.FirstOrDefault();
            if (tag == null)
            {
                return ApiResponse<TagDto>.ErrorResponse("Tag not found", "TAG_NOT_FOUND");
            }

            return ApiResponse<TagDto>.SuccessResponse(tag);
        }
        catch (Exception ex)
        {
            return ApiResponse<TagDto>.ErrorResponse("Failed to retrieve tag", ex.Message);
        }
    }

    public async Task<ApiResponse<TagWithListingsDto>> GetTagWithListingsAsync(
        Guid id, int page = 1, int pageSize = 10, string? status = null, string? complianceStatus = null)
    {
        try
        {
            // Get tag details
            var tagResult = await GetTagByIdAsync(id);
            if (!tagResult.Success)
            {
                return ApiResponse<TagWithListingsDto>.ErrorResponse(tagResult.Message, tagResult.Error?.Code);
            }

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var tag = new TagWithListingsDto
            {
                Id = tagResult.Data!.Id,
                Name = tagResult.Data.Name,
                Description = tagResult.Data.Description,
                IsActive = tagResult.Data.IsActive,
                CreatedAt = tagResult.Data.CreatedAt,
                UsageCount = tagResult.Data.UsageCount
            };

            // Build listing query with filters
            var offset = (page - 1) * pageSize;
            var whereConditions = new List<string> { "lt.TagID = @TagID", "l.Status = 'Approved'" };
            var parameters = new List<SqlParameter> { (id).ToSqlParam("@TagID") };

            if (!string.IsNullOrWhiteSpace(status))
            {
                whereConditions.Add("l.Status = @Status");
                parameters.Add((status).ToSqlParam("@Status"));
            }

            if (!string.IsNullOrWhiteSpace(complianceStatus))
            {
                whereConditions.Add("l.IslamicComplianceStatus = @ComplianceStatus");
                parameters.Add((complianceStatus).ToSqlParam("@ComplianceStatus"));
            }

            var whereClause = "WHERE " + string.Join(" AND ", whereConditions);

            // Get listings
            var listingsQuery = $@"
                SELECT 
                    l.ID,
                    l.Title,
                    l.ShortDescription,
                    l.LogoURL,
                    l.PlatformType,
                    l.PricingModel,
                    l.Price,
                    l.Currency,
                    l.IslamicComplianceStatus,
                    l.FeaturedLevel,
                    l.SlugURL,
                    l.CreatedAt,
                    l.ViewCount
                FROM Listings l
                INNER JOIN ListingTags lt ON l.ID = lt.ListingID
                {whereClause}
                ORDER BY l.FeaturedLevel DESC, l.CreatedAt DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            parameters.Add((offset).ToSqlParam("@Offset"));
            parameters.Add((pageSize).ToSqlParam("@PageSize"));

            var listings = await connection.ExecuteQuery<TagListingDto>(listingsQuery, parameters.ToArray());
            tag.Listings = listings.ToList();

            return ApiResponse<TagWithListingsDto>.SuccessResponse(tag);
        }
        catch (Exception ex)
        {
            return ApiResponse<TagWithListingsDto>.ErrorResponse("Failed to retrieve tag with listings", ex.Message);
        }
    }

    public async Task<ApiResponse<TagDto>> CreateTagAsync(CreateTagDto createDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var tagId = Guid.NewGuid();
            var now = DateTime.UtcNow;

            var insertQuery = @"
                INSERT INTO Tags (ID, Name, Description, CreatedAt)
                VALUES (@ID, @Name, @Description, @CreatedAt)";

            var parameters = new[]
            {
                (tagId).ToSqlParam("@ID"),
                (createDto.Name).ToSqlParam("@Name"),
                (createDto.Description).ToSqlParam("@Description"),
                (now).ToSqlParam("@CreatedAt")
            };

            await connection.ExecuteNonQueryText(insertQuery, parameters);

            return await GetTagByIdAsync(tagId);
        }
        catch (Exception ex)
        {
            return ApiResponse<TagDto>.ErrorResponse("Failed to create tag", ex.Message);
        }
    }

    public async Task<ApiResponse<TagDto>> UpdateTagAsync(Guid id, UpdateTagDto updateDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var updateFields = new List<string>();
            var parameters = new List<SqlParameter>();

            if (!string.IsNullOrWhiteSpace(updateDto.Name))
            {
                updateFields.Add("Name = @Name");
                parameters.Add((updateDto.Name).ToSqlParam("@Name"));
            }

            if (updateDto.Description != null)
            {
                updateFields.Add("Description = @Description");
                parameters.Add((updateDto.Description).ToSqlParam("@Description"));
            }

            if (updateDto.IsActive.HasValue)
            {
                updateFields.Add("IsActive = @IsActive");
                parameters.Add((updateDto.IsActive.Value).ToSqlParam("@IsActive"));
            }

            if (updateFields.Count == 0)
            {
                return ApiResponse<TagDto>.ErrorResponse("No fields to update", "NO_FIELDS_TO_UPDATE");
            }

            parameters.Add((id).ToSqlParam("@ID"));

            var updateQuery = $@"
                UPDATE Tags
                SET {string.Join(", ", updateFields)}
                WHERE ID = @ID";

            await connection.ExecuteNonQueryText(updateQuery, parameters.ToArray());

            return await GetTagByIdAsync(id);
        }
        catch (Exception ex)
        {
            return ApiResponse<TagDto>.ErrorResponse("Failed to update tag", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> DeleteTagAsync(Guid id, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if tag has listings
            var listingQuery = "SELECT COUNT(*) FROM ListingTags WHERE TagID = @ID";
            var listingParams = new[] { (id).ToSqlParam("@ID") };
            var listingCountResult = await connection.ExecuteQuery<int>(listingQuery, listingParams);
            var listingCount = listingCountResult.First();

            if (listingCount > 0)
            {
                return ApiResponse<bool>.ErrorResponse("Cannot delete tag with listings", "HAS_LISTINGS");
            }

            var deleteQuery = "DELETE FROM Tags WHERE ID = @ID";
            var deleteParams = new[] { (id).ToSqlParam("@ID") };
            await connection.ExecuteNonQueryText(deleteQuery, deleteParams);

            return ApiResponse<bool>.SuccessResponse(true);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to delete tag", ex.Message);
        }
    }

    public async Task<ApiResponse<List<TagListDto>>> GetPopularTagsAsync(int limit = 20)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var query = @"
                SELECT TOP (@Limit)
                    t.ID,
                    t.Name,
                    t.Description,
                    t.IsActive,
                    COUNT(lt.TagID) as UsageCount
                FROM Tags t
                INNER JOIN ListingTags lt ON t.ID = lt.TagID
                WHERE t.IsActive = 1
                GROUP BY t.ID, t.Name, t.Description, t.IsActive
                ORDER BY COUNT(lt.TagID) DESC, t.Name";

            var parameters = new[] { (limit).ToSqlParam("@Limit") };
            var tags = await connection.ExecuteQuery<TagListDto>(query, parameters);

            return ApiResponse<List<TagListDto>>.SuccessResponse(tags.ToList());
        }
        catch (Exception ex)
        {
            return ApiResponse<List<TagListDto>>.ErrorResponse("POPULAR_TAGS_ERROR", ex.Message);
        }
    }

    public async Task<ApiResponse<List<TagListDto>>> SearchTagsAsync(string query, int limit = 10)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var searchQuery = @"
                SELECT TOP (@Limit)
                    t.ID,
                    t.Name,
                    t.Description,
                    t.IsActive,
                    COUNT(lt.TagID) as UsageCount
                FROM Tags t
                LEFT JOIN ListingTags lt ON t.ID = lt.TagID
                WHERE t.IsActive = 1 AND (t.Name LIKE @Query OR t.Description LIKE @Query)
                GROUP BY t.ID, t.Name, t.Description, t.IsActive
                ORDER BY
                    CASE WHEN t.Name LIKE @ExactQuery THEN 1 ELSE 2 END,
                    COUNT(lt.TagID) DESC,
                    t.Name";

            var parameters = new[]
            {
                (limit).ToSqlParam("@Limit"),
                ($"%{query}%").ToSqlParam("@Query"),
                ($"{query}%").ToSqlParam("@ExactQuery")
            };

            var tags = await connection.ExecuteQuery<TagListDto>(searchQuery, parameters);

            return ApiResponse<List<TagListDto>>.SuccessResponse(tags.ToList());
        }
        catch (Exception ex)
        {
            return ApiResponse<List<TagListDto>>.ErrorResponse("TAG_SEARCH_ERROR", ex.Message);
        }
    }
}
