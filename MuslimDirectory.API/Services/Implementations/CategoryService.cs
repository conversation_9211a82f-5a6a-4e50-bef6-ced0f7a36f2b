using MuslimDirectory.API.Models.DTOs.Category;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Services.Interfaces;
using System.Data;
using System.Data.SqlClient;
using XGENO.DBHelpers.Core;

namespace MuslimDirectory.API.Services.Implementations;

public class CategoryService : ICategoryService
{
    private readonly string _connectionString;

    public CategoryService(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("Connection string not found");
    }

    public async Task<ApiResponse<List<CategoryDto>>> GetCategoriesAsync(
        bool includeSubCategories = true, bool includeInactive = false, Guid? parentId = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            if (!includeInactive)
            {
                whereConditions.Add("c.IsActive = 1");
            }

            if (parentId.HasValue)
            {
                whereConditions.Add("c.ParentCategoryID = @ParentId");
                parameters.Add((parentId.Value).ToSqlParam("@ParentId"));
            }
            else
            {
                whereConditions.Add("c.ParentCategoryID IS NULL");
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            var query = $@"
                SELECT 
                    c.ID,
                    c.Name,
                    c.Description,
                    c.IconURL,
                    c.ParentCategoryID,
                    pc.Name as ParentCategoryName,
                    c.SortOrder,
                    c.IsActive,
                    c.CreatedAt,
                    COUNT(DISTINCT lc.ListingID) as ListingCount
                FROM Categories c
                LEFT JOIN Categories pc ON c.ParentCategoryID = pc.ID
                LEFT JOIN ListingCategories lc ON c.ID = lc.CategoryID
                {whereClause}
                GROUP BY c.ID, c.Name, c.Description, c.IconURL, c.ParentCategoryID, pc.Name, c.SortOrder, c.IsActive, c.CreatedAt
                ORDER BY c.SortOrder, c.Name";

            var categories = await connection.ExecuteQuery<CategoryDto>(query, parameters.ToArray());
            var categoryList = categories.ToList();

            if (includeSubCategories && !parentId.HasValue)
            {
                // Load subcategories for each category
                foreach (var category in categoryList)
                {
                    var subCategoriesResult = await GetCategoriesAsync(false, includeInactive, category.Id);
                    if (subCategoriesResult.Success)
                    {
                        category.SubCategories = subCategoriesResult.Data;
                    }
                }
            }

            return ApiResponse<List<CategoryDto>>.SuccessResponse(categoryList);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<CategoryDto>>.ErrorResponse("CATEGORY_FETCH_ERROR", ex.Message);
        }
    }

    public async Task<ApiResponse<CategoryWithListingsDto>> GetCategoryByIdAsync(
        Guid id, int page = 1, int pageSize = 10, string? status = null, string? complianceStatus = null, 
        string? platformType = null, string? pricingModel = null, string? sortBy = null, string? sortOrder = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get category details
            var categoryQuery = @"
                SELECT 
                    c.ID,
                    c.Name,
                    c.Description,
                    c.IconURL,
                    c.ParentCategoryID,
                    pc.Name as ParentCategoryName,
                    c.SortOrder,
                    c.IsActive,
                    c.CreatedAt
                FROM Categories c
                LEFT JOIN Categories pc ON c.ParentCategoryID = pc.ID
                WHERE c.ID = @ID";

            var categoryParams = new[] { (id).ToSqlParam("@ID") };
            var categories = await connection.ExecuteQuery<CategoryWithListingsDto>(categoryQuery, categoryParams);
            var category = categories.FirstOrDefault();

            if (category == null)
            {
                return ApiResponse<CategoryWithListingsDto>.ErrorResponse("Category not found", "CATEGORY_NOT_FOUND");
            }

            // Get subcategories
            var subCategoriesResult = await GetCategoriesAsync(false, false, id);
            if (subCategoriesResult.Success)
            {
                category.SubCategories = subCategoriesResult.Data;
            }

            // Build listing query with filters
            var offset = (page - 1) * pageSize;
            var whereConditions = new List<string> { "lc.CategoryID = @CategoryID", "l.Status = 'Approved'" };
            var parameters = new List<SqlParameter> { (id).ToSqlParam("@CategoryID") };

            if (!string.IsNullOrWhiteSpace(status))
            {
                whereConditions.Add("l.Status = @Status");
                parameters.Add((status).ToSqlParam("@Status"));
            }

            if (!string.IsNullOrWhiteSpace(complianceStatus))
            {
                whereConditions.Add("l.IslamicComplianceStatus = @ComplianceStatus");
                parameters.Add((complianceStatus).ToSqlParam("@ComplianceStatus"));
            }

            if (!string.IsNullOrWhiteSpace(platformType))
            {
                whereConditions.Add("l.PlatformType = @PlatformType");
                parameters.Add((platformType).ToSqlParam("@PlatformType"));
            }

            if (!string.IsNullOrWhiteSpace(pricingModel))
            {
                whereConditions.Add("l.PricingModel = @PricingModel");
                parameters.Add((pricingModel).ToSqlParam("@PricingModel"));
            }

            var whereClause = "WHERE " + string.Join(" AND ", whereConditions);

            // Determine sort order
            var orderBy = "ORDER BY l.FeaturedLevel DESC, l.CreatedAt DESC";
            if (!string.IsNullOrWhiteSpace(sortBy))
            {
                var direction = sortOrder?.ToUpper() == "DESC" ? "DESC" : "ASC";
                orderBy = sortBy.ToLower() switch
                {
                    "name" => $"ORDER BY l.Title {direction}",
                    "created" => $"ORDER BY l.CreatedAt {direction}",
                    "views" => $"ORDER BY l.ViewCount {direction}",
                    "featured" => $"ORDER BY l.FeaturedLevel {direction}, l.CreatedAt DESC",
                    _ => orderBy
                };
            }

            // Get total count
            var countQuery = $@"
                SELECT COUNT(DISTINCT l.ID)
                FROM Listings l
                INNER JOIN ListingCategories lc ON l.ID = lc.ListingID
                {whereClause}";

            var totalCountResult = await connection.ExecuteQuery<int>(countQuery, parameters.ToArray());
            var totalCount = totalCountResult.First();

            // Get listings
            var listingsQuery = $@"
                SELECT DISTINCT
                    l.ID,
                    l.Title,
                    l.ShortDescription,
                    l.LogoURL,
                    l.PlatformType,
                    l.PricingModel,
                    l.Price,
                    l.Currency,
                    l.IslamicComplianceStatus,
                    l.FeaturedLevel,
                    l.SlugURL,
                    l.CreatedAt,
                    l.ViewCount
                FROM Listings l
                INNER JOIN ListingCategories lc ON l.ID = lc.ListingID
                {whereClause}
                {orderBy}
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            parameters.Add((offset).ToSqlParam("@Offset"));
            parameters.Add((pageSize).ToSqlParam("@PageSize"));

            var listings = await connection.ExecuteQuery<CategoryListingDto>(listingsQuery, parameters.ToArray());
            
            category.Listings = listings.ToList();
            category.TotalListings = totalCount;

            return ApiResponse<CategoryWithListingsDto>.SuccessResponse(category);
        }
        catch (Exception ex)
        {
            return ApiResponse<CategoryWithListingsDto>.ErrorResponse("Failed to retrieve category", ex.Message);
        }
    }

    public async Task<ApiResponse<CategoryDto>> CreateCategoryAsync(CreateCategoryDto createDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var categoryId = Guid.NewGuid();
            var now = DateTime.UtcNow;

            var insertQuery = @"
                INSERT INTO Categories (ID, Name, Description, IconURL, ParentCategoryID, SortOrder, CreatedAt)
                VALUES (@ID, @Name, @Description, @IconURL, @ParentCategoryID, @SortOrder, @CreatedAt)";

            var parameters = new[]
            {
                (categoryId).ToSqlParam("@ID"),
                (createDto.Name).ToSqlParam("@Name"),
                (createDto.Description).ToSqlParam("@Description"),
                (createDto.IconURL).ToSqlParam("@IconURL"),
                (createDto.ParentCategoryId).ToSqlParam("@ParentCategoryID"),
                (createDto.SortOrder).ToSqlParam("@SortOrder"),
                (now).ToSqlParam("@CreatedAt")
            };

            await connection.ExecuteNonQueryText(insertQuery, parameters);

            // Return the created category
            var categoryQuery = @"
                SELECT
                    c.ID,
                    c.Name,
                    c.Description,
                    c.IconURL,
                    c.ParentCategoryID,
                    pc.Name as ParentCategoryName,
                    c.SortOrder,
                    c.IsActive,
                    c.CreatedAt,
                    0 as ListingCount
                FROM Categories c
                LEFT JOIN Categories pc ON c.ParentCategoryID = pc.ID
                WHERE c.ID = @ID";

            var categoryParams = new[] { (categoryId).ToSqlParam("@ID") };
            var categories = await connection.ExecuteQuery<CategoryDto>(categoryQuery, categoryParams);

            return ApiResponse<CategoryDto>.SuccessResponse(categories.First());
        }
        catch (Exception ex)
        {
            return ApiResponse<CategoryDto>.ErrorResponse("Failed to create category", ex.Message);
        }
    }

    public async Task<ApiResponse<CategoryDto>> UpdateCategoryAsync(Guid id, UpdateCategoryDto updateDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var updateFields = new List<string>();
            var parameters = new List<SqlParameter>();

            if (!string.IsNullOrWhiteSpace(updateDto.Name))
            {
                updateFields.Add("Name = @Name");
                parameters.Add((updateDto.Name).ToSqlParam("@Name"));
            }

            if (updateDto.Description != null)
            {
                updateFields.Add("Description = @Description");
                parameters.Add((updateDto.Description).ToSqlParam("@Description"));
            }

            if (updateDto.IconURL != null)
            {
                updateFields.Add("IconURL = @IconURL");
                parameters.Add((updateDto.IconURL).ToSqlParam("@IconURL"));
            }

            if (updateDto.ParentCategoryId.HasValue)
            {
                updateFields.Add("ParentCategoryID = @ParentCategoryID");
                parameters.Add((updateDto.ParentCategoryId.Value).ToSqlParam("@ParentCategoryID"));
            }

            if (updateDto.SortOrder.HasValue)
            {
                updateFields.Add("SortOrder = @SortOrder");
                parameters.Add((updateDto.SortOrder.Value).ToSqlParam("@SortOrder"));
            }

            if (updateDto.IsActive.HasValue)
            {
                updateFields.Add("IsActive = @IsActive");
                parameters.Add((updateDto.IsActive.Value).ToSqlParam("@IsActive"));
            }

            if (updateFields.Count == 0)
            {
                return ApiResponse<CategoryDto>.ErrorResponse("No fields to update", "NO_FIELDS_TO_UPDATE");
            }

            parameters.Add((id).ToSqlParam("@ID"));

            var updateQuery = $@"
                UPDATE Categories
                SET {string.Join(", ", updateFields)}
                WHERE ID = @ID";

            await connection.ExecuteNonQueryText(updateQuery, parameters.ToArray());

            // Return updated category
            var categoryQuery = @"
                SELECT
                    c.ID,
                    c.Name,
                    c.Description,
                    c.IconURL,
                    c.ParentCategoryID,
                    pc.Name as ParentCategoryName,
                    c.SortOrder,
                    c.IsActive,
                    c.CreatedAt,
                    COUNT(DISTINCT lc.ListingID) as ListingCount
                FROM Categories c
                LEFT JOIN Categories pc ON c.ParentCategoryID = pc.ID
                LEFT JOIN ListingCategories lc ON c.ID = lc.CategoryID
                WHERE c.ID = @ID
                GROUP BY c.ID, c.Name, c.Description, c.IconURL, c.ParentCategoryID, pc.Name, c.SortOrder, c.IsActive, c.CreatedAt";

            var categoryParams = new[] { (id).ToSqlParam("@ID") };
            var categories = await connection.ExecuteQuery<CategoryDto>(categoryQuery, categoryParams);

            return ApiResponse<CategoryDto>.SuccessResponse(categories.First());
        }
        catch (Exception ex)
        {
            return ApiResponse<CategoryDto>.ErrorResponse("Failed to update category", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> DeleteCategoryAsync(Guid id, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if category has subcategories
            var subCategoryQuery = "SELECT COUNT(*) FROM Categories WHERE ParentCategoryID = @ID";
            var subCategoryParams = new[] { (id).ToSqlParam("@ID") };
            var subCategoryCountResult = await connection.ExecuteQuery<int>(subCategoryQuery, subCategoryParams);
            var subCategoryCount = subCategoryCountResult.First();

            if (subCategoryCount > 0)
            {
                return ApiResponse<bool>.ErrorResponse("Cannot delete category with subcategories", "HAS_SUBCATEGORIES");
            }

            // Check if category has listings
            var listingQuery = "SELECT COUNT(*) FROM ListingCategories WHERE CategoryID = @ID";
            var listingParams = new[] { (id).ToSqlParam("@ID") };
            var listingCountResult = await connection.ExecuteQuery<int>(listingQuery, listingParams);
            var listingCount = listingCountResult.First();

            if (listingCount > 0)
            {
                return ApiResponse<bool>.ErrorResponse("Cannot delete category with listings", "HAS_LISTINGS");
            }

            var deleteQuery = "DELETE FROM Categories WHERE ID = @ID";
            var deleteParams = new[] { (id).ToSqlParam("@ID") };
            await connection.ExecuteNonQueryText(deleteQuery, deleteParams);

            return ApiResponse<bool>.SuccessResponse(true);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to delete category", ex.Message);
        }
    }

    public async Task<ApiResponse<List<CategoryListDto>>> GetCategoriesListAsync(
        int page = 1, int pageSize = 10, string? search = null, bool includeInactive = false)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var offset = (page - 1) * pageSize;
            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            if (!includeInactive)
            {
                whereConditions.Add("c.IsActive = 1");
            }

            if (!string.IsNullOrWhiteSpace(search))
            {
                whereConditions.Add("(c.Name LIKE @Search OR c.Description LIKE @Search)");
                parameters.Add(($"%{search}%").ToSqlParam("@Search"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            var query = $@"
                SELECT
                    c.ID,
                    c.Name,
                    c.Description,
                    c.IconURL,
                    c.ParentCategoryID,
                    pc.Name as ParentCategoryName,
                    c.SortOrder,
                    c.IsActive,
                    COUNT(DISTINCT lc.ListingID) as ListingCount,
                    COUNT(DISTINCT sc.ID) as SubCategoryCount
                FROM Categories c
                LEFT JOIN Categories pc ON c.ParentCategoryID = pc.ID
                LEFT JOIN Categories sc ON c.ID = sc.ParentCategoryID
                LEFT JOIN ListingCategories lc ON c.ID = lc.CategoryID
                {whereClause}
                GROUP BY c.ID, c.Name, c.Description, c.IconURL, c.ParentCategoryID, pc.Name, c.SortOrder, c.IsActive
                ORDER BY c.SortOrder, c.Name
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            parameters.Add((offset).ToSqlParam("@Offset"));
            parameters.Add((pageSize).ToSqlParam("@PageSize"));

            var categories = await connection.ExecuteQuery<CategoryListDto>(query, parameters.ToArray());

            return ApiResponse<List<CategoryListDto>>.SuccessResponse(categories.ToList());
        }
        catch (Exception ex)
        {
            return ApiResponse<List<CategoryListDto>>.ErrorResponse("CATEGORY_LIST_ERROR", ex.Message);
        }
    }
}
