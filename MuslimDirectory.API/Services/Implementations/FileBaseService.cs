using Amazon.S3;
using Amazon.S3.Model;
using Amazon;
using Amazon.Runtime;
using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Utilities;
using MuslimDirectory.API.Models.Common;
using System.Net;
using System.Security.Cryptography;

namespace MuslimDirectory.API.Services.Implementations;

public class FileBaseService : IFileBaseService, IDisposable
{
    private readonly IAmazonS3 _s3Client;
    private readonly IConfiguration _configuration;
    private readonly string _bucketName;
    private readonly string _baseUrl;
    private readonly string _ipfsGatewayUrl;

    public FileBaseService(IConfiguration configuration)
    {
        _configuration = configuration;
        _bucketName = _configuration["FileBase:BucketName"] ?? throw new ArgumentNullException("FileBase:BucketName configuration is required");
        _baseUrl = _configuration["FileBase:BaseUrl"] ?? throw new ArgumentNullException("FileBase:BaseUrl configuration is required");
        _ipfsGatewayUrl = _configuration["FileBase:IpfsGatewayUrl"] ?? "https://glorious-yellow-wolverine.myfilebase.com/ipfs";

        // Get FileBase credentials from configuration
        var accessKey = _configuration["FileBase:AccessKey"] ?? throw new ArgumentNullException("FileBase:AccessKey configuration is required");
        var secretKey = _configuration["FileBase:SecretKey"] ?? throw new ArgumentNullException("FileBase:SecretKey configuration is required");
        var region = _configuration["FileBase:Region"] ?? "us-east-1";

        // Create AWS credentials
        var credentials = new BasicAWSCredentials(accessKey, secretKey);

        // Create S3 client with FileBase endpoint
        var config = new AmazonS3Config
        {
            ServiceURL = "https://s3.filebase.com",
            ForcePathStyle = true,
            UseHttp = false,
            UseDualstackEndpoint = false
        };

        _s3Client = new AmazonS3Client(credentials, config);
    }

    public async Task<ApiResponse<FileUploadResponse>> UploadFileAsync(IFormFile file, string? folder = null, string? customFileName = null, bool makePublic = true)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return ApiResponse<FileUploadResponse>.ErrorResponse("INVALID_FILE", "File is required and cannot be empty");
            }

            // Validate file size (max 50MB)
            const long maxFileSize = 50 * 1024 * 1024; // 50MB
            if (file.Length > maxFileSize)
            {
                return ApiResponse<FileUploadResponse>.ErrorResponse("FILE_TOO_LARGE", "File size cannot exceed 50MB");
            }

            // Validate file type
            var allowedContentTypes = new[]
            {
                "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
                "application/pdf", "text/plain", "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/vnd.ms-excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            };

            if (!allowedContentTypes.Contains(file.ContentType.ToLower()))
            {
                return ApiResponse<FileUploadResponse>.ErrorResponse("INVALID_FILE_TYPE", 
                    "File type not allowed. Supported types: images, PDF, text, Word, Excel");
            }

            // Generate file key
            var fileName = customFileName ?? file.FileName;
            var fileExtension = Path.GetExtension(fileName);
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            var uniqueFileName = $"{fileNameWithoutExtension}_{Guid.NewGuid()}{fileExtension}";
            
            var key = string.IsNullOrEmpty(folder) 
                ? uniqueFileName 
                : $"{folder.Trim('/')}/{uniqueFileName}";

            // Read file content into byte array to ensure exact size and integrity
            byte[] fileContent;
            using (var fileStream = file.OpenReadStream())
            {
                using var memoryStream = new MemoryStream();
                await fileStream.CopyToAsync(memoryStream);
                fileContent = memoryStream.ToArray();
            }

            // Create upload request with proper binary stream
            using var uploadStream = new MemoryStream(fileContent);

            var request = new PutObjectRequest
            {
                BucketName = _bucketName,
                Key = key,
                InputStream = uploadStream,
                ContentType = file.ContentType,
                DisablePayloadSigning = false,
                UseChunkEncoding = false,
                ServerSideEncryptionMethod = ServerSideEncryptionMethod.None
            };

            if (makePublic)
            {
                request.CannedACL = S3CannedACL.PublicRead;
            }

            // Calculate MD5 hash for integrity verification
            string md5Hash;
            using (var md5 = MD5.Create())
            {
                var hashBytes = md5.ComputeHash(fileContent);
                md5Hash = Convert.ToBase64String(hashBytes);
            }

            // Add metadata
            request.Metadata.Add("original-filename", file.FileName);
            request.Metadata.Add("upload-timestamp", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"));
            request.Metadata.Add("content-length", fileContent.Length.ToString());
            request.Metadata.Add("md5-hash", md5Hash);
            request.Metadata.Add("file-type", file.ContentType);
            request.Metadata.Add("upload-source", "muslim-directory-api");

            // Set Content-MD5 for S3 integrity check
            request.MD5Digest = md5Hash;

            // Upload file
            var response = await _s3Client.PutObjectAsync(request);

            if (response.HttpStatusCode == HttpStatusCode.OK)
            {
                // Extract IPFS CID from the upload response
                string? ipfsCid = null;

                // FileBase returns the IPFS CID in the x-amz-meta-cid header
                // We need to get the object metadata to access this header
                Console.WriteLine("Checking for IPFS CID in object metadata...");
                await Task.Delay(1000); // Wait for FileBase to process the upload
                ipfsCid = await GetIpfsCidFromMetadata(key);

                // Construct the public URL using IPFS gateway if CID is available
                var publicUrl = !string.IsNullOrEmpty(ipfsCid)
                    ? $"{_ipfsGatewayUrl.TrimEnd('/')}/{ipfsCid}"
                    : $"{_baseUrl.TrimEnd('/')}/{key}"; // Fallback to direct URL

                Console.WriteLine($"File uploaded successfully. Key: {key}, IPFS CID: {ipfsCid ?? "N/A"}, Public URL: {publicUrl}");

                var uploadResponse = new FileUploadResponse
                {
                    FileName = uniqueFileName,
                    PublicUrl = publicUrl,
                    Key = key,
                    FileSize = fileContent.Length,
                    ContentType = file.ContentType,
                    UploadedAt = DateTime.UtcNow,
                    Folder = folder
                };

                return ApiResponse<FileUploadResponse>.SuccessResponse(uploadResponse, "File uploaded and verified successfully");
            }
            else
            {
                return ApiResponse<FileUploadResponse>.ErrorResponse("UPLOAD_FAILED",
                    $"Failed to upload file to FileBase. Status: {response.HttpStatusCode}");
            }
        }
        catch (AmazonS3Exception ex)
        {
            return ApiResponse<FileUploadResponse>.ErrorResponse("S3_ERROR", $"FileBase error: {ex.Message}", ex.ErrorCode);
        }
        catch (Exception ex)
        {
            return ApiResponse<FileUploadResponse>.ErrorResponse("UPLOAD_ERROR", "An error occurred while uploading the file", ex.Message);
        }
    }

    public async Task<ApiResponse<FileDeleteResponse>> DeleteFileAsync(string key)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return ApiResponse<FileDeleteResponse>.ErrorResponse("INVALID_KEY", "File key is required");
            }

            var request = new DeleteObjectRequest
            {
                BucketName = _bucketName,
                Key = key
            };

            var response = await _s3Client.DeleteObjectAsync(request);

            var deleteResponse = new FileDeleteResponse
            {
                Success = response.HttpStatusCode == HttpStatusCode.NoContent,
                Message = response.HttpStatusCode == HttpStatusCode.NoContent ? "File deleted successfully" : "Failed to delete file",
                Key = key
            };

            return ApiResponse<FileDeleteResponse>.SuccessResponse(deleteResponse);
        }
        catch (AmazonS3Exception ex)
        {
            return ApiResponse<FileDeleteResponse>.ErrorResponse("S3_ERROR", $"FileBase error: {ex.Message}", ex.ErrorCode);
        }
        catch (Exception ex)
        {
            return ApiResponse<FileDeleteResponse>.ErrorResponse("DELETE_ERROR", "An error occurred while deleting the file", ex.Message);
        }
    }

    public async Task<ApiResponse<string>> GetFileUrlAsync(string key)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return ApiResponse<string>.ErrorResponse("INVALID_KEY", "File key is required");
            }

            // Try to get IPFS CID for the file
            var ipfsCid = await GetIpfsCidFromMetadata(key);
            var publicUrl = !string.IsNullOrEmpty(ipfsCid)
                ? $"{_ipfsGatewayUrl.TrimEnd('/')}/{ipfsCid}"
                : $"{_baseUrl.TrimEnd('/')}/{key}"; // Fallback to direct URL

            Console.WriteLine($"Generated URL for key {key}: {publicUrl} (IPFS CID: {ipfsCid ?? "N/A"})");
            return ApiResponse<string>.SuccessResponse(publicUrl);
        }
        catch (Exception ex)
        {
            return ApiResponse<string>.ErrorResponse("URL_ERROR", "An error occurred while generating file URL", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> FileExistsAsync(string key)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return ApiResponse<bool>.ErrorResponse("INVALID_KEY", "File key is required");
            }

            var request = new GetObjectMetadataRequest
            {
                BucketName = _bucketName,
                Key = key
            };

            try
            {
                await _s3Client.GetObjectMetadataAsync(request);
                return ApiResponse<bool>.SuccessResponse(true);
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == HttpStatusCode.NotFound)
            {
                return ApiResponse<bool>.SuccessResponse(false);
            }
        }
        catch (AmazonS3Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("S3_ERROR", $"FileBase error: {ex.Message}", ex.ErrorCode);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("CHECK_ERROR", "An error occurred while checking file existence", ex.Message);
        }
    }

    private bool IsValidIpfsCid(string cid)
    {
        if (string.IsNullOrEmpty(cid))
            return false;

        // IPFS CID v0 starts with 'Qm' and is 46 characters long
        if (cid.StartsWith("Qm") && cid.Length == 46)
            return true;

        // IPFS CID v1 starts with 'bafy', 'bafk', 'bafz', etc.
        if (cid.StartsWith("bafy") || cid.StartsWith("bafk") || cid.StartsWith("bafz") ||
            cid.StartsWith("bafm") || cid.StartsWith("baga") || cid.StartsWith("bagb"))
            return true;

        // Other valid IPFS CID prefixes
        if (cid.StartsWith("zdj") || cid.StartsWith("zb2") || cid.StartsWith("z"))
            return true;

        return false;
    }

    private async Task<string?> GetIpfsCidFromMetadata(string key)
    {
        try
        {
            var request = new GetObjectMetadataRequest
            {
                BucketName = _bucketName,
                Key = key
            };

            var response = await _s3Client.GetObjectMetadataAsync(request);

            // Check for x-amz-meta-cid in metadata (FileBase stores IPFS CID here)
            if (response.Metadata.Keys.Contains("x-amz-meta-cid"))
            {
                var cid = response.Metadata["x-amz-meta-cid"];
                if (IsValidIpfsCid(cid))
                {
                    Console.WriteLine($"IPFS CID found in object metadata 'cid': {cid}");
                    return cid;
                }
            }

            // Check alternative metadata keys
            if (response.Metadata.Keys.Contains("ipfs-cid"))
            {
                var cid = response.Metadata["ipfs-cid"];
                if (IsValidIpfsCid(cid))
                {
                    Console.WriteLine($"IPFS CID found in object metadata 'ipfs-cid': {cid}");
                    return cid;
                }
            }

            if (response.Metadata.Keys.Contains("ipfs-hash"))
            {
                var cid = response.Metadata["ipfs-hash"];
                if (IsValidIpfsCid(cid))
                {
                    Console.WriteLine($"IPFS CID found in object metadata 'ipfs-hash': {cid}");
                    return cid;
                }
            }

            // Check ETag as last resort
            var etag = response.ETag?.Trim('"');
            if (IsValidIpfsCid(etag))
            {
                Console.WriteLine($"IPFS CID found in ETag: {etag}");
                return etag;
            }

            Console.WriteLine($"No valid IPFS CID found in metadata for key: {key}. Available metadata keys: {string.Join(", ", response.Metadata.Keys)}");
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to get IPFS CID from metadata for {key}: {ex.Message}");
            return null;
        }
    }

    private async Task<string?> GetIpfsHashFromUploadedFile(string key)
    {
        // This method is now replaced by GetIpfsCidFromMetadata but keeping for compatibility
        return await GetIpfsCidFromMetadata(key);
    }

    public void Dispose()
    {
        _s3Client?.Dispose();
    }
}
