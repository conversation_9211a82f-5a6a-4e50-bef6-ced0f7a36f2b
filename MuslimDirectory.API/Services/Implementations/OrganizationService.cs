using MuslimDirectory.API.Models.DTOs.Organization;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;
using MuslimDirectory.API.Services.Interfaces;
using System.Data;
using System.Data.SqlClient;
using XGENO.DBHelpers.Core;

namespace MuslimDirectory.API.Services.Implementations;

public class OrganizationService : IOrganizationService
{
    private readonly string _connectionString;

    public OrganizationService(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("Connection string not found");
    }

    public async Task<ApiResponse<List<OrganizationListDto>>> GetOrganizationsAsync(
        int page = 1, int pageSize = 10, string? search = null, string? country = null, bool? isVerified = null)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var offset = (page - 1) * pageSize;
            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            if (!string.IsNullOrWhiteSpace(search))
            {
                whereConditions.Add("(o.Name LIKE @Search OR o.Description LIKE @Search)");
                parameters.Add(($"%{search}%").ToSqlParam("@Search"));
            }

            if (!string.IsNullOrWhiteSpace(country))
            {
                whereConditions.Add("o.Country = @Country");
                parameters.Add(country.ToSqlParam("@Country"));
            }

            if (isVerified.HasValue)
            {
                whereConditions.Add("o.IsVerified = @IsVerified");
                parameters.Add(isVerified.Value.ToSqlParam("@IsVerified"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countQuery = $@"
                SELECT COUNT(*)
                FROM Organizations o
                {whereClause}";

            var totalCountResult = await connection.ExecuteQuery<int>(countQuery, parameters.ToArray());
            var totalCount = totalCountResult.First();

            // Get organizations
            var query = $@"
                SELECT 
                    o.ID,
                    o.Name,
                    o.Description,
                    o.LogoURL,
                    o.Country,
                    o.IsVerified,
                    o.CreatedAt,
                    COUNT(ou.ID) as MemberCount
                FROM Organizations o
                LEFT JOIN OrganizationUsers ou ON o.ID = ou.OrganizationID
                {whereClause}
                GROUP BY o.ID, o.Name, o.Description, o.LogoURL, o.Country, o.IsVerified, o.CreatedAt
                ORDER BY o.CreatedAt DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            parameters.Add(offset.ToSqlParam("@Offset"));
            parameters.Add(pageSize.ToSqlParam("@PageSize"));

            var organizations = await connection.ExecuteQuery<OrganizationListDto>(query, parameters.ToArray());

            return ApiResponse<List<OrganizationListDto>>.SuccessResponse(organizations.ToList());
        }
        catch (Exception ex)
        {
            return ApiResponse<List<OrganizationListDto>>.ErrorResponse("ORGANIZATION_FETCH_ERROR", "Failed to retrieve organizations", ex.Message);
        }
    }

    public async Task<ApiResponse<OrganizationDto>> CreateOrganizationAsync(CreateOrganizationDto createDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var organizationId = Guid.NewGuid();
            var now = DateTime.UtcNow;

            var insertQuery = @"
                INSERT INTO Organizations (ID, Name, Description, LogoURL, Website, Email, PhoneNumber, Address, Country, IslamicComplianceCertificate, CreatedAt, UpdatedAt)
                VALUES (@ID, @Name, @Description, @LogoURL, @Website, @Email, @PhoneNumber, @Address, @Country, @IslamicComplianceCertificate, @CreatedAt, @UpdatedAt)";

            var parameters = new[]
            {
                (organizationId).ToSqlParam("@ID"),
                (createDto.Name).ToSqlParam("@Name"),
                (createDto.Description).ToSqlParam("@Description"),
                (createDto.LogoURL).ToSqlParam("@LogoURL"),
                (createDto.Website).ToSqlParam("@Website"),
                (createDto.Email).ToSqlParam("@Email"),
                (createDto.PhoneNumber).ToSqlParam("@PhoneNumber"),
                (createDto.Address).ToSqlParam("@Address"),
                (createDto.Country).ToSqlParam("@Country"),
                (createDto.IslamicComplianceCertificate).ToSqlParam("@IslamicComplianceCertificate"),
                (now).ToSqlParam("@CreatedAt"),
                (now).ToSqlParam("@UpdatedAt")
            };

            await connection.ExecuteNonQueryText(insertQuery, parameters);

            // Add creator as owner
            var memberQuery = @"
                INSERT INTO OrganizationUsers (ID, UserID, OrganizationID, Role, CreatedAt)
                VALUES (@ID, @UserID, @OrganizationID, @Role, @CreatedAt)";

            var memberParameters = new[]
            {
                (Guid.NewGuid()).ToSqlParam("@ID"),
                (userId).ToSqlParam("@UserID"),
                (organizationId).ToSqlParam("@OrganizationID"),
                ("Owner").ToSqlParam("@Role"),
                (now).ToSqlParam("@CreatedAt")
            };

            await connection.ExecuteNonQueryText(memberQuery, memberParameters);

            return await GetOrganizationByIdAsync(organizationId);
        }
        catch (Exception ex)
        {
            return ApiResponse<OrganizationDto>.ErrorResponse("ORGANIZATION_CREATE_ERROR", "Failed to create organization", ex.Message);
        }
    }

    public async Task<ApiResponse<OrganizationDto>> GetOrganizationByIdAsync(Guid id)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var query = @"
                SELECT
                    o.ID,
                    o.Name,
                    o.Description,
                    o.LogoURL,
                    o.Website,
                    o.Email,
                    o.PhoneNumber,
                    o.Address,
                    o.Country,
                    o.IsVerified,
                    o.IslamicComplianceCertificate,
                    o.CreatedAt,
                    o.UpdatedAt,
                    COUNT(ou.ID) as MemberCount
                FROM Organizations o
                LEFT JOIN OrganizationUsers ou ON o.ID = ou.OrganizationID
                WHERE o.ID = @ID
                GROUP BY o.ID, o.Name, o.Description, o.LogoURL, o.Website, o.Email, o.PhoneNumber, o.Address, o.Country, o.IsVerified, o.IslamicComplianceCertificate, o.CreatedAt, o.UpdatedAt";

            var parameters = new[] { (id).ToSqlParam("@ID") };
            var organization = await connection.ExecuteQuery<OrganizationDto>(query, parameters);

            var org = organization.FirstOrDefault();
            if (org == null)
            {
                return ApiResponse<OrganizationDto>.ErrorResponse("Organization not found", "ORGANIZATION_NOT_FOUND");
            }

            return ApiResponse<OrganizationDto>.SuccessResponse(org);
        }
        catch (Exception ex)
        {
            return ApiResponse<OrganizationDto>.ErrorResponse("Failed to retrieve organization", ex.Message);
        }
    }

    public async Task<ApiResponse<OrganizationDto>> UpdateOrganizationAsync(Guid id, UpdateOrganizationDto updateDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user has permission to update
            var permissionCheck = await CheckUserPermissionAsync(id, userId, new[] { "Owner", "Admin" });
            if (!permissionCheck.Success)
            {
                return ApiResponse<OrganizationDto>.ErrorResponse(permissionCheck.Message, permissionCheck.Error?.Code);
            }

            var updateFields = new List<string>();
            var parameters = new List<SqlParameter>();

            if (!string.IsNullOrWhiteSpace(updateDto.Name))
            {
                updateFields.Add("Name = @Name");
                parameters.Add((updateDto.Name).ToSqlParam("@Name"));
            }

            if (updateDto.Description != null)
            {
                updateFields.Add("Description = @Description");
                parameters.Add((updateDto.Description).ToSqlParam("@Description"));
            }

            if (updateDto.LogoURL != null)
            {
                updateFields.Add("LogoURL = @LogoURL");
                parameters.Add((updateDto.LogoURL).ToSqlParam("@LogoURL"));
            }

            if (updateDto.Website != null)
            {
                updateFields.Add("Website = @Website");
                parameters.Add((updateDto.Website).ToSqlParam("@Website"));
            }

            if (updateDto.Email != null)
            {
                updateFields.Add("Email = @Email");
                parameters.Add((updateDto.Email).ToSqlParam("@Email"));
            }

            if (updateDto.PhoneNumber != null)
            {
                updateFields.Add("PhoneNumber = @PhoneNumber");
                parameters.Add((updateDto.PhoneNumber).ToSqlParam("@PhoneNumber"));
            }

            if (updateDto.Address != null)
            {
                updateFields.Add("Address = @Address");
                parameters.Add((updateDto.Address).ToSqlParam("@Address"));
            }

            if (updateDto.Country != null)
            {
                updateFields.Add("Country = @Country");
                parameters.Add((updateDto.Country).ToSqlParam("@Country"));
            }

            if (updateDto.IslamicComplianceCertificate != null)
            {
                updateFields.Add("IslamicComplianceCertificate = @IslamicComplianceCertificate");
                parameters.Add((updateDto.IslamicComplianceCertificate).ToSqlParam("@IslamicComplianceCertificate"));
            }

            if (updateFields.Count == 0)
            {
                return ApiResponse<OrganizationDto>.ErrorResponse("No fields to update", "NO_FIELDS_TO_UPDATE");
            }

            updateFields.Add("UpdatedAt = @UpdatedAt");
            parameters.Add((DateTime.UtcNow).ToSqlParam("@UpdatedAt"));
            parameters.Add((id).ToSqlParam("@ID"));

            var updateQuery = $@"
                UPDATE Organizations
                SET {string.Join(", ", updateFields)}
                WHERE ID = @ID";

            await connection.ExecuteNonQueryText(updateQuery, parameters.ToArray());

            return await GetOrganizationByIdAsync(id);
        }
        catch (Exception ex)
        {
            return ApiResponse<OrganizationDto>.ErrorResponse("Failed to update organization", ex.Message);
        }
    }

    public async Task<ApiResponse<List<OrganizationMemberDto>>> GetOrganizationMembersAsync(Guid organizationId, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user has permission to view members
            var permissionCheck = await CheckUserPermissionAsync(organizationId, userId, new[] { "Owner", "Admin", "Member" });
            if (!permissionCheck.Success)
            {
                return ApiResponse<List<OrganizationMemberDto>>.ErrorResponse(permissionCheck.Error?.Code ?? "PERMISSION_DENIED", permissionCheck.Message);
            }

            var query = @"
                SELECT
                    ou.ID,
                    ou.UserID,
                    u.FirstName + ' ' + u.LastName as UserName,
                    u.Email as UserEmail,
                    u.ProfilePicture as UserProfilePicture,
                    ou.Role,
                    ou.CreatedAt as JoinedAt
                FROM OrganizationUsers ou
                INNER JOIN Users u ON ou.UserID = u.ID
                WHERE ou.OrganizationID = @OrganizationID
                ORDER BY
                    CASE ou.Role
                        WHEN 'Owner' THEN 1
                        WHEN 'Admin' THEN 2
                        WHEN 'Member' THEN 3
                    END,
                    ou.CreatedAt";

            var parameters = new[] { (organizationId).ToSqlParam("@OrganizationID") };
            var members = await connection.ExecuteQuery<OrganizationMemberDto>(query, parameters);

            return ApiResponse<List<OrganizationMemberDto>>.SuccessResponse(members.ToList());
        }
        catch (Exception ex)
        {
            return ApiResponse<List<OrganizationMemberDto>>.ErrorResponse("MEMBERS_FETCH_ERROR", ex.Message);
        }
    }

    public async Task<ApiResponse<OrganizationMemberDto>> AddOrganizationMemberAsync(Guid organizationId, AddOrganizationMemberDto memberDto, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user has permission to add members
            var permissionCheck = await CheckUserPermissionAsync(organizationId, userId, new[] { "Owner", "Admin" });
            if (!permissionCheck.Success)
            {
                return ApiResponse<OrganizationMemberDto>.ErrorResponse(permissionCheck.Message, permissionCheck.Error?.Code);
            }

            // Find user by email
            var userQuery = "SELECT ID FROM Users WHERE Email = @Email";
            var userParams = new[] { (memberDto.Email).ToSqlParam("@Email") };
            var users = await connection.ExecuteQuery<Guid>(userQuery, userParams);
            var targetUserId = users.FirstOrDefault();

            if (targetUserId == Guid.Empty)
            {
                return ApiResponse<OrganizationMemberDto>.ErrorResponse("User not found", "USER_NOT_FOUND");
            }

            // Check if user is already a member
            var existingMemberQuery = "SELECT COUNT(*) FROM OrganizationUsers WHERE OrganizationID = @OrganizationID AND UserID = @UserID";
            var existingParams = new[]
            {
                (organizationId).ToSqlParam("@OrganizationID"),
                (targetUserId).ToSqlParam("@UserID")
            };
            var existingCountResult = await connection.ExecuteQuery<int>(existingMemberQuery, existingParams);
            var existingCount = existingCountResult.First();

            if (existingCount > 0)
            {
                return ApiResponse<OrganizationMemberDto>.ErrorResponse("User is already a member of this organization", "USER_ALREADY_MEMBER");
            }

            // Add member
            var memberId = Guid.NewGuid();
            var insertQuery = @"
                INSERT INTO OrganizationUsers (ID, UserID, OrganizationID, Role, CreatedAt)
                VALUES (@ID, @UserID, @OrganizationID, @Role, @CreatedAt)";

            var insertParams = new[]
            {
                (memberId).ToSqlParam("@ID"),
                (targetUserId).ToSqlParam("@UserID"),
                (organizationId).ToSqlParam("@OrganizationID"),
                (memberDto.Role).ToSqlParam("@Role"),
                (DateTime.UtcNow).ToSqlParam("@CreatedAt")
            };

            await connection.ExecuteNonQueryText(insertQuery, insertParams);

            // Return the added member
            var memberQuery = @"
                SELECT
                    ou.ID,
                    ou.UserID,
                    u.FirstName + ' ' + u.LastName as UserName,
                    u.Email as UserEmail,
                    u.ProfilePicture as UserProfilePicture,
                    ou.Role,
                    ou.CreatedAt as JoinedAt
                FROM OrganizationUsers ou
                INNER JOIN Users u ON ou.UserID = u.ID
                WHERE ou.ID = @ID";

            var memberParams = new[] { (memberId).ToSqlParam("@ID") };
            var member = await connection.ExecuteQuery<OrganizationMemberDto>(memberQuery, memberParams);

            return ApiResponse<OrganizationMemberDto>.SuccessResponse(member.First());
        }
        catch (Exception ex)
        {
            return ApiResponse<OrganizationMemberDto>.ErrorResponse("Failed to add organization member", ex.Message);
        }
    }

    public async Task<ApiResponse<List<MyOrganizationDto>>> GetMyOrganizationsAsync(Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var query = @"
                SELECT
                    o.ID,
                    o.Name,
                    o.LogoURL,
                    ou.Role,
                    ou.CreatedAt as JoinedAt,
                    COUNT(ou2.ID) as MemberCount
                FROM Organizations o
                INNER JOIN OrganizationUsers ou ON o.ID = ou.OrganizationID
                LEFT JOIN OrganizationUsers ou2 ON o.ID = ou2.OrganizationID
                WHERE ou.UserID = @UserID
                GROUP BY o.ID, o.Name, o.LogoURL, ou.Role, ou.CreatedAt
                ORDER BY ou.CreatedAt DESC";

            var parameters = new[] { (userId).ToSqlParam("@UserID") };
            var organizations = await connection.ExecuteQuery<MyOrganizationDto>(query, parameters);

            return ApiResponse<List<MyOrganizationDto>>.SuccessResponse(organizations.ToList());
        }
        catch (Exception ex)
        {
            return ApiResponse<List<MyOrganizationDto>>.ErrorResponse("USER_ORGS_ERROR", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> RemoveOrganizationMemberAsync(Guid organizationId, Guid memberId, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user has permission to remove members
            var permissionCheck = await CheckUserPermissionAsync(organizationId, userId, new[] { "Owner", "Admin" });
            if (!permissionCheck.Success)
            {
                return ApiResponse<bool>.ErrorResponse(permissionCheck.Message, permissionCheck.Error?.Code);
            }

            // Cannot remove the owner
            var memberRoleQuery = "SELECT Role FROM OrganizationUsers WHERE ID = @ID AND OrganizationID = @OrganizationID";
            var roleParams = new[]
            {
                (memberId).ToSqlParam("@ID"),
                (organizationId).ToSqlParam("@OrganizationID")
            };
            var roles = await connection.ExecuteQuery<string>(memberRoleQuery, roleParams);
            var memberRole = roles.FirstOrDefault();

            if (memberRole == "Owner")
            {
                return ApiResponse<bool>.ErrorResponse("Cannot remove organization owner", "CANNOT_REMOVE_OWNER");
            }

            var deleteQuery = "DELETE FROM OrganizationUsers WHERE ID = @ID AND OrganizationID = @OrganizationID";
            var deleteParams = new[]
            {
                (memberId).ToSqlParam("@ID"),
                (organizationId).ToSqlParam("@OrganizationID")
            };

            await connection.ExecuteNonQueryText(deleteQuery, deleteParams);

            return ApiResponse<bool>.SuccessResponse(true);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to remove organization member", ex.Message);
        }
    }

    public async Task<ApiResponse<bool>> UpdateMemberRoleAsync(Guid organizationId, Guid memberId, string role, Guid userId)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Only owners can update roles
            var permissionCheck = await CheckUserPermissionAsync(organizationId, userId, new[] { "Owner" });
            if (!permissionCheck.Success)
            {
                return ApiResponse<bool>.ErrorResponse(permissionCheck.Message, permissionCheck.Error?.Code);
            }

            // Cannot change owner role
            var memberRoleQuery = "SELECT Role FROM OrganizationUsers WHERE ID = @ID AND OrganizationID = @OrganizationID";
            var roleParams = new[]
            {
                (memberId).ToSqlParam("@ID"),
                (organizationId).ToSqlParam("@OrganizationID")
            };
            var roles = await connection.ExecuteQuery<string>(memberRoleQuery, roleParams);
            var currentRole = roles.FirstOrDefault();

            if (currentRole == "Owner")
            {
                return ApiResponse<bool>.ErrorResponse("Cannot change owner role", "CANNOT_CHANGE_OWNER_ROLE");
            }

            var updateQuery = "UPDATE OrganizationUsers SET Role = @Role WHERE ID = @ID AND OrganizationID = @OrganizationID";
            var updateParams = new[]
            {
                (role).ToSqlParam("@Role"),
                (memberId).ToSqlParam("@ID"),
                (organizationId).ToSqlParam("@OrganizationID")
            };

            await connection.ExecuteNonQueryText(updateQuery, updateParams);

            return ApiResponse<bool>.SuccessResponse(true);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to update member role", ex.Message);
        }
    }

    private async Task<ApiResponse<bool>> CheckUserPermissionAsync(Guid organizationId, Guid userId, string[] allowedRoles)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var query = "SELECT Role FROM OrganizationUsers WHERE OrganizationID = @OrganizationID AND UserID = @UserID";
            var parameters = new[]
            {
                (organizationId).ToSqlParam("@OrganizationID"),
                (userId).ToSqlParam("@UserID")
            };

            var roles = await connection.ExecuteQuery<string>(query, parameters);
            var userRole = roles.FirstOrDefault();

            if (string.IsNullOrEmpty(userRole))
            {
                return ApiResponse<bool>.ErrorResponse("Access denied: Not a member of this organization", "ACCESS_DENIED");
            }

            if (!allowedRoles.Contains(userRole))
            {
                return ApiResponse<bool>.ErrorResponse("Access denied: Insufficient permissions", "INSUFFICIENT_PERMISSIONS");
            }

            return ApiResponse<bool>.SuccessResponse(true);
        }
        catch (Exception ex)
        {
            return ApiResponse<bool>.ErrorResponse("Failed to check permissions", ex.Message);
        }
    }
}
