using System.Net;
using System.Net.Mail;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Options;
using MuslimDirectory.API.Models.DTOs.Email;

namespace MuslimDirectory.API.Services.Implementations;

public class EmailService : IEmailService
{
    private readonly EmailSettings _emailSettings;
    private readonly ILogger<EmailService> _logger;
    private readonly HttpClient _httpClient;

    public EmailService(IOptions<EmailSettings> emailSettings, ILogger<EmailService> logger, HttpClient httpClient)
    {
        _emailSettings = emailSettings.Value;
        _logger = logger;
        _httpClient = httpClient;
    }

    public async Task<bool> SendEmailVerificationAsync(string toEmail, string firstName, string verificationToken)
    {
        var subject = "Verify Your Email - Muslim Directory";
        var verificationUrl = $"https://directory-project.netlify.app/account-activation?token={verificationToken}"; 
        
        var htmlBody = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Email Verification</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='text-align: center; margin-bottom: 30px;'>
            <h1 style='color: #2c5530;'>Muslim Directory</h1>
        </div>
        
        <h2 style='color: #2c5530;'>Welcome, {firstName}!</h2>
        
        <p>Thank you for registering with Muslim Directory. To complete your registration, please verify your email address by clicking the button below:</p>
        
        <div style='text-align: center; margin: 30px 0;'>
            <a href='{verificationUrl}' style='background-color: #2c5530; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Verify Email Address</a>
        </div>
        
        <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
        <p style='word-break: break-all; color: #666;'>{verificationUrl}</p>
        
        <p><strong>This verification link will expire in 24 hours.</strong></p>
        
        <hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>
        
        <p style='font-size: 12px; color: #666;'>
            If you didn't create an account with Muslim Directory, please ignore this email.
        </p>
        
        <p style='font-size: 12px; color: #666;'>
            Best regards,<br>
            The Muslim Directory Team
        </p>
    </div>
</body>
</html>";

        var plainTextBody = $@"
Welcome, {firstName}!

Thank you for registering with Muslim Directory. To complete your registration, please verify your email address by visiting this link:

{verificationUrl}

This verification link will expire in 24 hours.

If you didn't create an account with Muslim Directory, please ignore this email.

Best regards,
The Muslim Directory Team
";

        return await SendEmailAsync(toEmail, subject, htmlBody, plainTextBody);
    }

    public async Task<bool> SendPasswordResetAsync(string toEmail, string firstName, string resetToken)
    {
        var subject = "Reset Your Password - Muslim Directory";
        var resetUrl = $"https://directory-project.netlify.app/reset-password?token={resetToken}"; // Update with your actual domain
        
        var htmlBody = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Password Reset</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='text-align: center; margin-bottom: 30px;'>
            <h1 style='color: #2c5530;'>Muslim Directory</h1>
        </div>
        
        <h2 style='color: #2c5530;'>Password Reset Request</h2>
        
        <p>Hello {firstName},</p>
        
        <p>We received a request to reset your password for your Muslim Directory account. Click the button below to reset your password:</p>
        
        <div style='text-align: center; margin: 30px 0;'>
            <a href='{resetUrl}' style='background-color: #2c5530; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Reset Password</a>
        </div>
        
        <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
        <p style='word-break: break-all; color: #666;'>{resetUrl}</p>
        
        <p><strong>This reset link will expire in 1 hour.</strong></p>
        
        <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
        
        <hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>
        
        <p style='font-size: 12px; color: #666;'>
            Best regards,<br>
            The Muslim Directory Team
        </p>
    </div>
</body>
</html>";

        var plainTextBody = $@"
Hello {firstName},

We received a request to reset your password for your Muslim Directory account. Visit this link to reset your password:

{resetUrl}

This reset link will expire in 1 hour.

If you didn't request a password reset, please ignore this email. Your password will remain unchanged.

Best regards,
The Muslim Directory Team
";

        return await SendEmailAsync(toEmail, subject, htmlBody, plainTextBody);
    }

    public async Task<bool> SendEmailAsync(string toEmail, string subject, string htmlBody, string? plainTextBody = null)
    {
        if (_emailSettings.ServiceType.Equals("API", StringComparison.OrdinalIgnoreCase))
        {
            return await SendEmailViaApiAsync(toEmail, subject, htmlBody, plainTextBody);
        }
        else
        {
            return await SendEmailViaSmtpAsync(toEmail, subject, htmlBody, plainTextBody);
        }
    }

    private async Task<bool> SendEmailViaApiAsync(string toEmail, string subject, string htmlBody, string? plainTextBody = null)
    {
        try
        {
            var request = new ResendEmailRequest
            {
                From = $"{_emailSettings.SenderName} <{_emailSettings.SenderEmail}>",
                To = new[] { toEmail },
                Subject = subject,
                Html = htmlBody,
                Text = plainTextBody
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, MediaTypeHeaderValue.Parse("application/json"));

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_emailSettings.ApiKey}");

            var response = await _httpClient.PostAsync($"{_emailSettings.ApiBaseUrl}/emails", content);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Email sent successfully via API to {Email}", toEmail);
                return true;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to send email via API to {Email}. Status: {Status}, Error: {Error}",
                    toEmail, response.StatusCode, errorContent);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email via API to {Email}", toEmail);
            return false;
        }
    }

    private async Task<bool> SendEmailViaSmtpAsync(string toEmail, string subject, string htmlBody, string? plainTextBody = null)
    {
        try
        {
            using var client = new SmtpClient(_emailSettings.SmtpServer, _emailSettings.SmtpPort);

            // Gmail specific settings
            client.EnableSsl = true;
            client.UseDefaultCredentials = false;
            client.DeliveryMethod = SmtpDeliveryMethod.Network;
            client.Credentials = new NetworkCredential(_emailSettings.Username, _emailSettings.Password);

            // Additional security settings for Gmail
            client.Timeout = 20000; // 20 seconds timeout

            var mailMessage = new MailMessage
            {
                From = new MailAddress(_emailSettings.SenderEmail, _emailSettings.SenderName),
                Subject = subject,
                Body = htmlBody,
                IsBodyHtml = true
            };

            mailMessage.To.Add(toEmail);

            // Add plain text alternative if provided
            if (!string.IsNullOrEmpty(plainTextBody))
            {
                var plainView = AlternateView.CreateAlternateViewFromString(plainTextBody, null, "text/plain");
                var htmlView = AlternateView.CreateAlternateViewFromString(htmlBody, null, "text/html");

                mailMessage.AlternateViews.Add(plainView);
                mailMessage.AlternateViews.Add(htmlView);
            }

            await client.SendMailAsync(mailMessage);

            _logger.LogInformation("Email sent successfully via SMTP to {Email}", toEmail);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email via SMTP to {Email}", toEmail);
            return false;
        }
    }
}
