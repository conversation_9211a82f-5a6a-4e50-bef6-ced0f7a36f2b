namespace MuslimDirectory.API.Services.Implementations;

public class CookieService(IOptions<CookieSettings> cookieSettings) : ICookieService
{
    private readonly CookieSettings _cookieSettings = cookieSettings.Value;

    public void SetAccessTokenCookie(HttpResponse response, string token)
    {
        var cookieOptions = new CookieOptions
        {
            HttpOnly = _cookieSettings.HttpOnly,
            Secure = _cookieSettings.SecureOnly,
            SameSite = ParseSameSiteMode(_cookieSettings.SameSite),
            Expires = DateTime.UtcNow.AddMinutes(_cookieSettings.AccessTokenExpirationInMinutes),
            Path = _cookieSettings.Path
        };

        if (!string.IsNullOrEmpty(_cookieSettings.Domain))
        {
            cookieOptions.Domain = _cookieSettings.Domain;
        }

        response.Cookies.Append(_cookieSettings.AccessTokenCookieName, token, cookieOptions);
    }

    public void SetRefreshTokenCookie(HttpResponse response, string refreshToken)
    {
        var cookieOptions = new CookieOptions
        {
            HttpOnly = _cookieSettings.HttpOnly,
            Secure = _cookieSettings.SecureOnly,
            SameSite = ParseSameSiteMode(_cookieSettings.SameSite),
            Expires = DateTime.UtcNow.AddDays(_cookieSettings.RefreshTokenExpirationInDays),
            Path = _cookieSettings.Path
        };

        if (!string.IsNullOrEmpty(_cookieSettings.Domain))
        {
            cookieOptions.Domain = _cookieSettings.Domain;
        }

        response.Cookies.Append(_cookieSettings.RefreshTokenCookieName, refreshToken, cookieOptions);
    }

    public void SetAuthCookies(HttpResponse response, string accessToken, string refreshToken)
    {
        SetAccessTokenCookie(response, accessToken);
        SetRefreshTokenCookie(response, refreshToken);
    }

    public string? GetAccessTokenFromCookie(HttpRequest request)
    {
        return request.Cookies[_cookieSettings.AccessTokenCookieName];
    }

    public string? GetRefreshTokenFromCookie(HttpRequest request)
    {
        return request.Cookies[_cookieSettings.RefreshTokenCookieName];
    }

    public void ClearAuthCookies(HttpResponse response)
    {
        ClearAccessTokenCookie(response);
        ClearRefreshTokenCookie(response);
    }

    public void ClearAccessTokenCookie(HttpResponse response)
    {
        var cookieOptions = new CookieOptions
        {
            HttpOnly = _cookieSettings.HttpOnly,
            Secure = _cookieSettings.SecureOnly,
            SameSite = ParseSameSiteMode(_cookieSettings.SameSite),
            Expires = DateTime.UtcNow.AddDays(-1), // Expire immediately
            Path = _cookieSettings.Path
        };

        if (!string.IsNullOrEmpty(_cookieSettings.Domain))
        {
            cookieOptions.Domain = _cookieSettings.Domain;
        }

        response.Cookies.Append(_cookieSettings.AccessTokenCookieName, string.Empty, cookieOptions);
    }

    public void ClearRefreshTokenCookie(HttpResponse response)
    {
        var cookieOptions = new CookieOptions
        {
            HttpOnly = _cookieSettings.HttpOnly,
            Secure = _cookieSettings.SecureOnly,
            SameSite = ParseSameSiteMode(_cookieSettings.SameSite),
            Expires = DateTime.UtcNow.AddDays(-1), // Expire immediately
            Path = _cookieSettings.Path
        };

        if (!string.IsNullOrEmpty(_cookieSettings.Domain))
        {
            cookieOptions.Domain = _cookieSettings.Domain;
        }

        response.Cookies.Append(_cookieSettings.RefreshTokenCookieName, string.Empty, cookieOptions);
    }

    private static SameSiteMode ParseSameSiteMode(string sameSite)
    {
        return sameSite.ToLowerInvariant() switch
        {
            "strict" => SameSiteMode.Strict,
            "lax" => SameSiteMode.Lax,
            "none" => SameSiteMode.None,
            _ => SameSiteMode.Strict
        };
    }
}
