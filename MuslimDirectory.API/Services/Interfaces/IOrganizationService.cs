using MuslimDirectory.API.Models.DTOs.Organization;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface IOrganizationService
{
    Task<ApiResponse<List<OrganizationListDto>>> GetOrganizationsAsync(
        int page = 1,
        int pageSize = 10,
        string? search = null,
        string? country = null,
        bool? isVerified = null);

    Task<ApiResponse<OrganizationDto>> CreateOrganizationAsync(CreateOrganizationDto createDto, Guid userId);

    Task<ApiResponse<OrganizationDto>> GetOrganizationByIdAsync(Guid id);

    Task<ApiResponse<OrganizationDto>> UpdateOrganizationAsync(Guid id, UpdateOrganizationDto updateDto, Guid userId);

    Task<ApiResponse<List<OrganizationMemberDto>>> GetOrganizationMembersAsync(Guid organizationId, Guid userId);

    Task<ApiResponse<OrganizationMemberDto>> AddOrganizationMemberAsync(Guid organizationId, AddOrganizationMemberDto memberDto, Guid userId);

    Task<ApiResponse<List<MyOrganizationDto>>> GetMyOrganizationsAsync(Guid userId);

    Task<ApiResponse<bool>> RemoveOrganizationMemberAsync(Guid organizationId, Guid memberId, Guid userId);

    Task<ApiResponse<bool>> UpdateMemberRoleAsync(Guid organizationId, Guid memberId, string role, Guid userId);
}
