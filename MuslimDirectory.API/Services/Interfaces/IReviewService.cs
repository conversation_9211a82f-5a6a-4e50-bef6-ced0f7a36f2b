using MuslimDirectory.API.Models.DTOs.Review;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface IReviewService
{
    // Public endpoints
    Task<ApiResponse<PaginatedResult<ReviewDto>>> GetReviewsAsync(
        Guid listingId,
        int page = 1,
        int limit = 20,
        string? sortBy = null,
        int? rating = null,
        bool? hasResponse = null);

    // Authenticated endpoints
    Task<ApiResponse<ReviewCreateResponseDto>> CreateReviewAsync(CreateReviewDto createDto, Guid userId);

    Task<ApiResponse<ReviewDto>> UpdateReviewAsync(Guid reviewId, UpdateReviewDto updateDto, Guid userId);

    Task<ApiResponse<bool>> DeleteReviewAsync(Guid reviewId, Guid userId);

    Task<ApiResponse<ReviewHelpfulResponseDto>> MarkReviewHelpfulAsync(
        Guid reviewId, ReviewHelpfulDto helpfulDto, Guid userId);

    Task<ApiResponse<bool>> ReportReviewAsync(Guid reviewId, ReportReviewDto reportDto, Guid userId);

    Task<ApiResponse<ReviewResponseDto>> RespondToReviewAsync(
        Guid reviewId, ReviewResponseCreateDto responseDto, Guid userId);

    Task<ApiResponse<PaginatedResult<ReviewDto>>> GetUserReviewsAsync(
        Guid userId,
        int page = 1,
        int limit = 20,
        string? status = null);

    // Helper methods
    Task<ApiResponse<ReviewSummaryDto>> GetReviewSummaryAsync(Guid listingId);
    Task<ApiResponse<bool>> CanUserReviewListingAsync(Guid userId, Guid listingId);
    Task<ApiResponse<bool>> CanUserEditReviewAsync(Guid userId, Guid reviewId);
}
