using MuslimDirectory.API.Models.DTOs.Admin;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface IAdminService
{
    // Dashboard and overview
    Task<ApiResponse<AdminDashboardDto>> GetDashboardAsync();
    Task<ApiResponse<PaginatedResult<AdminModerationItemDto>>> GetModerationQueueAsync(
        string? type = null, string? status = null, string? priority = null, 
        int page = 1, int limit = 20);

    // User management
    Task<ApiResponse<PaginatedResult<AdminUserDto>>> GetUsersAsync(
        string? search = null, string? status = null, string? role = null,
        DateTime? createdFrom = null, DateTime? createdTo = null,
        int page = 1, int limit = 20);
    Task<ApiResponse<bool>> UpdateUserStatusAsync(Guid userId, UpdateUserStatusDto updateDto, Guid adminUserId);

    // Listing management
    Task<ApiResponse<PaginatedResult<AdminListingDto>>> GetListingsAsync(
        string? search = null, string? status = null, string? category = null,
        bool? isVerified = null, bool? isFeatured = null,
        DateTime? createdFrom = null, DateTime? createdTo = null,
        int page = 1, int limit = 20);
    Task<ApiResponse<bool>> UpdateListingStatusAsync(Guid listingId, UpdateListingStatusDto updateDto, Guid adminUserId);

    // Review management
    Task<ApiResponse<PaginatedResult<AdminReviewDto>>> GetReviewsAsync(
        string? search = null, string? status = null,
        DateTime? createdFrom = null, DateTime? createdTo = null,
        int page = 1, int limit = 20);
    Task<ApiResponse<bool>> UpdateReviewStatusAsync(Guid reviewId, UpdateReviewStatusDto updateDto, Guid adminUserId);

    // Report management
    Task<ApiResponse<PaginatedResult<AdminReportDto>>> GetReportsAsync(
        string? type = null, string? status = null, string? priority = null,
        DateTime? createdFrom = null, DateTime? createdTo = null,
        int page = 1, int limit = 20);
    Task<ApiResponse<bool>> UpdateReportStatusAsync(Guid reportId, UpdateReportStatusDto updateDto, Guid adminUserId);
}
