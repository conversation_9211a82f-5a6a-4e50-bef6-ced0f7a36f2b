namespace MuslimDirectory.API.Services.Interfaces;

public interface IUserService
{
    Task<ApiResponse<UserProfileWrapperResponse>> GetUserProfileAsync(Guid userId);
    Task<ApiResponse<UserProfileWrapperResponse>> UpdateUserProfileAsync(Guid userId, UpdateUserProfileRequest request);
    Task<ApiResponse<string>> ChangePasswordAsync(Guid userId, ChangePasswordRequest request);
    Task<ApiResponse<UserPreferencesResponse>> GetUserPreferencesAsync(Guid userId);
    Task<ApiResponse<string>> UpdateUserPreferencesAsync(Guid userId, UpdateUserPreferencesRequest request);
}
