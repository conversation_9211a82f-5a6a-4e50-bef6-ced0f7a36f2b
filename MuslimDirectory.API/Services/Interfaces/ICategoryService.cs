using MuslimDirectory.API.Models.DTOs.Category;
using MuslimDirectory.API.Models.DTOs.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface ICategoryService
{
    Task<ApiResponse<List<CategoryDto>>> GetCategoriesAsync(
        bool includeSubCategories = true, 
        bool includeInactive = false, 
        Guid? parentId = null);

    Task<ApiResponse<CategoryWithListingsDto>> GetCategoryByIdAsync(
        Guid id, 
        int page = 1, 
        int pageSize = 10, 
        string? status = null, 
        string? complianceStatus = null, 
        string? platformType = null, 
        string? pricingModel = null, 
        string? sortBy = null, 
        string? sortOrder = null);

    Task<ApiResponse<CategoryDto>> CreateCategoryAsync(CreateCategoryDto createDto, Guid userId);

    Task<ApiResponse<CategoryDto>> UpdateCategoryAsync(Guid id, UpdateCategoryDto updateDto, Guid userId);

    Task<ApiResponse<bool>> DeleteCategoryAsync(Guid id, Guid userId);

    Task<ApiResponse<List<CategoryListDto>>> GetCategoriesListAsync(
        int page = 1, 
        int pageSize = 10, 
        string? search = null, 
        bool includeInactive = false);
}
