using MuslimDirectory.API.Models.DTOs.Search;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface ISearchService
{
    // Public search endpoints
    Task<ApiResponse<PaginatedResult<SearchResultDto>>> SearchAsync(
        SearchDto searchDto, Guid? userId = null, string? sessionId = null);

    Task<ApiResponse<List<SearchSuggestionDto>>> GetSearchSuggestionsAsync(
        string query, int limit = 10);

    // Authenticated search endpoints
    Task<ApiResponse<PaginatedResult<RecentSearchDto>>> GetRecentSearchesAsync(
        Guid userId, int page = 1, int limit = 20);

    Task<ApiResponse<bool>> ClearRecentSearchesAsync(Guid userId);

    // Helper methods
    Task<ApiResponse<SearchStatsDto>> GetSearchStatsAsync(SearchDto searchDto);
    Task<ApiResponse<List<PopularSearchDto>>> GetPopularSearchesAsync(int limit = 10);
    Task<ApiResponse<SearchFiltersDto>> GetSearchFiltersAsync();
}
