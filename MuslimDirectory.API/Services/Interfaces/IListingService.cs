using MuslimDirectory.API.Models.DTOs.Listing;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface IListingService
{
    // Public endpoints
    Task<ApiResponse<PaginatedResult<ListingDto>>> GetListingsAsync(
        int page = 1, int limit = 20, string? search = null, string? category = null,
        string? platform = null, string? language = null, string? pricingModel = null,
        string? complianceStatus = null, bool? featured = null, string? sortBy = null,
        int? minRating = null);

    Task<ApiResponse<List<FeaturedListingDto>>> GetFeaturedListingsAsync(int limit = 6);

    Task<ApiResponse<List<ListingDto>>> GetNewReleasesAsync(int limit = 10, int days = 30);

    Task<ApiResponse<List<ListingDto>>> GetTrendingListingsAsync(
        string timeframe = "week", int limit = 10);

    Task<ApiResponse<RecommendationsDto>> GetRecommendationsAsync(Guid userId);

    Task<ApiResponse<ListingDetailDto>> GetListingByIdAsync(Guid id, Guid? userId = null);

    // Authenticated endpoints
    Task<ApiResponse<ListingDto>> CreateListingAsync(CreateListingDto createDto, Guid userId);

    Task<ApiResponse<ListingDto>> UpdateListingAsync(Guid id, UpdateListingDto updateDto, Guid userId);

    Task<ApiResponse<bool>> DeleteListingAsync(Guid id, Guid userId);

    Task<ApiResponse<bool>> TrackViewAsync(Guid id, TrackViewDto trackDto);

    Task<ApiResponse<ListingAnalyticsDto>> GetListingAnalyticsAsync(
        Guid id, Guid userId, DateTime? startDate = null, DateTime? endDate = null,
        string granularity = "day");

    // Favorites
    Task<ApiResponse<bool>> AddToFavoritesAsync(Guid listingId, Guid userId);

    Task<ApiResponse<bool>> RemoveFromFavoritesAsync(Guid listingId, Guid userId);

    Task<ApiResponse<bool>> CheckIsFavoritedAsync(Guid listingId, Guid userId);

    Task<ApiResponse<PaginatedResult<ListingDto>>> GetUserFavoritesAsync(
        Guid userId, int page = 1, int limit = 20, string? sortBy = null, string? search = null);
}
