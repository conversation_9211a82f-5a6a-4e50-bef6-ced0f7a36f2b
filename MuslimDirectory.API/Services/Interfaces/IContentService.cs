using MuslimDirectory.API.Models.DTOs.Content;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface IContentService
{
    // Content Pages
    Task<ApiResponse<PaginatedResult<ContentPageDto>>> GetContentPagesAsync(
        string? search = null, bool? isPublished = null, int page = 1, int limit = 20);
    Task<ApiResponse<ContentPageDto>> GetContentPageBySlugAsync(string slug);
    Task<ApiResponse<ContentPageDto>> CreateContentPageAsync(CreateContentPageDto createDto, Guid userId);
    Task<ApiResponse<ContentPageDto>> UpdateContentPageAsync(Guid id, UpdateContentPageDto updateDto, Guid userId);
    Task<ApiResponse<bool>> DeleteContentPageAsync(Guid id);

    // FAQs
    Task<ApiResponse<PaginatedResult<FaqDto>>> GetFaqsAsync(
        string? search = null, string? category = null, bool? isPublished = null, int page = 1, int limit = 20);
    Task<ApiResponse<List<FaqCategoryDto>>> GetFaqCategoriesAsync();
    Task<ApiResponse<FaqDto>> GetFaqByIdAsync(Guid id);
    Task<ApiResponse<FaqDto>> CreateFaqAsync(CreateFaqDto createDto, Guid userId);
    Task<ApiResponse<FaqDto>> UpdateFaqAsync(Guid id, UpdateFaqDto updateDto, Guid userId);
    Task<ApiResponse<bool>> DeleteFaqAsync(Guid id);
    Task<ApiResponse<bool>> MarkFaqAsHelpfulAsync(Guid faqId, bool isHelpful, Guid? userId = null, string? ipAddress = null);

    // Contact Submissions
    Task<ApiResponse<PaginatedResult<ContactSubmissionDto>>> GetContactSubmissionsAsync(
        string? search = null, string? status = null, string? priority = null, string? category = null,
        DateTime? createdFrom = null, DateTime? createdTo = null, int page = 1, int limit = 20);
    Task<ApiResponse<ContactSubmissionDto>> CreateContactSubmissionAsync(CreateContactSubmissionDto createDto);
    Task<ApiResponse<ContactSubmissionDto>> UpdateContactSubmissionAsync(Guid id, UpdateContactSubmissionDto updateDto, Guid userId);

    // Statistics and Analytics
    Task<ApiResponse<ContentStatsDto>> GetContentStatsAsync();
}
