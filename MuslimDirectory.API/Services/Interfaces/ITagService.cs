using MuslimDirectory.API.Models.DTOs.Tag;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface ITagService
{
    Task<ApiResponse<List<TagDto>>> GetTagsAsync(
        int page = 1,
        int pageSize = 10,
        string? search = null,
        bool includeInactive = false,
        string? sortBy = null,
        string? sortOrder = null);

    Task<ApiResponse<TagDto>> GetTagByIdAsync(Guid id);

    Task<ApiResponse<TagWithListingsDto>> GetTagWithListingsAsync(
        Guid id, 
        int page = 1, 
        int pageSize = 10, 
        string? status = null, 
        string? complianceStatus = null);

    Task<ApiResponse<TagDto>> CreateTagAsync(CreateTagDto createDto, Guid userId);

    Task<ApiResponse<TagDto>> UpdateTagAsync(Guid id, UpdateTagDto updateDto, Guid userId);

    Task<ApiResponse<bool>> DeleteTagAsync(Guid id, Guid userId);

    Task<ApiResponse<List<TagListDto>>> GetPopularTagsAsync(int limit = 20);

    Task<ApiResponse<List<TagListDto>>> SearchTagsAsync(string query, int limit = 10);
}
