using MuslimDirectory.API.Models.DTOs.Utilities;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface IFileBaseService
{
    /// <summary>
    /// Upload a file to FileBase bucket
    /// </summary>
    /// <param name="file">The file to upload</param>
    /// <param name="folder">Optional folder path</param>
    /// <param name="customFileName">Optional custom filename</param>
    /// <param name="makePublic">Whether to make the file publicly accessible</param>
    /// <returns>File upload response with public URL</returns>
    Task<ApiResponse<FileUploadResponse>> UploadFileAsync(IFormFile file, string? folder = null, string? customFileName = null, bool makePublic = true);

    /// <summary>
    /// Delete a file from FileBase bucket
    /// </summary>
    /// <param name="key">The file key/path in the bucket</param>
    /// <returns>Delete operation result</returns>
    Task<ApiResponse<FileDeleteResponse>> DeleteFileAsync(string key);

    /// <summary>
    /// Get the public URL for a file
    /// </summary>
    /// <param name="key">The file key/path in the bucket</param>
    /// <returns>Public URL of the file</returns>
    Task<ApiResponse<string>> GetFileUrlAsync(string key);

    /// <summary>
    /// Check if a file exists in the bucket
    /// </summary>
    /// <param name="key">The file key/path in the bucket</param>
    /// <returns>True if file exists, false otherwise</returns>
    Task<ApiResponse<bool>> FileExistsAsync(string key);
}
