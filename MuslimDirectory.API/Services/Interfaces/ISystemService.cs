using MuslimDirectory.API.Models.DTOs.System;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface ISystemService
{
    // Public system endpoints
    Task<ApiResponse<SystemHealthDto>> GetSystemHealthAsync();
    Task<ApiResponse<SystemSettingsDto>> GetSystemSettingsAsync();
    Task<ApiResponse<SystemVersionDto>> GetSystemVersionAsync();
    Task<ApiResponse<List<CountryDto>>> GetCountriesAsync(bool includeStates = false, bool includeCities = false);

    // Helper methods
    Task<ApiResponse<List<CountryDto>>> GetSupportedCountriesAsync();
    Task<ApiResponse<CountryDto>> GetCountryByCodeAsync(string countryCode, bool includeStates = false, bool includeCities = false);
    Task<ApiResponse<List<StateProvinceDto>>> GetStatesByCountryAsync(string countryCode);
    Task<ApiResponse<List<CityDto>>> GetCitiesByStateAsync(Guid stateId);
    Task<ApiResponse<List<CityDto>>> GetMajorCitiesByCountryAsync(string countryCode);
}
