namespace MuslimDirectory.API.Services.Interfaces;

public interface ICookieService
{
    void SetAccessTokenCookie(HttpResponse response, string token);
    void SetRefreshTokenCookie(HttpResponse response, string refreshToken);
    void SetAuthCookies(HttpResponse response, string accessToken, string refreshToken);
    string? GetAccessTokenFromCookie(HttpRequest request);
    string? GetRefreshTokenFromCookie(HttpRequest request);
    void ClearAuthCookies(HttpResponse response);
    void ClearAccessTokenCookie(HttpResponse response);
    void ClearRefreshTokenCookie(HttpResponse response);
}
