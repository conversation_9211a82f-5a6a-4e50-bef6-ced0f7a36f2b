# **API Specifications**

## **API Overview**

**Base URL:** <https://muslimdirectory.xgeno.com/api/v1>

**Content Type:** application/json

**Authentication:** <PERSON><PERSON> (JWT)

**Rate Limiting:**

- Authenticated users: 1000 requests/hour
- Guest users: 100 requests/hour

**Error Response Format:**

{

"success": false,

"error": {

"code": "ERROR_CODE",

"message": "Human readable error message",

"details": {},

"field": "fieldName"

},

"timestamp": "2025-06-21T10:30:00Z"

}

**Success Response Format:**

{

"success": true,

"data": {},

“message: "Human readable message",

"meta": {

"pagination": {},

"timestamp": "2025-06-21T10:30:00Z"

}

}

## **Authentication & Authorization**

### **POST /auth/signup**

Register a new user account.

**Request Body:**

{

"firstName": "<PERSON>",

"lastName": "<PERSON>e",

"email": "<<EMAIL>>",

"password": "securePassword123",

"phoneNumber": "+**********",

"country": "US",

"city": "New York",

"preferredLanguage": "EN",

"gender": "M",

"acceptTerms": true,

"acceptPrivacy": true

}

**Response (201 Created):**

{

"success": true,

"provider": "Local",

"data": {

"user": {

"id": "uuid",

"email": "<<EMAIL>>",

"firstName": "John",

"lastName": "Doe",

"userType": "Regular",

"preferredLanguage": "EN",

"profilePicture": "https://...",

"isEmailVerified": true

},

"token": "jwt_token_here",

}

“message”: {“User created successfully”}

}

### **POST /auth/signin**

Authenticate user and return access token.

**Request Body:**

{

"email": "<<EMAIL>>",

"password": "securePassword123",

}

**Response (200 OK):**

{

"success": true,

"provider": "Local",

"data": {

"user": {

"id": "uuid",

"email": "<<EMAIL>>",

"firstName": "John",

"lastName": "Doe",

"userType": "Regular",

"preferredLanguage": "EN",

"profilePicture": "https://...",

"isEmailVerified": true

},

"token": "jwt_token_here",

"refreshToken": "refresh_token_here",

},

“message”:””,

}

### **POST /auth/social-login**

Authenticate using social providers (Google, Apple).

**Response (200 OK):**

**:**

{

"success": true,

"provider": "Google", “ data”:{

"user": {

"email": "<<EMAIL>>",

"firstName": "John",

"lastName": "Doe",

"providerId": "google_user_id",

"profilePicture": "https://..."

"isEmailVerified": true

},

"token": "provider_access_token",

},

"message": "",

}

### **POST /auth/forgot-password**

Request password reset.

**Request Body:**

{

"email": "<<EMAIL>>"

}

**Response (200 OK):**

{

"success": true,

"data": {

},

"message": "If this email exists in our system, you will receive password reset instructions."

}

### **POST /auth/reset-password**

Reset password using token.

**Request Body:**

{

"token": "reset_token",

"newPassword": "newSecurePassword123"

},

**Response (200 OK):**

{

"success": true,

"data": { },

"message": "Your password has been successfully reset."

}

### **POST /auth/verify-email**

Verify email address.

**Request Body:**

{

"token": "verification_token"

}

**Response (200 OK):**

{

"success": true,

"data": {

},

"message": "Email verified successfully. You can now access all features."

}

### **POST /auth/resend-verification**

Resend email verification.

**Request Body:**

{

"email": "<<EMAIL>>"

}

**Response (200 OK):**

{

"success": true,

"data": {

"message": "If this email is registered with us, a new verification link has been sent to your inbox. Please check your email.."

},

### **POST /auth/refresh-token**

Refresh access token.

**Request Body:**

{

"accessToken": "access_token_here"

}

**Response (200 OK):**

{

"success": true,

"provider": "Local",

"token": "provider_access_token",

“ data”:{

"user": {

"email": "<<EMAIL>>",

"firstName": "John",

"lastName": "Doe",

"providerId": "google_user_id",

"profilePicture": "https://..."

"isEmailVerified": true

},

"token": "provider_access_token",

},

"message": "",

}

### **POST /auth/logout**

Logout user and invalidate tokens.

**Headers:** Authorization: Bearer token  
<br/>

## **User Management APIs**

### **GET /users/profile**

Get current user's profile.

**Headers:** Authorization: Bearer token

**Response (200 OK):**

{

"success": true,

"data": {

"id": "uuid",

"email": "<<EMAIL>>",

"firstName": "John",

"lastName": "Doe",

"profilePicture": "https://...",

"gender": "M",

"country": "US",

"city": "New York",

"phoneNumber": "+**********",

"preferredLanguage": "EN",

"isEmailVerified": true,

"isPhoneVerified": false,

"userType": "Regular",

"memberSince": "2025-01-01T00:00:00Z",

"stats": {

"reviewsCount": 15,

"favoritesCount": 8

},

"lastLoginAt": "2025-06-21T10:00:00Z"

}

}

### **PUT /users/profile**

Update user profile.

**Headers:** Authorization: Bearer token

**Request Body:**

{

"firstName": "Jane",

"lastName": "Smith",

"phoneNumber": "+**********",

"country": "CA",

"city": "Toronto",

"preferredLanguage": "EN",

"gender": "F",

"profilePicture": "https://..."

}

### **PUT /users/change-password**

Change user password.

**Headers:** Authorization: Bearer token

**Request Body:**

{

"currentPassword": "oldPassword123",

"newPassword": "newPassword456"

}

### **GET /users/preferences**

Get user preferences.

**Headers:** Authorization: Bearer token

**Response (200 OK):**

{

"success": true,

"data": {

"language": "EN",

"notifications": {

"newApps": true,

"reviewResponses": true,

"weeklyRecommendations": false

},

"privacy": {

"profileVisibility": "public",

"reviewDisplayName": "firstName"

},

"appPreferences": {

"defaultSortOrder": "rating",

"preferredPlatforms": \["iOS", "Android"\]

}

}

}

### **PUT /users/preferences**

Update user preferences.

**Headers:** Authorization: Bearer token

**Request Body:**

{

"language": "AR",

"notifications": {

"newApps": false,

"reviewResponses": true,

"weeklyRecommendations": true

},

"privacy": {

"profileVisibility": "private",

"reviewDisplayName": "initials"

},

"appPreferences": {

"defaultSortOrder": "newest",

"preferredPlatforms": \["iOS", "Web"\]

}

}

## **Organization Management APIs**

### **GET /organizations**

Get list of organizations (public endpoint).

**Query Parameters:**

- page (integer, default: 1)
- limit (integer, default: 20, max: 100)
- search (string)
- isVerified (boolean)
- country (string)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"name": "Islamic App Dev Co",

"description": "Developing authentic Islamic applications since 2020",

"logoURL": "https://...",

"website": "<https://islamicappdev.com>",

"country": "US",

"isVerified": true,

"listingsCount": 12,

"averageRating": 4.5,

"createdAt": "2025-01-01T00:00:00Z"

}

\],

"meta": {

"pagination": {

"page": 1,

"limit": 20,

"total": 150,

"totalPages": 8

}

}

}

### **POST /organizations**

Create new organization.

**Headers:** Authorization: Bearer token

**Request Body:**

{

"name": "My Islamic Organization",

"description": "Description of the organization and its mission",

"logoURL": "<https://example.com/logo.png>",

"website": "<https://myorg.com>",

"email": "<<EMAIL>>",

"phoneNumber": "+**********",

"address": "123 Main St, City, State, ZIP",

"country": "US",

"islamicComplianceCertificate": "<https://example.com/certificate.pdf>"

}

**Response (201 Created):**

{

"success": true,

"data": {

"id": "uuid",

"name": "My Islamic Organization",

"isVerified": false,

"status": "pending_verification",

"message": "Organization created successfully. Verification is pending."

}

}

### **GET /organizations/{id}**

Get organization details.

**Response (200 OK):**

{

"success": true,

"data": {

"id": "uuid",

"name": "Islamic App Dev Co",

"description": "Developing authentic Islamic applications since 2020",

"logoURL": "https://...",

"website": "<https://islamicappdev.com>",

"email": "<<EMAIL>>",

"phoneNumber": "+**********",

"address": "123 Main St, New York, NY, 10001",

"country": "US",

"isVerified": true,

"islamicComplianceCertificate": "https://...",

"stats": {

"listingsCount": 15,

"totalReviews": 245,

"averageRating": 4.6

},

"listings": \[

{

"id": "uuid",

"title": "Prayer Times Pro",

"logoURL": "https://...",

"rating": 4.5,

"reviewCount": 120

}

\],

"createdAt": "2025-01-01T00:00:00Z"

}

}

### **PUT /organizations/{id}**

Update organization (owners/admins only).

**Headers:** Authorization: Bearer token

### **GET /organizations/{id}/members**

Get organization members (members only).

**Headers:** Authorization: Bearer token

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"user": {

"id": "uuid",

"firstName": "John",

"lastName": "Doe",

"email": "<<EMAIL>>",

"profilePicture": "https://..."

},

"role": "Owner",

"joinedAt": "2025-01-01T00:00:00Z"

}

\]

}

### **POST /organizations/{id}/members**

Add organization member (owners only).

**Headers:** Authorization: Bearer token

**Request Body:**

{

"email": "<<EMAIL>>",

"role": "Admin"

}

### **GET /organizations/my-organizations**

Get organizations where user is a member.

**Headers:** Authorization: Bearer token

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"name": "My Organization",

"logoURL": "https://...",

"role": "Owner",

"listingsCount": 5,

"pendingSubmissions": 2

}

\]

}

## **Listing Management APIs**

### **GET /listings**

Get listings with filtering and pagination (public endpoint).

**Query Parameters:**

- page (integer, default: 1)
- limit (integer, default: 20, max: 100)
- search (string)
- category (string, category ID)
- platform (string: iOS, Android, Web, Desktop)
- language (string: EN, AR, UR, etc.)
- pricingModel (string: Free, Paid, Freemium, Subscription)
- complianceStatus (string: Verified, Pending)
- featured (boolean)
- sortBy (string: newest, oldest, rating, popular, title)
- minRating (integer: 1-5)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"title": "Prayer Times Pro",

"shortDescription": "Accurate prayer times for Muslims worldwide with Qibla direction",

"logoURL": "https://...",

"platformType": "Mobile App",

"supportedPlatforms": \["iOS", "Android"\],

"pricingModel": "Free",

"price": 0,

"currency": "USD",

"islamicComplianceStatus": "Verified",

"rating": {

"average": 4.5,

"count": 120

},

"organization": {

"id": "uuid",

"name": "Islamic Apps Inc",

"logoURL": "https://...",

"isVerified": true

},

"primaryCategory": {

"id": "uuid",

"name": "Prayer & Worship"

},

"featuredLevel": 1,

"viewCount": 1250,

"createdAt": "2025-01-01T00:00:00Z",

"updatedAt": "2025-06-01T00:00:00Z"

}

\],

"meta": {

"pagination": {

"page": 1,

"limit": 20,

"total": 500,

"totalPages": 25

},

"filters": {

"appliedFilters": {

"category": "prayer",

"platform": "iOS"

}

}

}

}

### **GET /listings/featured**

Get featured listings for homepage.

**Query Parameters:**

- limit (integer, default: 6)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"title": "Featured App",

"shortDescription": "Brief description",

"logoURL": "https://...",

"rating": 4.8,

"reviewCount": 95,

"featuredLevel": 2,

"organization": {

"name": "Developer Name",

"isVerified": true

}

}

\]

}

### **GET /listings/new-releases**

Get recently added listings.

**Query Parameters:**

- limit (integer, default: 10)
- days (integer, default: 30)

### **GET /listings/trending**

Get trending listings.

**Query Parameters:**

- timeframe (string: day, week, month)
- limit (integer, default: 10)

### **GET /listings/recommendations**

Get personalized recommendations (authenticated users).

**Headers:** Authorization: Bearer token

**Response (200 OK):**

{

"success": true,

"data": {

"sections": \[

{

"title": "Based on your interests",

"listings": \[\]

},

{

"title": "Popular in United States",

"listings": \[\]

},

{

"title": "Similar to your favorites",

"listings": \[\]

}

\]

}

}

### **GET /listings/{id}**

Get detailed listing information.

**Response (200 OK):**

{

"success": true,

"data": {

"id": "uuid",

"title": "Prayer Times Pro",

"shortDescription": "Accurate prayer times for Muslims worldwide",

"fullDescription": "Detailed description with features, benefits, and Islamic compliance information...",

"logoURL": "https://...",

"website": "<https://prayertimespro.com>",

"platformType": "Mobile App",

"supportedPlatforms": \["iOS", "Android"\],

"appStoreURL": "<https://apps.apple.com/app/>...",

"playStoreURL": "<https://play.google.com/store/apps/>...",

"websiteURL": null,

"pricingModel": "Free",

"price": 0,

"currency": "USD",

"islamicComplianceStatus": "Verified",

"complianceNotes": "Verified by Islamic Digital Compliance Board. App follows Islamic prayer time calculations and includes proper Qibla direction.",

"organization": {

"id": "uuid",

"name": "Islamic Apps Inc",

"logoURL": "https://...",

"isVerified": true,

"website": "<https://islamicapps.com>",

"email": "<<EMAIL>>"

},

"categories": \[

{

"id": "uuid",

"name": "Prayer & Worship",

"isPrimary": true

},

{

"id": "uuid",

"name": "Islamic Calendar",

"isPrimary": false

}

\],

"tags": \[

{

"id": "uuid",

"name": "Salah"

},

{

"id": "uuid",

"name": "Qibla"

}

\],

"media": \[

{

"id": "uuid",

"mediaType": "Screenshot",

"mediaURL": "https://...",

"thumbnailURL": "https://...",

"sortOrder": 1

}

\],

"rating": {

"average": 4.5,

"count": 120,

"islamicComplianceAverage": 4.8,

"distribution": {

"5": 60,

"4": 30,

"3": 20,

"2": 5,

"1": 5

}

},

"primaryLanguage": "EN",

"supportedLanguages": \["EN", "AR", "UR", "ID"\],

"viewCount": 1500,

"isFavorited": false,

"relatedListings": \[

{

"id": "uuid",

"title": "Similar App",

"logoURL": "https://...",

"rating": 4.2

}

\],

"createdAt": "2025-01-01T00:00:00Z",

"updatedAt": "2025-06-01T00:00:00Z"

}

}

### **POST /listings**

Submit new listing.

**Headers:** Authorization: Bearer token

**Request Body:**

{

"title": "My Islamic App",

"shortDescription": "Brief description under 400 characters",

"fullDescription": "Detailed description with features and benefits",

"logoURL": "<https://example.com/logo.png>",

"website": "<https://myapp.com>",

"platformType": "Mobile App",

"supportedPlatforms": \["iOS", "Android"\],

"appStoreURL": "<https://apps.apple.com/app/>...",

"playStoreURL": "<https://play.google.com/store/apps/>...",

"websiteURL": null,

"pricingModel": "Free",

"price": 0,

"currency": "USD",

"organizationId": "uuid",

"categoryIds": \["uuid1", "uuid2"\],

"primaryCategoryId": "uuid1",

"tagIds": \["uuid1", "uuid2"\],

"primaryLanguage": "EN",

"supportedLanguages": \["EN", "AR"\],

"metaTitle": "SEO optimized title",

"metaDescription": "SEO description",

"media": \[

{

"mediaType": "Screenshot",

"mediaURL": "<https://example.com/screenshot1.png>",

"sortOrder": 1

}

\]

}

**Response (201 Created):**

{

"success": true,

"data": {

"id": "uuid",

"status": "Pending",

"message": "Listing submitted successfully and is under review. You will be notified once the review is complete."

}

}

### **PUT /listings/{id}**

Update listing (organization members only).

**Headers:** Authorization: Bearer token

### **DELETE /listings/{id}**

Delete listing (organization owners only).

**Headers:** Authorization: Bearer token

### **POST /listings/{id}/view**

Track listing view (for analytics).

**Request Body:**

{

"source": "search",

"referrer": "category_page"

}

### **GET /listings/{id}/analytics**

Get listing analytics (organization members only).

**Headers:** Authorization: Bearer token

**Query Parameters:**

- startDate (string, ISO date)
- endDate (string, ISO date)
- granularity (string: day, week, month)

**Response (200 OK):**

{

"success": true,

"data": {

"summary": {

"totalViews": 1500,

"totalReviews": 120,

"averageRating": 4.5,

"favoriteCount": 80

},

"charts": {

"viewsOverTime": \[

{"date": "2025-06-15", "views": 45},

{"date": "2025-06-16", "views": 52}

\],

"ratingDistribution": {

"5": 60,

"4": 30,

"3": 20,

"2": 5,

"1": 5

},

"geographicData": \[

{"country": "US", "views": 450},

{"country": "UK", "views": 230}

\]

}

}

}

## **Category & Tag APIs**

### **GET /categories**

Get all categories (public endpoint).

**Query Parameters:**

- parent (string, parent category ID)
- includeInactive (boolean, admin only)
- includeCount (boolean, default: true)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"name": "Prayer & Worship",

"description": "Applications for prayer times, Qibla direction, and worship",

"iconURL": "https://...",

"parentCategoryId": null,

"sortOrder": 1,

"isActive": true,

"listingCount": 45,

"subcategories": \[

{

"id": "uuid",

"name": "Prayer Times",

"description": "Accurate prayer time applications",

"listingCount": 25,

"iconURL": "https://..."

},

{

"id": "uuid",

"name": "Qibla Direction",

"listingCount": 15,

"iconURL": "https://..."

}

\]

},

{

"id": "uuid",

"name": "Quran & Tafsir",

"description": "Quran reading, recitation, and interpretation",

"iconURL": "https://...",

"listingCount": 38,

"subcategories": \[\]

}

\]

}

### **GET /categories/{id}**

Get category details with listings.

**Query Parameters:**

- includeListings (boolean, default: false)
- listingLimit (integer, default: 10)

**Response (200 OK):**

{

"success": true,

"data": {

"id": "uuid",

"name": "Prayer & Worship",

"description": "Applications for prayer times, Qibla direction, and worship",

"iconURL": "https://...",

"parentCategory": null,

"subcategories": \[

{

"id": "uuid",

"name": "Prayer Times",

"listingCount": 25

}

\],

"listingCount": 45,

"isActive": true,

"listings": \[\],

"createdAt": "2025-01-01T00:00:00Z"

}

}

### **GET /tags**

Get all tags (public endpoint).

**Query Parameters:**

- search (string)
- popular (boolean, default: false)
- limit (integer, default: 50)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"name": "Salah",

"description": "Prayer-related applications and tools",

"usageCount": 25,

"isActive": true

},

{

"id": "uuid",

"name": "Qibla",

"description": "Qibla direction and compass applications",

"usageCount": 18,

"isActive": true

}

\]

}

## **Review & Rating APIs**

### **GET /reviews**

Get reviews for a listing.

**Query Parameters:**

- listingId (string, required)
- page (integer, default: 1)
- limit (integer, default: 20)
- sortBy (string: newest, oldest, rating_high, rating_low, helpful)
- rating (integer: 1-5)
- status (string: approved - public default, all - admin only)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"user": {

"id": "uuid",

"firstName": "John",

"lastName": "D.",

"profilePicture": "https://...",

"displayName": "John D."

},

"rating": 5,

"title": "Excellent app for prayer times",

"reviewText": "Very accurate prayer times and user-friendly interface. The Qibla direction feature is spot-on!",

"islamicComplianceRating": 5,

"helpfulCount": 15,

"reportCount": 0,

"status": "Approved",

"createdAt": "2025-06-01T00:00:00Z",

"updatedAt": "2025-06-01T00:00:00Z",

"response": {

"id": "uuid",

"responseText": "Thank you for your positive feedback! We're glad you find our app helpful.",

"respondedBy": {

"name": "Islamic Apps Inc",

"type": "Organization",

"logoURL": "https://..."

},

"createdAt": "2025-06-02T00:00:00Z"

}

}

\],

"meta": {

"pagination": {

"page": 1,

"limit": 20,

"total": 120,

"totalPages": 6

},

"summary": {

"averageRating": 4.5,

"islamicComplianceAverage": 4.8,

"totalReviews": 120,

"ratingDistribution": {

"5": 60,

"4": 30,

"3": 20,

"2": 5,

"1": 5

}

}

}

}

### **POST /reviews**

Submit a review.

**Headers:** Authorization: Bearer token

**Request Body:**

{

"listingId": "uuid",

"rating": 5,

"title": "Great app for daily prayers!",

"reviewText": "This app has been very helpful for my daily prayers. The notifications are timely and the interface is clean and easy to use.",

"islamicComplianceRating": 5

}

**Response (201 Created):**

{

"success": true,

"data": {

"id": "uuid",

"status": "Pending",

"message": "Review submitted successfully and is under moderation. You will be notified once it's approved."

}

}

### **PUT /reviews/{id}**

Update review (author only, within edit window).

**Headers:** Authorization: Bearer token

**Request Body:**

{

"rating": 4,

"title": "Updated review title",

"reviewText": "Updated review content...",

"islamicComplianceRating": 4

}

### **DELETE /reviews/{id}**

Delete review (author only if not approved, admin always).

**Headers:** Authorization: Bearer token

### **POST /reviews/{id}/helpful**

Mark review as helpful/unhelpful.

**Headers:** Authorization: Bearer token

**Request Body:**

{

"isHelpful": true

}

**Response (200 OK):**

{

"success": true,

"data": {

"helpfulCount": 16,

"userVote": true

}

}

### **POST /reviews/{id}/report**

Report a review.

**Headers:** Authorization: Bearer token

**Request Body:**

{

"reasonCode": "Spam",

"reasonText": "This review appears to be spam and not a genuine user experience"

}

### **POST /reviews/{id}/response**

Respond to review (organization members only).

**Headers:** Authorization: Bearer token

**Request Body:**

{

"responseText": "Thank you for your feedback! We're glad you enjoyed our app. If you have any suggestions for improvement, please feel free to contact us."

}

**Response (201 Created):**

{

"success": true,

"data": {

"id": "uuid",

"message": "Response posted successfully"

}

}

### **GET /users/reviews**

Get current user's reviews.

**Headers:** Authorization: Bearer token

**Query Parameters:**

- page (integer, default: 1)
- limit (integer, default: 20)
- status (string: all, pending, approved, rejected)
- sortBy (string: newest, oldest, rating)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"listing": {

"id": "uuid",

"title": "Prayer Times Pro",

"logoURL": "https://...",

"organization": {

"name": "Islamic Apps Inc"

}

},

"rating": 5,

"title": "Great app!",

"reviewText": "Very helpful for daily prayers...",

"islamicComplianceRating": 5,

"status": "Approved",

"helpfulCount": 8,

"createdAt": "2025-06-01T00:00:00Z",

"response": {

"responseText": "Thank you for your feedback!",

"createdAt": "2025-06-02T00:00:00Z"

}

}

\],

"meta": {

"pagination": {

"page": 1,

"limit": 20,

"total": 15,

"totalPages": 1

},

"stats": {

"totalReviews": 15,

"pendingReviews": 2,

"approvedReviews": 12,

"rejectedReviews": 1

}

}

}

## **Search & Discovery APIs**

### **GET /search**

Global search across listings.

**Query Parameters:**

- q (string, search query, required)
- type (string: listings, organizations, default: listings)
- category (string, category ID)
- platform (string)
- language (string)
- pricingModel (string)
- minRating (integer)
- page (integer, default: 1)
- limit (integer, default: 20)

**Response (200 OK):**

{

"success": true,

"data": {

"listings": \[

{

"id": "uuid",

"title": "Prayer Times Pro",

"shortDescription": "Accurate prayer times...",

"logoURL": "https://...",

"rating": 4.5,

"reviewCount": 120,

"organization": {

"name": "Islamic Apps Inc",

"isVerified": true

},

"islamicComplianceStatus": "Verified"

}

\],

"organizations": \[

{

"id": "uuid",

"name": "Prayer App Developers",

"logoURL": "https://...",

"isVerified": true,

"listingsCount": 5

}

\],

"suggestions": \[

"prayer times",

"prayer app",

"salah times"

\]

},

"meta": {

"query": "prayer",

"totalResults": 45,

"searchTime": 0.125,

"pagination": {

"page": 1,

"limit": 20,

"total": 45,

"totalPages": 3

}

}

}

### **GET /search/suggestions**

Get search suggestions as user types.

**Query Parameters:**

- q (string, partial query)
- limit (integer, default: 10)

**Response (200 OK):**

{

"success": true,

"data": {

"suggestions": \[

"prayer times",

"prayer app",

"quran recitation",

"islamic calendar"

\]

}

}

### **GET /search/recent**

Get recent searches for authenticated user.

**Headers:** Authorization: Bearer token

**Response (200 OK):**

{

"success": true,

"data": \[

{

"query": "prayer times",

"searchedAt": "2025-06-20T15:30:00Z"

},

{

"query": "quran app",

"searchedAt": "2025-06-19T10:15:00Z"

}

\]

}

### **DELETE /search/recent**

Clear recent searches.

**Headers:** Authorization: Bearer token

## **User Interaction APIs**

### **GET /users/favorites**

Get user's favorite listings.

**Headers:** Authorization: Bearer token

**Query Parameters:**

- page (integer, default: 1)
- limit (integer, default: 20)
- sortBy (string: newest, oldest, title, rating)
- search (string)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"listing": {

"id": "uuid",

"title": "Prayer Times Pro",

"shortDescription": "Accurate prayer times...",

"logoURL": "https://...",

"rating": 4.5,

"reviewCount": 120,

"organization": {

"name": "Islamic Apps Inc",

"isVerified": true

},

"pricingModel": "Free",

"supportedPlatforms": \["iOS", "Android"\]

},

"addedAt": "2025-06-01T00:00:00Z"

}

\],

"meta": {

"pagination": {

"page": 1,

"limit": 20,

"total": 8,

"totalPages": 1

}

}

}

### **POST /users/favorites**

Add listing to favorites.

**Headers:** Authorization: Bearer token

**Request Body:**

{

"listingId": "uuid"

}

**Response (201 Created):**

{

"success": true,

"data": {

"message": "Listing added to favorites successfully",

"isFavorited": true

}

}

### **DELETE /users/favorites/{listingId}**

Remove listing from favorites.

**Headers:** Authorization: Bearer token

**Response (200 OK):**

{

"success": true,

"data": {

"message": "Listing removed from favorites",

"isFavorited": false

}

}

### **GET /users/favorites/check/{listingId}**

Check if listing is favorited by user.

**Headers:** Authorization: Bearer token

**Response (200 OK):**

{

"success": true,

"data": {

"isFavorited": true,

"addedAt": "2025-06-01T00:00:00Z"

}

}

## **Content Management APIs**

### **GET /content/pages**

Get content pages (public endpoint).

**Query Parameters:**

- slug (string)
- language (string, default: EN)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"title": "About Us",

"slug": "about-us",

"content": "&lt;h1&gt;About Muslim Directory&lt;/h1&gt;&lt;p&gt;Our mission is to...&lt;/p&gt;",

"metaTitle": "About Us - Muslim Directory",

"metaDescription": "Learn about our mission to connect Muslims with authentic digital resources",

"pageLanguage": "EN",

"isPublished": true,

"updatedAt": "2025-06-01T00:00:00Z"

}

\]

}

### **GET /content/pages/{slug}**

Get specific content page by slug.

**Query Parameters:**

- language (string, default: EN)

**Response (200 OK):**

{

"success": true,

"data": {

"id": "uuid",

"title": "About Us",

"slug": "about-us",

"content": "&lt;h1&gt;About Muslim Directory&lt;/h1&gt;&lt;p&gt;Our mission is to connect Muslims worldwide with authentic, trustworthy Islamic digital resources...&lt;/p&gt;",

"metaTitle": "About Us - Muslim Directory",

"metaDescription": "Learn about our mission...",

"pageLanguage": "EN",

"updatedAt": "2025-06-01T00:00:00Z"

}

}

### **GET /content/faq**

Get FAQ items.

**Query Parameters:**

- category (string)
- language (string, default: EN)
- search (string)

**Response (200 OK):**

{

"success": true,

"data": {

"categories": \[

{

"name": "Getting Started",

"faqs": \[

{

"id": "uuid",

"question": "How do I find Islamic apps on the platform?",

"answer": "You can browse apps by categories such as Prayer & Worship, Quran & Tafsir, etc., or use our search function...",

"sortOrder": 1,

"helpful": 25,

"notHelpful": 2

}

\]

},

{

"name": "Account Management",

"faqs": \[

{

"id": "uuid",

"question": "How do I create an account?",

"answer": "Click the 'Sign Up' button and fill in your details...",

"sortOrder": 1

}

\]

}

\]

}

}

### **POST /content/faq/{id}/helpful**

Rate FAQ helpfulness.

**Request Body:**

{

"isHelpful": true

}

### **GET /content/support/contact**

Get contact information and support options.

**Response (200 OK):**

{

"success": true,

"data": {

"email": "<<EMAIL>>",

"responseTime": "24-48 hours",

"categories": \[

{

"code": "general",

"name": "General Inquiry"

},

{

"code": "technical",

"name": "Technical Issue"

},

{

"code": "listing",

"name": "Listing Related"

},

{

"code": "account",

"name": "Account Issue"

}

\]

}

}

### **POST /content/support/contact**

Submit support request.

**Headers:** Authorization: Bearer token (optional)

**Request Body:**

{

"name": "John Doe",

"email": "<<EMAIL>>",

"category": "technical",

"subject": "Unable to submit app listing",

"message": "I'm having trouble submitting my app listing. The form keeps showing an error...",

"attachments": \[

{

"filename": "screenshot.png",

"url": "https://..."

}

\]

}

**Response (201 Created):**

{

"success": true,

"data": {

"ticketId": "TICKET-2025-001234",

"message": "Support request submitted successfully. You will receive a response within 24-48 hours.",

"expectedResponseTime": "2025-06-23T10:30:00Z"

}

}

## **Admin & Moderation APIs**

### **GET /admin/dashboard**

Get admin dashboard statistics.

**Headers:** Authorization: Bearer token (Admin only)

**Query Parameters:**

- timeframe (string: day, week, month, year, default: month)

**Response (200 OK):**

{

"success": true,

"data": {

"stats": {

"users": {

"total": 50000,

"new": 500,

"active": 12000

},

"listings": {

"total": 1000,

"pending": 25,

"approved": 950,

"rejected": 15

},

"reviews": {

"total": 5000,

"pending": 100,

"approved": 4850

},

"organizations": {

"total": 150,

"verified": 120,

"pending": 30

}

},

"charts": {

"userGrowth": \[

{"date": "2025-06-01", "count": 48500},

{"date": "2025-06-15", "count": 49750}

\],

"listingsByCategory": \[

{"category": "Prayer & Worship", "count": 245},

{"category": "Quran & Tafsir", "count": 180}

\],

"reviewsOverTime": \[

{"date": "2025-06-15", "count": 45},

{"date": "2025-06-16", "count": 52}

\]

},

"alerts": \[

{

"type": "info",

"message": "25 listings pending review",

"actionUrl": "/admin/moderation-queue?type=listings"

}

\]

}

}

### **GET /admin/moderation-queue**

Get items requiring moderation.

**Headers:** Authorization: Bearer token (Admin/Moderator only)

**Query Parameters:**

- entityType (string: Listing, Review, User, Organization)
- status (string: Pending, InProgress, Completed)
- assignedTo (string, user ID)
- page (integer, default: 1)
- limit (integer, default: 20)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"queueId": "uuid",

"entityType": "Listing",

"entityId": "uuid",

"entity": {

"title": "New Prayer App",

"organization": {

"name": "Developer Name"

},

"submittedAt": "2025-06-19T10:00:00Z"

},

"priority": "Medium",

"assignedTo": {

"id": "uuid",

"name": "Moderator Ahmed"

},

"status": "Pending",

"createdAt": "2025-06-19T10:00:00Z"

}

\],

"meta": {

"pagination": {

"page": 1,

"limit": 20,

"total": 35,

"totalPages": 2

},

"summary": {

"totalPending": 35,

"myAssigned": 8,

"overdue": 3

}

}

}

### **PUT /admin/moderation-queue/{queueId}**

Update moderation item.

**Headers:** Authorization: Bearer token (Admin/Moderator only)

**Request Body:**

{

"status": "Completed",

"actionTaken": "Approve",

"notes": "Listing approved - meets all Islamic compliance requirements and quality standards",

"assignedTo": "uuid"

}

### **GET /admin/moderation-queue/{queueId}**

Get detailed moderation item.

**Headers:** Authorization: Bearer token (Admin/Moderator only)

**Response (200 OK):**

{

"success": true,

"data": {

"queueId": "uuid",

"entityType": "Listing",

"entityId": "uuid",

"entity": {

"title": "New Prayer App",

"description": "Complete app details...",

"organization": {

"name": "Developer Name",

"isVerified": true

},

"submittedBy": {

"name": "John Doe",

"email": "<<EMAIL>>"

}

},

"guidelines": {

"islamicCompliance": \[

"App must not contain any haram content",

"Prayer times must be calculated according to Islamic principles",

"Qibla direction must be accurate"

\],

"technicalRequirements": \[

"App must be functional and accessible",

"Screenshots must be current and representative",

"Description must be accurate and complete"

\]

},

"history": \[

{

"action": "Submitted",

"by": "John Doe",

"date": "2025-06-19T10:00:00Z"

}

\]

}

}

### **GET /admin/users**

Get users list for admin management.

**Headers:** Authorization: Bearer token (Admin only)

**Query Parameters:**

- page (integer, default: 1)
- limit (integer, default: 20)
- userType (string: Regular, Developer, Organization, Admin, Moderator)
- status (string: active, inactive, suspended)
- search (string)
- country (string)
- sortBy (string: newest, oldest, name, email)

**Response (200 OK):**

{

"success": true,

"data": \[

{

"id": "uuid",

"firstName": "John",

"lastName": "Doe",

"email": "<<EMAIL>>",

"userType": "Regular",

"country": "US",

"isActive": true,

"isEmailVerified": true,

"registeredAt": "2025-01-15T00:00:00Z",

"lastLoginAt": "2025-06-20T08:30:00Z",

"stats": {

"reviewsCount": 15,

"favoritesCount": 8,

"organizationsCount": 0

}

}

\],

"meta": {

"pagination": {

"page": 1,

"limit": 20,

"total": 50000,

"totalPages": 2500

}

}

}

### **PUT /admin/users/{id}/status**

Update user status.

**Headers:** Authorization: Bearer token (Admin only)

**Request Body:**

{

"isActive": false,

"reason": "Violation of community guidelines - posting inappropriate reviews"

}

### **GET /admin/listings**

Get all listings for admin review.

**Headers:** Authorization: Bearer token (Admin/Moderator only)

**Query Parameters:**

- status (string: Draft, Pending, Approved, Rejected, Suspended)
- complianceStatus (string: Verified, Pending, Rejected, Under Review)
- page (integer, default: 1)
- limit (integer, default: 20)
- search (string)
- organization (string, organization ID)
- category (string, category ID)

### **PUT /admin/listings/{id}/status**

Update listing status.

**Headers:** Authorization: Bearer token (Admin/Moderator only)

**Request Body:**

{

"status": "Approved",

"islamicComplianceStatus": "Verified",

"complianceNotes": "Reviewed and approved by Islamic compliance team. App follows proper Islamic guidelines for prayer times and includes accurate Qibla direction.",

"moderatorNotes": "All technical and content requirements met. High quality submission.",

"featuredLevel": 1

}

### **GET /admin/reviews**

Get all reviews for moderation.

**Headers:** Authorization: Bearer token (Admin/Moderator only)

**Query Parameters:**

- status (string: Pending, Approved, Rejected, Flagged)
- page (integer, default: 1)
- limit (integer, default: 20)
- listing (string, listing ID)
- reported (boolean)

### **PUT /admin/reviews/{id}/status**

Update review status.

**Headers:** Authorization: Bearer token (Admin/Moderator only)

**Request Body:**

{

"status": "Approved",

"moderatorNotes": "Review is genuine and follows community guidelines"

}

## **System APIs**

### **GET /system/health**

Check system health status (public endpoint).

**Response (200 OK):**

{

"success": true,

"data": {

"status": "healthy",

"timestamp": "2025-06-21T10:30:00Z",

"version": "1.0.0",

"services": {

"database": "healthy",

"search": "healthy",

"cache": "healthy",

"storage": "healthy"

},

"uptime": 99.98

}

}

### **GET /system/settings**

Get public system settings.

**Response (200 OK):**

{

"success": true,

"data": {

"supportedLanguages": \[

{"code": "EN", "name": "English", "nativeName": "English"},

{"code": "AR", "name": "Arabic", "nativeName": "العربية"},

{"code": "UR", "name": "Urdu", "nativeName": "اردو"},

{"code": "ID", "name": "Indonesian", "nativeName": "Bahasa Indonesia"},

{"code": "TR", "name": "Turkish", "nativeName": "Türkçe"}

\],

"supportedPlatforms": \["iOS", "Android", "Web", "Desktop"\],

"pricingModels": \["Free", "Paid", "Freemium", "Subscription"\],

"fileUpload": {

"maxFileSize": 5242880,

"allowedFileTypes": \["jpg", "jpeg", "png", "gif", "pdf"\],

"maxFiles": 10

},

"features": {

"maintenanceMode": false,

"registrationEnabled": true,

"socialLoginEnabled": true,

"reviewModerationEnabled": true

},

"limits": {

"maxReviewLength": 2000,

"maxDescriptionLength": 4000,

"maxTagsPerListing": 10,

"maxCategoriesPerListing": 3

}

}

}

### **GET /system/version**

Get API version information.

**Response (200 OK):**

{

"success": true,

"data": {

"version": "1.0.0",

"releaseDate": "2025-06-01",

"environment": "production",

"features": \[

"User management",

"Listing management",

"Review system",

"Organization management",

"Content management",

"Admin panel"

\],

"changelog": \[

{

"version": "1.0.0",

"date": "2025-06-01",

"changes": \["Initial release with core features"\]

}

\]

}

}

### **GET /system/countries**

Get list of supported countries.

**Response (200 OK):**

{

"success": true,

"data": \[

{"code": "US", "name": "United States"},

{"code": "UK", "name": "United Kingdom"},

{"code": "SA", "name": "Saudi Arabia"},

{"code": "AE", "name": "United Arab Emirates"},

{"code": "PK", "name": "Pakistan"},

{"code": "ID", "name": "Indonesia"},

{"code": "TR", "name": "Turkey"}

\]

}

## **Rate Limiting & Security**

### **Rate Limiting Headers**

- X-RateLimit-Limit: Request limit per hour
- X-RateLimit-Remaining: Remaining requests in current window
- X-RateLimit-Reset: Unix timestamp when rate limit resets

### **Security Headers**

- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Strict-Transport-Security: max-age=31536000

### **Common HTTP Status Codes**

- 200 OK: Success
- 201 Created: Resource created successfully
- 400 Bad Request: Invalid request data
- 401 Unauthorized: Authentication required
- 403 Forbidden: Access denied
- 404 Not Found: Resource not found
- 422 Unprocessable Entity: Validation errors
- 429 Too Many Requests: Rate limit exceeded
- 500 Internal Server Error: Server error

### **Authentication Flow**

1. User registers → Email verification required
2. User logs in → JWT token returned with refresh token
3. Token expires → Use refresh token to get new access token
4. User logs out → All tokens invalidated

### **File Upload Process**

1. Client requests upload URL → Server returns signed URL
2. Client uploads file directly to storage
3. Client submits form with file URL
4. Server validates and processes submission

This comprehensive API specification covers all the screen flows in your customized design and aligns with your database schema. The APIs support both web and mobile applications with consistent response formats, proper error handling, and security considerations.