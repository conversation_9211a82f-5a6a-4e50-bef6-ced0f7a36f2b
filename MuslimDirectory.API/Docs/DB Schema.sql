use salaamprojects_muslim_directory_dev_db
GO

/*
EXEC sp_msforeachtable 'drop table [?]'
drop table UserSessions
drop table Categories
drop table ListingCategories
drop table ListingMedia
drop table OrganizationUsers
drop table Tags
drop table ListingTags
drop table Organizations
drop table PasswordResetTokens
drop table Reviews
drop table Listings
drop table UserAuthProviders
drop table Users
*/

-- Set database options
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Core User Management Tables
-- =============================================

-- Users Table
CREATE TABLE [dbo].[Users] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [Email]                             NVARCHAR(255) NOT NULL,
    [PhoneNumber]                       NVARCHAR(20) NULL,
    [PasswordHash]                      NVARCHAR(255) NULL,
    [FirstName]                         NVARCHAR(100) NULL,
    [LastName]                          NVARCHAR(100) NULL,
    [ProfilePicture]                    NVARCHAR(500) NULL,
    [Gender]                            VARCHAR(1) NULL CHECK ([Gender] IN ('M', 'F')),
    [Country]                           VARCHAR(2) NULL,
    [City]                              NVARCHAR(100) NULL,
    [PreferredLanguage]                 NVARCHAR(10) NOT NULL DEFAULT 'EN',
    [IsEmailVerified]                   BIT NOT NULL DEFAULT 0,
    [IsPhoneVerified]                   BIT NOT NULL DEFAULT 0,
    [IsActive]                          BIT NOT NULL DEFAULT 1,
    [UserType]                          NVARCHAR(20) NOT NULL DEFAULT 'Regular' CHECK ([UserType] IN ('Regular', 'Developer', 'Organization', 'Admin', 'Moderator')),
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [LastLoginAt]                       DATETIME NULL,
    CONSTRAINT [PK_Users]               PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [UQ_Users_Email]         UNIQUE ([Email])
);

-- UserAuthProviders Table
CREATE TABLE [dbo].[UserAuthProviders] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [UserID]                            UNIQUEIDENTIFIER NOT NULL,
    [Provider]                          NVARCHAR(50) NOT NULL,
    [ProviderID]                        NVARCHAR(255) NOT NULL,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_UserAuthProviders]   PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_UserAuthProviders_Users] 
                                        FOREIGN KEY ([UserID]) REFERENCES [dbo].[Users]([ID]) ON DELETE CASCADE,
    CONSTRAINT [UQ_UserAuthProviders_Provider_ProviderID] 
                                        UNIQUE ([Provider], [ProviderID])
);

-- UserSessions Table
CREATE TABLE [dbo].[UserSessions] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [UserID]                            UNIQUEIDENTIFIER NOT NULL,
    [Token]                             NVARCHAR(500) NOT NULL,
    [ExpiresAt]                         DATETIME NOT NULL,
    [IsActive]                          BIT NOT NULL DEFAULT 1,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_UserSessions]        PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_UserSessions_Users]  FOREIGN KEY ([UserID]) REFERENCES [dbo].[Users]([ID]) ON DELETE CASCADE,
    CONSTRAINT [UQ_UserSessions_Token]  UNIQUE ([Token])
);

-- PasswordResetTokens Table
CREATE TABLE [dbo].[PasswordResetTokens] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [UserID]                            UNIQUEIDENTIFIER NOT NULL,
    [Token]                             NVARCHAR(255) NOT NULL,
    [ExpiresAt]                         DATETIME NOT NULL,
    [IsUsed]                            BIT NOT NULL DEFAULT 0,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_PasswordResetTokens] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_PasswordResetTokens_Users] 
                                        FOREIGN KEY ([UserID]) REFERENCES [dbo].[Users]([ID]) ON DELETE CASCADE,
    CONSTRAINT [UQ_PasswordResetTokens_Token] 
                                        UNIQUE ([Token])
);

-- =============================================
-- Organization/Developer Tables
-- =============================================

-- Organizations Table
CREATE TABLE [dbo].[Organizations] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [Name]                              NVARCHAR(255) NOT NULL,
    [Description]                       NVARCHAR(MAX) NULL,
    [LogoURL]                           NVARCHAR(500) NULL,
    [Website]                           NVARCHAR(500) NULL,
    [Email]                             NVARCHAR(255) NULL,
    [PhoneNumber]                       NVARCHAR(20) NULL,
    [Address]                           NVARCHAR(500) NULL,
    [Country]                           VARCHAR(2) NULL,
    [IsVerified]                        BIT NOT NULL DEFAULT 0,
    [IslamicComplianceCertificate]      NVARCHAR(500) NULL,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_Organizations]       PRIMARY KEY CLUSTERED ([ID])
);

-- OrganizationUsers Table
CREATE TABLE [dbo].[OrganizationUsers] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [UserID]                            UNIQUEIDENTIFIER NOT NULL,
    [OrganizationID]                    UNIQUEIDENTIFIER NOT NULL,
    [Role]                              NVARCHAR(50) NOT NULL DEFAULT 'Member' CHECK ([Role] IN ('Owner', 'Admin', 'Member')),
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_OrganizationUsers]   PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_OrganizationUsers_Users] 
                                        FOREIGN KEY ([UserID]) REFERENCES [dbo].[Users]([ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_OrganizationUsers_Organizations] 
                                        FOREIGN KEY ([OrganizationID]) REFERENCES [dbo].[Organizations]([ID]) ON DELETE CASCADE,
    CONSTRAINT [UQ_OrganizationUsers_User_Organization] 
                                        UNIQUE ([UserID], [OrganizationID])
);

-- =============================================
-- Category and Taxonomy Tables
-- =============================================

-- Categories Table
CREATE TABLE [dbo].[Categories] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [Name]                              NVARCHAR(100) NOT NULL,
    [Description]                       NVARCHAR(400) NULL,
    [IconURL]                           NVARCHAR(255) NULL,
    [ParentCategoryID]                  UNIQUEIDENTIFIER NULL,
    [SortOrder]                         INT NOT NULL DEFAULT 0,
    [IsActive]                          BIT NOT NULL DEFAULT 1,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_Categories]          PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Categories_ParentCategory] 
                                        FOREIGN KEY ([ParentCategoryID]) REFERENCES [dbo].[Categories]([ID])
);

-- Tags Table
CREATE TABLE [dbo].[Tags] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [Name]                              NVARCHAR(100) NOT NULL,
    [Description]                       NVARCHAR(400) NULL,
    [IsActive]                          BIT NOT NULL DEFAULT 1,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_Tags]                PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [UQ_Tags_Name]           UNIQUE ([Name])
);

-- =============================================
-- Main Listings Table
-- =============================================

-- Listings Table
CREATE TABLE [dbo].[Listings] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [Title]                             NVARCHAR(255) NOT NULL,
    [ShortDescription]                  NVARCHAR(400) NULL,
    [FullDescription]                   NVARCHAR(4000) NULL,
    [LogoURL]                           NVARCHAR(500) NULL,
    [Website]                           NVARCHAR(500) NULL,
    [PlatformType]                      NVARCHAR(50) NULL CHECK ([PlatformType] IN ('Mobile App', 'Website', 'Desktop App', 'Browser Extension')),
    [SupportedPlatforms]                NVARCHAR(800) NULL, -- JSON array
    [AppStoreURL]                       NVARCHAR(500) NULL,
    [PlayStoreURL]                      NVARCHAR(500) NULL,
    [WebsiteURL]                        NVARCHAR(500) NULL,
    [PricingModel]                      NVARCHAR(50) NULL CHECK ([PricingModel] IN ('Free', 'Paid', 'Freemium', 'Subscription')),
    [Price]                             DECIMAL(10,2) NULL,
    [Currency]                          NVARCHAR(3) NULL DEFAULT 'USD',
    [IslamicComplianceStatus]           NVARCHAR(50) NOT NULL DEFAULT 'Under Review' CHECK ([IslamicComplianceStatus] IN ('Verified', 'Pending', 'Rejected', 'Under Review')),
    [ComplianceNotes]                   NVARCHAR(4000) NULL,
    [OrganizationID]                    UNIQUEIDENTIFIER NULL,
    [SubmittedBy]                       UNIQUEIDENTIFIER NOT NULL,
    [Status]                            NVARCHAR(50) NOT NULL DEFAULT 'Draft' CHECK ([Status] IN ('Draft', 'Pending', 'Approved', 'Rejected', 'Suspended')),
    [FeaturedLevel]                     INT NOT NULL DEFAULT 0 CHECK ([FeaturedLevel] >= 0),
    [SlugURL]                           NVARCHAR(255) NOT NULL,
    [MetaTitle]                         NVARCHAR(255) NULL,
    [MetaDescription]                   NVARCHAR(500) NULL,
    [PrimaryLanguage]                   VARCHAR(2) NULL DEFAULT 'EN',
    [SupportedLanguages]                NVARCHAR(255) NULL, -- JSON array
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [ApprovedAt]                        DATETIME NULL,
    [ViewCount]                         BIGINT NOT NULL DEFAULT 0,
    CONSTRAINT [PK_Listings]            PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Listings_Organizations] 
                                        FOREIGN KEY ([OrganizationID]) REFERENCES [dbo].[Organizations]([ID]),
    CONSTRAINT [FK_Listings_SubmittedBy] 
                                        FOREIGN KEY ([SubmittedBy]) REFERENCES [dbo].[Users]([ID]),
    CONSTRAINT [UQ_Listings_SlugURL]    UNIQUE ([SlugURL])
);

-- ListingCategories Table
CREATE TABLE [dbo].[ListingCategories] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [ListingID]                         UNIQUEIDENTIFIER NOT NULL,
    [CategoryID]                        UNIQUEIDENTIFIER NOT NULL,
    [IsPrimary]                         BIT NOT NULL DEFAULT 0,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_ListingCategories]   PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ListingCategories_Listings] 
                                        FOREIGN KEY ([ListingID]) REFERENCES [dbo].[Listings]([ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_ListingCategories_Categories] 
                                        FOREIGN KEY ([CategoryID]) REFERENCES [dbo].[Categories]([ID]) ON DELETE CASCADE,
    CONSTRAINT [UQ_ListingCategories_Listing_Category] 
                                        UNIQUE ([ListingID], [CategoryID])
);

-- ListingTags Table
CREATE TABLE [dbo].[ListingTags] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [ListingID]                         UNIQUEIDENTIFIER NOT NULL,
    [TagID]                             UNIQUEIDENTIFIER NOT NULL,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_ListingTags]         PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ListingTags_Listings] FOREIGN KEY ([ListingID]) REFERENCES [dbo].[Listings]([ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_ListingTags_Tags] FOREIGN KEY ([TagID]) REFERENCES [dbo].[Tags]([ID]) ON DELETE CASCADE,
    CONSTRAINT [UQ_ListingTags_Listing_Tag] UNIQUE ([ListingID], [TagID])
);

-- =============================================
-- Media and Assets
-- =============================================

-- ListingMedia Table
CREATE TABLE [dbo].[ListingMedia] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [ListingID]                         UNIQUEIDENTIFIER NOT NULL,
    [MediaType]                         NVARCHAR(50) NOT NULL CHECK ([MediaType] IN ('Screenshot', 'Video', 'Logo', 'Banner')),
    [MediaURL]                          NVARCHAR(500) NOT NULL,
    [ThumbnailURL]                      NVARCHAR(500) NULL,
    [SortOrder]                         INT NOT NULL DEFAULT 0,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_ListingMedia]        PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ListingMedia_Listings] 
                                        FOREIGN KEY ([ListingID]) REFERENCES [dbo].[Listings]([ID]) ON DELETE CASCADE
);

-- =============================================
-- Reviews and Ratings
-- =============================================

-- Reviews Table
CREATE TABLE [dbo].[Reviews] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [ListingID]                         UNIQUEIDENTIFIER NOT NULL,
    [UserID]                            UNIQUEIDENTIFIER NOT NULL,
    [Rating]                            INT NOT NULL CHECK ([Rating] BETWEEN 1 AND 5),
    [Title]                             NVARCHAR(255) NULL,
    [ReviewText]                        NVARCHAR(2000) NULL,
    [IslamicComplianceRating]           INT NULL CHECK ([IslamicComplianceRating] BETWEEN 1 AND 5),
    [Status]                            NVARCHAR(50) NOT NULL DEFAULT 'Pending' CHECK ([Status] IN ('Pending', 'Approved', 'Rejected', 'Flagged')),
    [ModeratedBy]                       UNIQUEIDENTIFIER NULL,
    [ModerationNotes]                   NVARCHAR(2000) NULL,
    [HelpfulCount]                      INT NOT NULL DEFAULT 0,
    [ReportCount]                       INT NOT NULL DEFAULT 0,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_Reviews]             PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Reviews_Listings]    FOREIGN KEY ([ListingID]) REFERENCES [dbo].[Listings]([ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_Reviews_Users]       FOREIGN KEY ([UserID]) REFERENCES [dbo].[Users]([ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_Reviews_ModeratedBy] FOREIGN KEY ([ModeratedBy]) REFERENCES [dbo].[Users]([ID]),
    CONSTRAINT [UQ_Reviews_User_Listing] 
                                        UNIQUE ([UserID], [ListingID])
);

-- ReviewHelpfulVotes Table
CREATE TABLE [dbo].[ReviewHelpfulVotes] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [ReviewID]                          UNIQUEIDENTIFIER NOT NULL,
    [UserID]                            UNIQUEIDENTIFIER NOT NULL,
    [IsHelpful]                         BIT NOT NULL,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_ReviewHelpfulVotes]  PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ReviewHelpfulVotes_Reviews] 
                                        FOREIGN KEY ([ReviewID]) REFERENCES [dbo].[Reviews]([ID]),
    CONSTRAINT [FK_ReviewHelpfulVotes_Users] 
                                        FOREIGN KEY ([UserID]) REFERENCES [dbo].[Users]([ID]) ON DELETE CASCADE,
    CONSTRAINT [UQ_ReviewHelpfulVotes_Review_User] 
                                        UNIQUE ([ReviewID], [UserID])
);

-- ReviewReports Table
CREATE TABLE [dbo].[ReviewReports] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [ReviewID]                          UNIQUEIDENTIFIER NOT NULL,
    [UserID]                            UNIQUEIDENTIFIER NOT NULL,
    [ReasonCode]                        NVARCHAR(50) NOT NULL CHECK ([ReasonCode] IN ('Spam', 'Inappropriate', 'Fake', 'Offensive', 'Other')),
    [ReasonText]                        NVARCHAR(1000) NULL,
    [Status]                            NVARCHAR(50) NOT NULL DEFAULT 'Pending' CHECK ([Status] IN ('Pending', 'Resolved', 'Dismissed')),
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_ReviewReports]       PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ReviewReports_Reviews] 
                                        FOREIGN KEY ([ReviewID]) REFERENCES [dbo].[Reviews]([ID]),
    CONSTRAINT [FK_ReviewReports_Users] FOREIGN KEY ([UserID]) REFERENCES [dbo].[Users]([ID]) 
);

-- ReviewResponses Table
CREATE TABLE [dbo].[ReviewResponses] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [ReviewID]                          UNIQUEIDENTIFIER NOT NULL,
    [RespondedBy]                       UNIQUEIDENTIFIER NOT NULL,
    [ResponseText]                      NVARCHAR(2000) NOT NULL,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_ReviewResponses]     PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ReviewResponses_Reviews] 
                                        FOREIGN KEY ([ReviewID]) REFERENCES [dbo].[Reviews]([ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_ReviewResponses_Users]   
                                        FOREIGN KEY ([RespondedBy])REFERENCES [dbo].[Users]([ID])
);

-- =============================================
-- User Preferences and Interactions
-- =============================================

-- UserFavorites Table
CREATE TABLE [dbo].[UserFavorites] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [UserID]                            UNIQUEIDENTIFIER NOT NULL,
    [ListingID]                         UNIQUEIDENTIFIER NOT NULL,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_UserFavorites]       PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_UserFavorites_Users] FOREIGN KEY ([UserID]) REFERENCES [dbo].[Users]([ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_UserFavorites_Listings]
                                        FOREIGN KEY ([ListingID]) REFERENCES [dbo].[Listings]([ID]) ON DELETE CASCADE,
    CONSTRAINT [UQ_UserFavorites_User_Listing] 
                                        UNIQUE ([UserID], [ListingID])
);

-- UserPreferences Table
CREATE TABLE [dbo].[UserPreferences] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [UserID]                            UNIQUEIDENTIFIER NOT NULL,
    [PreferenceKey]                     NVARCHAR(20) NOT NULL,
    [PreferenceValue]                   NVARCHAR(100) NULL,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_UserPreferences]     PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_UserPreferences_Users] 
                                        FOREIGN KEY ([UserID]) REFERENCES [dbo].[Users]([ID]) ON DELETE CASCADE,
    CONSTRAINT [UQ_UserPreferences_User_Key] 
                                        UNIQUE ([UserID], [PreferenceKey])
);

-- =============================================
-- Content Management
-- =============================================

-- ContentPages Table
CREATE TABLE [dbo].[ContentPages] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [Title]                             NVARCHAR(255) NOT NULL,
    [Slug]                              NVARCHAR(255) NOT NULL,
    [Content]                           NVARCHAR(MAX) NULL,
    [MetaTitle]                         NVARCHAR(255) NULL,
    [MetaDescription]                   NVARCHAR(500) NULL,
    [PageLanguage]                      VARCHAR(2) NOT NULL DEFAULT 'EN',
    [IsPublished]                       BIT NOT NULL DEFAULT 0,
    [CreatedBy]                         UNIQUEIDENTIFIER NOT NULL,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [PK_ContentPages]        PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ContentPages_CreatedBy] 
                                        FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([ID]),
    CONSTRAINT [UQ_ContentPages_Slug]   UNIQUE ([Slug])
);

-- FAQ Table
CREATE TABLE [dbo].[FAQs] (
    [ID]                                UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [Question]                          NVARCHAR(500) NOT NULL,
    [Answer]                            NVARCHAR(4000) NOT NULL,
    [CategoryName]                      NVARCHAR(100) NULL,
    [Language]                          VARCHAR(2) NOT NULL DEFAULT 'EN',
    [SortOrder]                         INT NOT NULL DEFAULT 0,
    [IsActive]                          BIT NOT NULL DEFAULT 1,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),    
    CONSTRAINT [PK_FAQs]                PRIMARY KEY CLUSTERED ([ID])
);

-- =============================================
-- Moderation and Admin
-- =============================================

-- ModerationQueue Table
CREATE TABLE [dbo].[ModerationQueue] (
    [QueueId]                           UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    [EntityType]                        NVARCHAR(50) NOT NULL CHECK ([EntityType] IN ('Listing', 'Review', 'User', 'Organization')),
    [EntityID]                          UNIQUEIDENTIFIER NOT NULL,
    [AssignedTo]                        UNIQUEIDENTIFIER NULL,
    [Status]                            NVARCHAR(50) NOT NULL DEFAULT 'Pending' CHECK ([Status] IN ('Pending', 'InProgress', 'Completed', 'Escalated')),
    [ActionTaken]                       NVARCHAR(50) NULL CHECK ([ActionTaken] IN ('Approve', 'Reject', 'Flag', 'Suspend', 'Delete')),
    [Notes]                             NVARCHAR(2000) NULL,
    [CreatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [CompletedAt]                       DATETIME NULL,    
    CONSTRAINT [PK_ModerationQueue]     PRIMARY KEY CLUSTERED ([QueueId]),
    CONSTRAINT [FK_ModerationQueue_AssignedTo] 
                                        FOREIGN KEY ([AssignedTo]) REFERENCES [dbo].[Users]([ID])
);

-- =============================================
-- System Configuration
-- =============================================

-- SystemSettings Table
CREATE TABLE [dbo].[SystemSettings] (
    [SettingKey]                        NVARCHAR(100) NOT NULL,
    [SettingValue]                      NVARCHAR(500) NULL,
    [Description]                       NVARCHAR(2000) NULL,
    [DataType]                          NVARCHAR(50) NOT NULL DEFAULT 'String' CHECK ([DataType] IN ('String', 'Number', 'Boolean', 'JSON')),
    [UpdatedBy]                         UNIQUEIDENTIFIER NULL,
    [UpdatedAt]                         DATETIME NOT NULL DEFAULT GETUTCDATE(),    
    CONSTRAINT [PK_SystemSettings]      PRIMARY KEY CLUSTERED ([SettingKey]),
    CONSTRAINT [FK_SystemSettings_UpdatedBy] 
                                        FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([ID])
);

-- =============================================
-- Performance Indexes
-- =============================================

-- Users indexes
CREATE NONCLUSTERED INDEX [IX_Users_Email] 
                                        ON [dbo].[Users] ([Email]);
CREATE NONCLUSTERED INDEX [IX_Users_PhoneNumber] 
                                        ON [dbo].[Users] ([PhoneNumber]);
CREATE NONCLUSTERED INDEX [IX_Users_UserType] 
                                        ON [dbo].[Users] ([UserType]);

-- UserAuthProviders indexes
CREATE NONCLUSTERED INDEX [IX_UserAuthProviders_UserID] 
                                        ON [dbo].[UserAuthProviders] ([UserID]);

-- Listings indexes
CREATE NONCLUSTERED INDEX [IX_Listings_Status] 
                                        ON [dbo].[Listings] ([Status]);
CREATE NONCLUSTERED INDEX [IX_Listings_SlugURL] 
                                        ON [dbo].[Listings] ([SlugURL]);
CREATE NONCLUSTERED INDEX [IX_Listings_PlatformType] 
                                        ON [dbo].[Listings] ([PlatformType]);
CREATE NONCLUSTERED INDEX [IX_Listings_Featured_Created] 
                                        ON [dbo].[Listings] ([FeaturedLevel] DESC, [CreatedAt] DESC);
CREATE NONCLUSTERED INDEX [IX_Listings_Status_Compliance_Featured] 
                                        ON [dbo].[Listings] ([Status], [IslamicComplianceStatus], [FeaturedLevel] DESC, [CreatedAt] DESC);
CREATE NONCLUSTERED INDEX [IX_Listings_OrganizationID_Status] 
                                        ON [dbo].[Listings] ([OrganizationID], [Status]);
CREATE NONCLUSTERED INDEX [IX_Listings_SubmittedBy] 
                                        ON [dbo].[Listings] ([SubmittedBy]);

-- Reviews indexes
CREATE NONCLUSTERED INDEX [IX_Reviews_ListingID_Status_Rating] 
                                        ON [dbo].[Reviews] ([ListingID], [Status], [Rating] DESC, [CreatedAt] DESC);
CREATE NONCLUSTERED INDEX [IX_Reviews_ListingID_Status_Created] 
                                        ON [dbo].[Reviews] ([ListingID], [Status], [CreatedAt] DESC);
CREATE NONCLUSTERED INDEX [IX_Reviews_UserID_Created] 
                                        ON [dbo].[Reviews] ([UserID], [CreatedAt] DESC);

-- ModerationQueue indexes
CREATE NONCLUSTERED INDEX [IX_ModerationQueue_Status_Created] 
                                        ON [dbo].[ModerationQueue] ([Status], [CreatedAt]);
CREATE NONCLUSTERED INDEX [IX_ModerationQueue_AssignedTo] 
                                        ON [dbo].[ModerationQueue] ([AssignedTo]);
CREATE NONCLUSTERED INDEX [IX_ModerationQueue_EntityType_EntityID] 
                                        ON [dbo].[ModerationQueue] ([EntityType], [EntityID]);

-- Categories indexes
CREATE NONCLUSTERED INDEX [IX_Categories_ParentCategoryID] 
                                        ON [dbo].[Categories] ([ParentCategoryID]);
CREATE NONCLUSTERED INDEX [IX_Categories_IsActive_SortOrder] 
                                        ON [dbo].[Categories] ([IsActive], [SortOrder]);

-- ListingCategories indexes
CREATE NONCLUSTERED INDEX [IX_ListingCategories_CategoryID] 
                                        ON [dbo].[ListingCategories] ([CategoryID]);
CREATE NONCLUSTERED INDEX [IX_ListingCategories_IsPrimary] 
                                        ON [dbo].[ListingCategories] ([IsPrimary]);

-- ListingTags indexes
CREATE NONCLUSTERED INDEX [IX_ListingTags_TagID] 
                                        ON [dbo].[ListingTags] ([TagID]);

-- UserFavorites indexes
CREATE NONCLUSTERED INDEX [IX_UserFavorites_ListingID] 
                                        ON [dbo].[UserFavorites] ([ListingID]);

-- UserSessions indexes
CREATE NONCLUSTERED INDEX [IX_UserSessions_UserID_IsActive] 
                                        ON [dbo].[UserSessions] ([UserID], [IsActive]);
CREATE NONCLUSTERED INDEX [IX_UserSessions_ExpiresAt] 
                                        ON [dbo].[UserSessions] ([ExpiresAt]);

-- PasswordResetTokens indexes
CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_UserID] 
                                        ON [dbo].[PasswordResetTokens] ([UserID]);
CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_ExpiresAt_IsUsed] 
                                        ON [dbo].[PasswordResetTokens] ([ExpiresAt], [IsUsed]);

GO

-- =============================================
-- Triggers for UpdatedAt columns
-- =============================================

-- Users UpdatedAt trigger
CREATE TRIGGER [dbo].[TR_Users_UpdatedAt]
ON [dbo].[Users]
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE [dbo].[Users]
    SET [UpdatedAt] = GETUTCDATE()
    WHERE [ID] IN (SELECT [ID] FROM inserted);
END;
GO

-- Organizations UpdatedAt trigger
CREATE TRIGGER [dbo].[TR_Organizations_UpdatedAt]
ON [dbo].[Organizations]
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE [dbo].[Organizations]
    SET [UpdatedAt] = GETUTCDATE()
    WHERE [ID] IN (SELECT [ID] FROM inserted);
END;
GO

-- Listings UpdatedAt trigger
CREATE TRIGGER [dbo].[TR_Listings_UpdatedAt]
ON [dbo].[Listings]
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE [dbo].[Listings]
    SET [UpdatedAt] = GETUTCDATE()
    WHERE [ID] IN (SELECT [ID] FROM inserted);
END;
GO

-- Reviews UpdatedAt trigger
CREATE TRIGGER [dbo].[TR_Reviews_UpdatedAt]
ON [dbo].[Reviews]
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE [dbo].[Reviews]
    SET [UpdatedAt] = GETUTCDATE()
    WHERE [ID] IN (SELECT [ID] FROM inserted);
END;
GO

-- ReviewResponses UpdatedAt trigger
CREATE TRIGGER [dbo].[TR_ReviewResponses_UpdatedAt]
ON [dbo].[ReviewResponses]
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE [dbo].[ReviewResponses]
    SET [UpdatedAt] = GETUTCDATE()
    WHERE [ID] IN (SELECT [ID] FROM inserted);
END;
GO

-- UserPreferences UpdatedAt trigger
CREATE TRIGGER [dbo].[TR_UserPreferences_UpdatedAt]
ON [dbo].[UserPreferences]
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE [dbo].[UserPreferences]
    SET [UpdatedAt] = GETUTCDATE()
    WHERE [ID] IN (SELECT [ID] FROM inserted);
END;
GO

-- ContentPages UpdatedAt trigger
CREATE TRIGGER [dbo].[TR_ContentPages_UpdatedAt]
ON [dbo].[ContentPages]
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE [dbo].[ContentPages]
    SET [UpdatedAt] = GETUTCDATE()
    WHERE [ID] IN (SELECT [ID] FROM inserted);
END;