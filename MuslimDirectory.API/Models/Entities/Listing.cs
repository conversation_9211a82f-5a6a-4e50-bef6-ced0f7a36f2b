

namespace MuslimDirectory.API.Models.Entities;

public class Listing
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("Title")]
    public string Title { get; set; } = string.Empty;

    [ColumnAttribute("ShortDescription")]
    public string? ShortDescription { get; set; }

    [ColumnAttribute("FullDescription")]
    public string? FullDescription { get; set; }

    [ColumnAttribute("LogoURL")]
    public string? LogoURL { get; set; }

    [ColumnAttribute("Website")]
    public string? Website { get; set; }

    [ColumnAttribute("PlatformType")]
    public string? PlatformType { get; set; }

    [ColumnAttribute("SupportedPlatforms")]
    public string? SupportedPlatforms { get; set; } // JSON array

    [ColumnAttribute("AppStoreURL")]
    public string? AppStoreURL { get; set; }

    [ColumnAttribute("PlayStoreURL")]
    public string? PlayStoreURL { get; set; }

    [ColumnAttribute("WebsiteURL")]
    public string? WebsiteURL { get; set; }

    [ColumnAttribute("PricingModel")]
    public string? PricingModel { get; set; }

    [ColumnAttribute("Price")]
    public decimal? Price { get; set; }

    [ColumnAttribute("Currency")]
    public string? Currency { get; set; }

    [ColumnAttribute("IslamicComplianceStatus")]
    public string IslamicComplianceStatus { get; set; } = "Under Review";

    [ColumnAttribute("ComplianceNotes")]
    public string? ComplianceNotes { get; set; }

    [ColumnAttribute("OrganizationID")]
    public Guid? OrganizationID { get; set; }

    [ColumnAttribute("SubmittedBy")]
    public Guid SubmittedBy { get; set; }

    [ColumnAttribute("Status")]
    public string Status { get; set; } = "Draft";

    [ColumnAttribute("FeaturedLevel")]
    public int FeaturedLevel { get; set; } = 0;

    [ColumnAttribute("SlugURL")]
    public string SlugURL { get; set; } = string.Empty;

    [ColumnAttribute("MetaTitle")]
    public string? MetaTitle { get; set; }

    [ColumnAttribute("MetaDescription")]
    public string? MetaDescription { get; set; }

    [ColumnAttribute("PrimaryLanguage")]
    public string? PrimaryLanguage { get; set; }

    [ColumnAttribute("SupportedLanguages")]
    public string? SupportedLanguages { get; set; } // JSON array

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }

    [ColumnAttribute("ApprovedAt")]
    public DateTime? ApprovedAt { get; set; }

    [ColumnAttribute("ViewCount")]
    public long ViewCount { get; set; } = 0;
}

public class ListingCategory
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("ListingID")]
    public Guid ListingID { get; set; }

    [ColumnAttribute("CategoryID")]
    public Guid CategoryID { get; set; }

    [ColumnAttribute("IsPrimary")]
    public bool IsPrimary { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }
}

public class ListingTag
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("ListingID")]
    public Guid ListingID { get; set; }

    [ColumnAttribute("TagID")]
    public Guid TagID { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }
}

public class ListingMedia
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("ListingID")]
    public Guid ListingID { get; set; }

    [ColumnAttribute("MediaType")]
    public string MediaType { get; set; } = string.Empty;

    [ColumnAttribute("MediaURL")]
    public string MediaURL { get; set; } = string.Empty;

    [ColumnAttribute("ThumbnailURL")]
    public string? ThumbnailURL { get; set; }

    [ColumnAttribute("SortOrder")]
    public int SortOrder { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }
}
