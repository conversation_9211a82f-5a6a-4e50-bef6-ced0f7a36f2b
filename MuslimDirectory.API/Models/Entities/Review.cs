namespace MuslimDirectory.API.Models.Entities;

public class Review
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("ListingID")]
    public Guid ListingID { get; set; }

    [ColumnAttribute("UserID")]
    public Guid UserID { get; set; }

    [ColumnAttribute("Rating")]
    public int Rating { get; set; }

    [ColumnAttribute("Title")]
    public string? Title { get; set; }

    [ColumnAttribute("ReviewText")]
    public string? ReviewText { get; set; }

    [ColumnAttribute("IslamicComplianceRating")]
    public int? IslamicComplianceRating { get; set; }

    [ColumnAttribute("Status")]
    public string Status { get; set; } = "Pending";

    [ColumnAttribute("ModeratedBy")]
    public Guid? ModeratedBy { get; set; }

    [ColumnAttribute("ModerationNotes")]
    public string? ModerationNotes { get; set; }

    [ColumnAttribute("HelpfulCount")]
    public int HelpfulCount { get; set; } = 0;

    [ColumnAttribute("ReportCount")]
    public int ReportCount { get; set; } = 0;

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }
}

public class ReviewReport
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("ReviewID")]
    public Guid ReviewID { get; set; }

    [ColumnAttribute("UserID")]
    public Guid UserID { get; set; }

    [ColumnAttribute("ReasonCode")]
    public string ReasonCode { get; set; } = string.Empty;

    [ColumnAttribute("ReasonText")]
    public string? ReasonText { get; set; }

    [ColumnAttribute("Status")]
    public string Status { get; set; } = "Pending";

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }
}

public class ReviewResponse
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("ReviewID")]
    public Guid ReviewID { get; set; }

    [ColumnAttribute("RespondedBy")]
    public Guid RespondedBy { get; set; }

    [ColumnAttribute("ResponseText")]
    public string ResponseText { get; set; } = string.Empty;

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }
}

public class ReviewHelpful
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("ReviewID")]
    public Guid ReviewID { get; set; }

    [ColumnAttribute("UserID")]
    public Guid UserID { get; set; }

    [ColumnAttribute("IsHelpful")]
    public bool IsHelpful { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }
}
