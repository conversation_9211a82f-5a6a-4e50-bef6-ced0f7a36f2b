namespace MuslimDirectory.API.Models.Entities;

public class UserPreference
{
    [Column("ID")]
    public Guid Id { get; set; }
    
    [Column("UserID")]
    public Guid UserId { get; set; }
    
    public string PreferenceKey { get; set; } = string.Empty;
    
    public string? PreferenceValue { get; set; }
    
    public DateTime CreatedAt { get; set; }
    
    public DateTime UpdatedAt { get; set; }
}
