using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Column = XGENO.DBHelpers.Core.Attributes.ColumnAttribute;

namespace MuslimDirectory.API.Models.Entities;

[Table("OrganizationUsers")]
public class OrganizationUser
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; }

    [Required]
    [Column("UserID")]
    public Guid UserId { get; set; }

    [Required]
    [Column("OrganizationID")]
    public Guid OrganizationId { get; set; }

    [Required]
    [Column("Role")]
    [StringLength(50)]
    public string Role { get; set; } = "Member";

    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    // Navigation properties
    [ForeignKey("UserId")]
    public virtual User? User { get; set; }

    [ForeignKey("OrganizationId")]
    public virtual Organization? Organization { get; set; }
}

public static class OrganizationRoles
{
    public const string Owner = "Owner";
    public const string Admin = "Admin";
    public const string Member = "Member";
}
