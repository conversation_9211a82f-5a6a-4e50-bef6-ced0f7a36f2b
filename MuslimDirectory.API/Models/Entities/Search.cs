namespace MuslimDirectory.API.Models.Entities;

public class SearchHistory
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("UserID")]
    public Guid? UserID { get; set; }

    [ColumnAttribute("SessionID")]
    public string? SessionID { get; set; }

    [ColumnAttribute("Query")]
    public string Query { get; set; } = string.Empty;

    [ColumnAttribute("SearchType")]
    public string? SearchType { get; set; }

    [ColumnAttribute("Category")]
    public string? Category { get; set; }

    [ColumnAttribute("Location")]
    public string? Location { get; set; }

    [ColumnAttribute("Latitude")]
    public decimal? Latitude { get; set; }

    [ColumnAttribute("Longitude")]
    public decimal? Longitude { get; set; }

    [ColumnAttribute("Radius")]
    public int? Radius { get; set; }

    [ColumnAttribute("ResultsCount")]
    public int ResultsCount { get; set; }

    [ColumnAttribute("SearchDuration")]
    public int SearchDuration { get; set; } // in milliseconds

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }
}

public class PopularSearch
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("Query")]
    public string Query { get; set; } = string.Empty;

    [ColumnAttribute("Category")]
    public string? Category { get; set; }

    [ColumnAttribute("SearchCount")]
    public int SearchCount { get; set; }

    [ColumnAttribute("LastSearched")]
    public DateTime LastSearched { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }
}

public class SearchSuggestion
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("Text")]
    public string Text { get; set; } = string.Empty;

    [ColumnAttribute("Type")]
    public string Type { get; set; } = string.Empty; // "query", "category", "location", "listing"

    [ColumnAttribute("EntityID")]
    public Guid? EntityID { get; set; }

    [ColumnAttribute("SearchCount")]
    public int SearchCount { get; set; }

    [ColumnAttribute("IsActive")]
    public bool IsActive { get; set; } = true;

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }
}
