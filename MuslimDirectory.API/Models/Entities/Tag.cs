using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Column = XGENO.DBHelpers.Core.Attributes.ColumnAttribute;

namespace MuslimDirectory.API.Models.Entities;

[Table("Tags")]
public class Tag
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; }

    [Required]
    [Column("Name")]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [Column("Description")]
    [StringLength(400)]
    public string? Description { get; set; }

    [Column("IsActive")]
    public bool IsActive { get; set; } = true;

    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; }
}
