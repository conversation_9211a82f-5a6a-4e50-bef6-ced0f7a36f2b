namespace MuslimDirectory.API.Models.Entities;

public class User
{
    [Column("ID")]
    public Guid Id { get; set; }
    
    public string Email { get; set; } = string.Empty;
    
    public string? PhoneNumber { get; set; }
    
    public string? PasswordHash { get; set; }
    
    public string? FirstName { get; set; }
    
    public string? LastName { get; set; }
    
    public string? ProfilePicture { get; set; }
    
    public string? Gender { get; set; }
    
    public string? Country { get; set; }
    
    public string? City { get; set; }
    
    public string PreferredLanguage { get; set; } = "EN";
    
    public bool IsEmailVerified { get; set; }
    
    public bool IsPhoneVerified { get; set; }
    
    public bool IsActive { get; set; } = true;

    [Column("IsTermsAccepted")]
    public bool IsTermsAccepted { get; set; } = false;

    public string UserType { get; set; } = "Regular";

    public DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }

    public DateTime? LastLoginAt { get; set; }
}
