using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Column = XGENO.DBHelpers.Core.Attributes.ColumnAttribute;

namespace MuslimDirectory.API.Models.Entities;

[Table("Categories")]
public class Category
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; }

    [Required]
    [Column("Name")]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [Column("Description")]
    [StringLength(400)]
    public string? Description { get; set; }

    [Column("IconURL")]
    [StringLength(255)]
    public string? IconURL { get; set; }

    [Column("ParentCategoryID")]
    public Guid? ParentCategoryId { get; set; }

    [Column("SortOrder")]
    public int SortOrder { get; set; } = 0;

    [Column("IsActive")]
    public bool IsActive { get; set; } = true;

    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    // Navigation properties
    [ForeignKey("ParentCategoryId")]
    public virtual Category? ParentCategory { get; set; }

    public virtual ICollection<Category> SubCategories { get; set; } = new List<Category>();
}
