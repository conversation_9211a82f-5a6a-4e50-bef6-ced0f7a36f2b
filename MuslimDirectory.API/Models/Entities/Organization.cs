using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Column = XGENO.DBHelpers.Core.Attributes.ColumnAttribute;

namespace MuslimDirectory.API.Models.Entities;

[Table("Organizations")]
public class Organization
{
    [Key]
    [Column("ID")]
    public Guid Id { get; set; }

    [Required]
    [Column("Name")]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Column("Description")]
    public string? Description { get; set; }

    [Column("LogoURL")]
    [StringLength(500)]
    public string? LogoURL { get; set; }

    [Column("Website")]
    [StringLength(500)]
    public string? Website { get; set; }

    [Column("Email")]
    [StringLength(255)]
    public string? Email { get; set; }

    [Column("PhoneNumber")]
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [Column("Address")]
    [StringLength(500)]
    public string? Address { get; set; }

    [Column("Country")]
    [StringLength(2)]
    public string? Country { get; set; }

    [Column("IsVerified")]
    public bool IsVerified { get; set; } = false;

    [Column("IslamicComplianceCertificate")]
    [StringLength(500)]
    public string? IslamicComplianceCertificate { get; set; }

    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [Column("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }
}
