using XGENO.DBHelpers.Core.Attributes;

namespace MuslimDirectory.API.Models.Entities;

public class ContentPage
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("Slug")]
    public string Slug { get; set; } = string.Empty;

    [ColumnAttribute("Title")]
    public string Title { get; set; } = string.Empty;

    [ColumnAttribute("Content")]
    public string Content { get; set; } = string.Empty;

    [ColumnAttribute("MetaTitle")]
    public string? MetaTitle { get; set; }

    [ColumnAttribute("MetaDescription")]
    public string? MetaDescription { get; set; }

    [ColumnAttribute("MetaKeywords")]
    public string? MetaKeywords { get; set; }

    [ColumnAttribute("IsPublished")]
    public bool IsPublished { get; set; }

    [ColumnAttribute("SortOrder")]
    public int SortOrder { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [ColumnAttribute("CreatedBy")]
    public Guid? CreatedBy { get; set; }

    [ColumnAttribute("UpdatedBy")]
    public Guid? UpdatedBy { get; set; }
}

public class Faq
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("Question")]
    public string Question { get; set; } = string.Empty;

    [ColumnAttribute("Answer")]
    public string Answer { get; set; } = string.Empty;

    [ColumnAttribute("Category")]
    public string Category { get; set; } = string.Empty;

    [ColumnAttribute("IsPublished")]
    public bool IsPublished { get; set; }

    [ColumnAttribute("SortOrder")]
    public int SortOrder { get; set; }

    [ColumnAttribute("ViewCount")]
    public int ViewCount { get; set; }

    [ColumnAttribute("HelpfulCount")]
    public int HelpfulCount { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [ColumnAttribute("CreatedBy")]
    public Guid? CreatedBy { get; set; }

    [ColumnAttribute("UpdatedBy")]
    public Guid? UpdatedBy { get; set; }
}

public class ContactSubmission
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("Name")]
    public string Name { get; set; } = string.Empty;

    [ColumnAttribute("Email")]
    public string Email { get; set; } = string.Empty;

    [ColumnAttribute("PhoneNumber")]
    public string? PhoneNumber { get; set; }

    [ColumnAttribute("Subject")]
    public string Subject { get; set; } = string.Empty;

    [ColumnAttribute("Message")]
    public string Message { get; set; } = string.Empty;

    [ColumnAttribute("Status")]
    public string Status { get; set; } = string.Empty; // "New", "InProgress", "Resolved", "Closed"

    [ColumnAttribute("Priority")]
    public string Priority { get; set; } = string.Empty; // "Low", "Medium", "High", "Urgent"

    [ColumnAttribute("Category")]
    public string? Category { get; set; }

    [ColumnAttribute("Response")]
    public string? Response { get; set; }

    [ColumnAttribute("InternalNotes")]
    public string? InternalNotes { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("RespondedAt")]
    public DateTime? RespondedAt { get; set; }

    [ColumnAttribute("RespondedBy")]
    public Guid? RespondedBy { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [ColumnAttribute("UpdatedBy")]
    public Guid? UpdatedBy { get; set; }
}

public class FaqHelpful
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("FaqID")]
    public Guid FaqId { get; set; }

    [ColumnAttribute("UserID")]
    public Guid? UserId { get; set; }

    [ColumnAttribute("IPAddress")]
    public string? IpAddress { get; set; }

    [ColumnAttribute("IsHelpful")]
    public bool IsHelpful { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }
}
