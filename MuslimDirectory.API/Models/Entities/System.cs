namespace MuslimDirectory.API.Models.Entities;

public class SystemSetting
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("Key")]
    public string Key { get; set; } = string.Empty;

    [ColumnAttribute("Value")]
    public string Value { get; set; } = string.Empty;

    [ColumnAttribute("DataType")]
    public string DataType { get; set; } = string.Empty; // "string", "int", "bool", "json"

    [ColumnAttribute("Category")]
    public string Category { get; set; } = string.Empty;

    [ColumnAttribute("Description")]
    public string? Description { get; set; }

    [ColumnAttribute("IsPublic")]
    public bool IsPublic { get; set; } = false;

    [ColumnAttribute("IsEditable")]
    public bool IsEditable { get; set; } = true;

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }
}

public class SystemHealth
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("Component")]
    public string Component { get; set; } = string.Empty;

    [ColumnAttribute("Status")]
    public string Status { get; set; } = string.Empty; // "Healthy", "Degraded", "Unhealthy"

    [ColumnAttribute("ResponseTime")]
    public int ResponseTime { get; set; } // in milliseconds

    [ColumnAttribute("Details")]
    public string? Details { get; set; } // JSON

    [ColumnAttribute("LastChecked")]
    public DateTime LastChecked { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }
}

public class Country
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("Code")]
    public string Code { get; set; } = string.Empty; // ISO 3166-1 alpha-2

    [ColumnAttribute("Name")]
    public string Name { get; set; } = string.Empty;

    [ColumnAttribute("NativeName")]
    public string NativeName { get; set; } = string.Empty;

    [ColumnAttribute("Region")]
    public string Region { get; set; } = string.Empty;

    [ColumnAttribute("SubRegion")]
    public string SubRegion { get; set; } = string.Empty;

    [ColumnAttribute("Languages")]
    public string Languages { get; set; } = string.Empty; // JSON array

    [ColumnAttribute("Currencies")]
    public string Currencies { get; set; } = string.Empty; // JSON array

    [ColumnAttribute("Flag")]
    public string Flag { get; set; } = string.Empty; // Unicode flag emoji

    [ColumnAttribute("IsSupported")]
    public bool IsSupported { get; set; } = true;

    [ColumnAttribute("DisplayOrder")]
    public int DisplayOrder { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }
}

public class StateProvince
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("CountryID")]
    public Guid CountryID { get; set; }

    [ColumnAttribute("Code")]
    public string Code { get; set; } = string.Empty;

    [ColumnAttribute("Name")]
    public string Name { get; set; } = string.Empty;

    [ColumnAttribute("Type")]
    public string Type { get; set; } = string.Empty; // "State", "Province", "Territory"

    [ColumnAttribute("DisplayOrder")]
    public int DisplayOrder { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }
}

public class City
{
    [ColumnAttribute("ID")]
    public Guid Id { get; set; }

    [ColumnAttribute("StateProvinceID")]
    public Guid? StateProvinceID { get; set; }

    [ColumnAttribute("CountryID")]
    public Guid CountryID { get; set; }

    [ColumnAttribute("Name")]
    public string Name { get; set; } = string.Empty;

    [ColumnAttribute("Latitude")]
    public decimal? Latitude { get; set; }

    [ColumnAttribute("Longitude")]
    public decimal? Longitude { get; set; }

    [ColumnAttribute("IsCapital")]
    public bool IsCapital { get; set; } = false;

    [ColumnAttribute("IsMajorCity")]
    public bool IsMajorCity { get; set; } = false;

    [ColumnAttribute("Population")]
    public int? Population { get; set; }

    [ColumnAttribute("TimeZone")]
    public string? TimeZone { get; set; }

    [ColumnAttribute("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [ColumnAttribute("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }
}
