using MuslimDirectory.API.Models.DTOs.User;
using MuslimDirectory.API.Models.DTOs.Organization;
using System.ComponentModel.DataAnnotations;

namespace MuslimDirectory.API.Models.DTOs.Review;

public class ReviewDto
{
    public Guid Id { get; set; }
    public Guid ListingID { get; set; }
    public string? ListingTitle { get; set; }
    public int Rating { get; set; }
    public string? Title { get; set; }
    public string? ReviewText { get; set; }
    public int? IslamicComplianceRating { get; set; }
    public string Status { get; set; } = string.Empty;
    public int HelpfulCount { get; set; }
    public bool? UserFoundHelpful { get; set; }
    public ReviewAuthorDto Author { get; set; } = new();
    public ReviewResponseDto? Response { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ReviewAuthorDto
{
    public Guid Id { get; set; }
    public string DisplayName { get; set; } = string.Empty;
    public string? ProfilePicture { get; set; }
    public bool IsVerified { get; set; }
}

public class ReviewResponseDto
{
    public Guid Id { get; set; }
    public string ResponseText { get; set; } = string.Empty;
    public ReviewResponderDto RespondedBy { get; set; } = new();
    public DateTime CreatedAt { get; set; }
}

public class ReviewResponderDto
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // "Organization" or "User"
    public string? LogoURL { get; set; }
}

public class CreateReviewDto
{
    [Required(ErrorMessage = "Listing ID is required")]
    public Guid ListingId { get; set; }

    [Required(ErrorMessage = "Rating is required")]
    [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5")]
    public int Rating { get; set; }

    [StringLength(255, ErrorMessage = "Title cannot exceed 255 characters")]
    public string? Title { get; set; }

    [StringLength(2000, ErrorMessage = "Review text cannot exceed 2000 characters")]
    public string? ReviewText { get; set; }

    [Range(1, 5, ErrorMessage = "Islamic compliance rating must be between 1 and 5")]
    public int? IslamicComplianceRating { get; set; }
}

public class UpdateReviewDto
{
    [Required(ErrorMessage = "Rating is required")]
    [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5")]
    public int Rating { get; set; }

    [StringLength(255, ErrorMessage = "Title cannot exceed 255 characters")]
    public string? Title { get; set; }

    [StringLength(2000, ErrorMessage = "Review text cannot exceed 2000 characters")]
    public string? ReviewText { get; set; }

    [Range(1, 5, ErrorMessage = "Islamic compliance rating must be between 1 and 5")]
    public int? IslamicComplianceRating { get; set; }
}

public class ReviewHelpfulDto
{
    [Required(ErrorMessage = "IsHelpful is required")]
    public bool IsHelpful { get; set; }
}

public class ReviewHelpfulResponseDto
{
    public int HelpfulCount { get; set; }
    public bool? UserVote { get; set; }
}

public class ReportReviewDto
{
    [Required(ErrorMessage = "Reason code is required")]
    [RegularExpression(@"^(Spam|Inappropriate|Fake|Offensive|Other)$", 
        ErrorMessage = "Reason code must be one of: Spam, Inappropriate, Fake, Offensive, Other")]
    public string ReasonCode { get; set; } = string.Empty;

    [StringLength(1000, ErrorMessage = "Reason text cannot exceed 1000 characters")]
    public string? ReasonText { get; set; }
}

public class ReviewResponseCreateDto
{
    [Required(ErrorMessage = "Response text is required")]
    [StringLength(2000, ErrorMessage = "Response text cannot exceed 2000 characters")]
    public string ResponseText { get; set; } = string.Empty;
}

public class ReviewSummaryDto
{
    public decimal AverageRating { get; set; }
    public decimal IslamicComplianceAverage { get; set; }
    public int TotalReviews { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
}

public class ReviewCreateResponseDto
{
    public Guid Id { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
}
