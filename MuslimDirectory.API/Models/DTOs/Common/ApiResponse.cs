namespace MuslimDirectory.API.Models.DTOs.Common;

public class ApiResponse<T>
{
    public bool Success { get; set; }
    public T? Data { get; set; }
    public string? Message { get; set; }
    public string? Provider { get; set; }
    public ApiError? Error { get; set; }
    public ApiMeta? Meta { get; set; }

    public static ApiResponse<T> SuccessResponse(T data, string? message = null, string? provider = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message,
            Provider = provider,
            Meta = new ApiMeta { Timestamp = DateTime.UtcNow }
        };
    }

    public static ApiResponse<T> ErrorResponse(string code, string message, object? details = null, string? field = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Error = new ApiError
            {
                Code = code,
                Message = message,
                Details = details,
                Field = field
            },
            Meta = new ApiMeta { Timestamp = DateTime.UtcNow }
        };
    }
}

public class ApiError
{
    public string Code { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public object? Details { get; set; }
    public string? Field { get; set; }
}

public class ApiMeta
{
    public DateTime Timestamp { get; set; }
    public ApiPagination? Pagination { get; set; }
}

public class ApiPagination
{
    public int Page { get; set; }
    public int Limit { get; set; }
    public int Total { get; set; }
    public int TotalPages { get; set; }
}
