using MuslimDirectory.API.Models.DTOs.User;

namespace MuslimDirectory.API.Models.DTOs.Auth;

public class UserRequest
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? ProfilePicture { get; set; }
    public string? Gender { get; set; }
    public string? Country { get; set; }
    public string? City { get; set; }
    public string? PhoneNumber { get; set; }
    public string PreferredLanguage { get; set; } = "EN";
    public bool IsEmailVerified { get; set; }
    public bool IsPhoneVerified { get; set; }
    public bool IsTermsAccepted { get; set; }
    public string UserType { get; set; } = "Regular";
    public DateTime MemberSince { get; set; }
    public UserStatsResponse Stats { get; set; } = new();
    public DateTime? LastLoginAt { get; set; }
}
