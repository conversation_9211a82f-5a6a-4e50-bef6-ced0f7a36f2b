namespace MuslimDirectory.API.Models.DTOs.Tag;

public class TagDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public int UsageCount { get; set; }
}

public class TagListDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public int UsageCount { get; set; }
}

public class CreateTagDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class UpdateTagDto
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
}

public class TagWithListingsDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public int UsageCount { get; set; }
    public List<TagListingDto>? Listings { get; set; }
}

public class TagListingDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? ShortDescription { get; set; }
    public string? LogoURL { get; set; }
    public string? PlatformType { get; set; }
    public string? PricingModel { get; set; }
    public decimal? Price { get; set; }
    public string? Currency { get; set; }
    public string IslamicComplianceStatus { get; set; } = string.Empty;
    public int FeaturedLevel { get; set; }
    public string SlugURL { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public long ViewCount { get; set; }
}
