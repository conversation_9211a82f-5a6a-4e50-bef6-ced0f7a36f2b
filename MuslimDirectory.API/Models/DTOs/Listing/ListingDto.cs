using MuslimDirectory.API.Models.DTOs.Organization;
using MuslimDirectory.API.Models.DTOs.Category;
using MuslimDirectory.API.Models.DTOs.Tag;

namespace MuslimDirectory.API.Models.DTOs.Listing;

public class ListingDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? ShortDescription { get; set; }
    public string? LogoURL { get; set; }
    public string? PlatformType { get; set; }
    public List<string> SupportedPlatforms { get; set; } = new();
    public string? PricingModel { get; set; }
    public decimal? Price { get; set; }
    public string? Currency { get; set; }
    public string IslamicComplianceStatus { get; set; } = string.Empty;
    public RatingDto Rating { get; set; } = new();
    public OrganizationListDto? Organization { get; set; }
    public CategoryListDto? PrimaryCategory { get; set; }
    public int FeaturedLevel { get; set; }
    public long ViewCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ListingDetailDto : ListingDto
{
    public string? FullDescription { get; set; }
    public string? Website { get; set; }
    public string? AppStoreURL { get; set; }
    public string? PlayStoreURL { get; set; }
    public string? WebsiteURL { get; set; }
    public string? ComplianceNotes { get; set; }
    public List<ListingCategoryDto> Categories { get; set; } = new();
    public List<TagListDto> Tags { get; set; } = new();
    public List<ListingMediaDto> Media { get; set; } = new();
    public string? PrimaryLanguage { get; set; }
    public List<string> SupportedLanguages { get; set; } = new();
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public bool IsFavorited { get; set; }
    public List<ListingDto> RelatedListings { get; set; } = new();
}

public class ListingCategoryDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public bool IsPrimary { get; set; }
}

public class ListingMediaDto
{
    public Guid Id { get; set; }
    public string MediaType { get; set; } = string.Empty;
    public string MediaURL { get; set; } = string.Empty;
    public string? ThumbnailURL { get; set; }
    public int SortOrder { get; set; }
}

public class RatingDto
{
    public decimal Average { get; set; }
    public int Count { get; set; }
    public decimal IslamicComplianceAverage { get; set; }
    public Dictionary<int, int> Distribution { get; set; } = new();
}

public class CreateListingDto
{
    public string Title { get; set; } = string.Empty;
    public string? ShortDescription { get; set; }
    public string? FullDescription { get; set; }
    public string? LogoURL { get; set; }
    public string? Website { get; set; }
    public string? PlatformType { get; set; }
    public List<string> SupportedPlatforms { get; set; } = new();
    public string? AppStoreURL { get; set; }
    public string? PlayStoreURL { get; set; }
    public string? WebsiteURL { get; set; }
    public string? PricingModel { get; set; }
    public decimal? Price { get; set; }
    public string? Currency { get; set; }
    public Guid OrganizationId { get; set; }
    public List<Guid> CategoryIds { get; set; } = new();
    public Guid PrimaryCategoryId { get; set; }
    public List<Guid> TagIds { get; set; } = new();
    public string? PrimaryLanguage { get; set; }
    public List<string> SupportedLanguages { get; set; } = new();
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public List<CreateListingMediaDto> Media { get; set; } = new();
}

public class CreateListingMediaDto
{
    public string MediaType { get; set; } = string.Empty;
    public string MediaURL { get; set; } = string.Empty;
    public string? ThumbnailURL { get; set; }
    public int SortOrder { get; set; }
}

public class UpdateListingDto
{
    public string? Title { get; set; }
    public string? ShortDescription { get; set; }
    public string? FullDescription { get; set; }
    public string? LogoURL { get; set; }
    public string? Website { get; set; }
    public string? PlatformType { get; set; }
    public List<string>? SupportedPlatforms { get; set; }
    public string? AppStoreURL { get; set; }
    public string? PlayStoreURL { get; set; }
    public string? WebsiteURL { get; set; }
    public string? PricingModel { get; set; }
    public decimal? Price { get; set; }
    public string? Currency { get; set; }
    public List<Guid>? CategoryIds { get; set; }
    public Guid? PrimaryCategoryId { get; set; }
    public List<Guid>? TagIds { get; set; }
    public string? PrimaryLanguage { get; set; }
    public List<string>? SupportedLanguages { get; set; }
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public List<CreateListingMediaDto>? Media { get; set; }
}

public class ListingAnalyticsDto
{
    public ListingAnalyticsSummaryDto Summary { get; set; } = new();
    public List<AnalyticsDataPointDto> ViewsOverTime { get; set; } = new();
    public List<AnalyticsDataPointDto> ReviewsOverTime { get; set; } = new();
    public List<AnalyticsDataPointDto> FavoritesOverTime { get; set; } = new();
}

public class ListingAnalyticsSummaryDto
{
    public long TotalViews { get; set; }
    public int TotalReviews { get; set; }
    public decimal AverageRating { get; set; }
    public int FavoriteCount { get; set; }
}

public class AnalyticsDataPointDto
{
    public DateTime Date { get; set; }
    public int Count { get; set; }
}

public class TrackViewDto
{
    public string? Source { get; set; }
    public string? Referrer { get; set; }
}

public class FeaturedListingDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? ShortDescription { get; set; }
    public string? LogoURL { get; set; }
    public decimal Rating { get; set; }
    public int ReviewCount { get; set; }
    public int FeaturedLevel { get; set; }
    public OrganizationListDto? Organization { get; set; }
}

public class RecommendationSectionDto
{
    public string Title { get; set; } = string.Empty;
    public List<ListingDto> Listings { get; set; } = new();
}

public class RecommendationsDto
{
    public List<RecommendationSectionDto> Sections { get; set; } = new();
}
