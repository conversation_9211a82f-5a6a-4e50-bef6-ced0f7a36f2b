using System.ComponentModel.DataAnnotations;

namespace MuslimDirectory.API.Models.DTOs.Content;

public class ContentPageDto
{
    public Guid Id { get; set; }
    public string Slug { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public string? MetaKeywords { get; set; }
    public bool IsPublished { get; set; }
    public int SortOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
}

public class FaqDto
{
    public Guid Id { get; set; }
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsPublished { get; set; }
    public int SortOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int ViewCount { get; set; }
    public bool IsHelpful { get; set; }
    public int HelpfulCount { get; set; }
}

public class FaqCategoryDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int FaqCount { get; set; }
    public int SortOrder { get; set; }
}

public class ContactSubmissionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // "New", "InProgress", "Resolved", "Closed"
    public string? Response { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? RespondedAt { get; set; }
    public string? RespondedBy { get; set; }
    public string? InternalNotes { get; set; }
    public string Priority { get; set; } = string.Empty; // "Low", "Medium", "High", "Urgent"
    public string? Category { get; set; }
}

public class CreateContentPageDto
{
    [Required]
    [StringLength(100)]
    public string Slug { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;

    [Required]
    public string Content { get; set; } = string.Empty;

    [StringLength(200)]
    public string? MetaTitle { get; set; }

    [StringLength(300)]
    public string? MetaDescription { get; set; }

    [StringLength(500)]
    public string? MetaKeywords { get; set; }

    public bool IsPublished { get; set; } = true;

    public int SortOrder { get; set; } = 0;
}

public class UpdateContentPageDto
{
    [StringLength(100)]
    public string? Slug { get; set; }

    [StringLength(200)]
    public string? Title { get; set; }

    public string? Content { get; set; }

    [StringLength(200)]
    public string? MetaTitle { get; set; }

    [StringLength(300)]
    public string? MetaDescription { get; set; }

    [StringLength(500)]
    public string? MetaKeywords { get; set; }

    public bool? IsPublished { get; set; }

    public int? SortOrder { get; set; }
}

public class CreateFaqDto
{
    [Required]
    [StringLength(500)]
    public string Question { get; set; } = string.Empty;

    [Required]
    public string Answer { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string Category { get; set; } = string.Empty;

    public bool IsPublished { get; set; } = true;

    public int SortOrder { get; set; } = 0;
}

public class UpdateFaqDto
{
    [StringLength(500)]
    public string? Question { get; set; }

    public string? Answer { get; set; }

    [StringLength(50)]
    public string? Category { get; set; }

    public bool? IsPublished { get; set; }

    public int? SortOrder { get; set; }
}

public class CreateContactSubmissionDto
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;

    [Phone]
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [Required]
    [StringLength(200)]
    public string Subject { get; set; } = string.Empty;

    [Required]
    [StringLength(2000)]
    public string Message { get; set; } = string.Empty;

    [StringLength(50)]
    public string? Category { get; set; }
}

public class UpdateContactSubmissionDto
{
    [Required]
    public string Status { get; set; } = string.Empty; // "New", "InProgress", "Resolved", "Closed"

    [StringLength(2000)]
    public string? Response { get; set; }

    [StringLength(1000)]
    public string? InternalNotes { get; set; }

    public string Priority { get; set; } = "Medium"; // "Low", "Medium", "High", "Urgent"

    [StringLength(50)]
    public string? Category { get; set; }
}

public class ContentStatsDto
{
    public int TotalPages { get; set; }
    public int PublishedPages { get; set; }
    public int DraftPages { get; set; }
    public int TotalFaqs { get; set; }
    public int PublishedFaqs { get; set; }
    public int FaqCategories { get; set; }
    public int TotalContactSubmissions { get; set; }
    public int NewContactSubmissions { get; set; }
    public int ResolvedContactSubmissions { get; set; }
    public int MostViewedFaqId { get; set; }
    public string? MostViewedFaqQuestion { get; set; }
    public DateTime LastContentUpdate { get; set; }
}

public class FaqSearchDto
{
    public string? Query { get; set; }
    public string? Category { get; set; }
    public bool? IsPublished { get; set; }
    public int Page { get; set; } = 1;
    public int Limit { get; set; } = 20;
}

public class ContentPageSearchDto
{
    public string? Query { get; set; }
    public bool? IsPublished { get; set; }
    public int Page { get; set; } = 1;
    public int Limit { get; set; } = 20;
}

public class ContactSubmissionSearchDto
{
    public string? Query { get; set; }
    public string? Status { get; set; }
    public string? Priority { get; set; }
    public string? Category { get; set; }
    public DateTime? CreatedFrom { get; set; }
    public DateTime? CreatedTo { get; set; }
    public int Page { get; set; } = 1;
    public int Limit { get; set; } = 20;
}
