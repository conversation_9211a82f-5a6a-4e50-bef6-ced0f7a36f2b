using System.ComponentModel.DataAnnotations;

namespace MuslimDirectory.API.Models.DTOs.Utilities;

public class FileUploadRequest
{
    [Required(ErrorMessage = "File is required")]
    public IFormFile File { get; set; } = null!;

    [StringLength(100, ErrorMessage = "Folder name cannot exceed 100 characters")]
    public string? Folder { get; set; }

    [StringLength(255, ErrorMessage = "Custom filename cannot exceed 255 characters")]
    public string? CustomFileName { get; set; }

    public bool MakePublic { get; set; } = true;
}

public class FileUploadResponse
{
    public string FileName { get; set; } = string.Empty;
    public string PublicUrl { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public string? Folder { get; set; }
}

public class FileDeleteRequest
{
    [Required(ErrorMessage = "File key is required")]
    public string Key { get; set; } = string.Empty;
}

public class FileDeleteResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
}
