namespace MuslimDirectory.API.Models.DTOs.User;

public class UserPreferencesResponse
{
    public string Language { get; set; } = "EN";
    public NotificationPreferences Notifications { get; set; } = new();
    public PrivacyPreferences Privacy { get; set; } = new();
    public AppPreferences AppPreferences { get; set; } = new();
}

public class NotificationPreferences
{
    public bool NewApps { get; set; } = true;
    public bool ReviewResponses { get; set; } = true;
    public bool WeeklyRecommendations { get; set; } = false;
}

public class PrivacyPreferences
{
    public string ProfileVisibility { get; set; } = "public"; // public, private
    public string ReviewDisplayName { get; set; } = "firstName"; // firstName, initials, anonymous
}

public class AppPreferences
{
    public string DefaultSortOrder { get; set; } = "rating"; // rating, newest, alphabetical
    public List<string> PreferredPlatforms { get; set; } = new() { "iOS", "Android" };
}
