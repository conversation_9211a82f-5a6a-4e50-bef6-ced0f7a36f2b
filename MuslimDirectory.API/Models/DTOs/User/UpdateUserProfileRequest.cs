namespace MuslimDirectory.API.Models.DTOs.User;

public class UpdateUserProfileRequest
{
    [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
    public string? FirstName { get; set; }

    [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
    public string? LastName { get; set; }

    [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
    [RegularExpression(@"^\+?[1-9]\d{1,14}$", ErrorMessage = "Invalid phone number format")]
    public string? PhoneNumber { get; set; }

    [StringLength(2, MinimumLength = 2, ErrorMessage = "Country must be a 2-character code")]
    public string? Country { get; set; }

    [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
    public string? City { get; set; }

    [StringLength(10, ErrorMessage = "Preferred language cannot exceed 10 characters")]
    public string? PreferredLanguage { get; set; }

    [RegularExpression(@"^[MF]$", ErrorMessage = "Gender must be 'M' or 'F'")]
    public string? Gender { get; set; }

    [StringLength(500, ErrorMessage = "Profile picture URL cannot exceed 500 characters")]
    [Url(ErrorMessage = "Invalid profile picture URL format")]
    public string? ProfilePicture { get; set; }
}
