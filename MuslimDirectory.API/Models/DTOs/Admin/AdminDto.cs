using System.ComponentModel.DataAnnotations;

namespace MuslimDirectory.API.Models.DTOs.Admin;

public class AdminDashboardDto
{
    public AdminStatsDto Stats { get; set; } = new();
    public List<AdminRecentActivityDto> RecentActivity { get; set; } = new();
    public List<AdminModerationItemDto> PendingModeration { get; set; } = new();
    public AdminSystemStatusDto SystemStatus { get; set; } = new();
    public List<AdminAlertDto> Alerts { get; set; } = new();
}

public class AdminStatsDto
{
    public long TotalUsers { get; set; }
    public long ActiveUsers { get; set; }
    public long NewUsersToday { get; set; }
    public long TotalListings { get; set; }
    public long ActiveListings { get; set; }
    public long PendingListings { get; set; }
    public long TotalReviews { get; set; }
    public long PendingReviews { get; set; }
    public long TotalReports { get; set; }
    public long UnresolvedReports { get; set; }
    public long TotalOrganizations { get; set; }
    public long VerifiedOrganizations { get; set; }
    public double AverageRating { get; set; }
    public long TotalSearches { get; set; }
    public long SearchesToday { get; set; }
}

public class AdminRecentActivityDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty; // "User", "Listing", "Review", "Report"
    public string Action { get; set; } = string.Empty; // "Created", "Updated", "Deleted", "Reported"
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class AdminModerationItemDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty; // "Listing", "Review", "Report", "User"
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty; // "Low", "Medium", "High", "Critical"
    public string SubmittedBy { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
    public string? Reason { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}

public class AdminSystemStatusDto
{
    public string Status { get; set; } = string.Empty; // "Healthy", "Warning", "Critical"
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public int ActiveConnections { get; set; }
    public double ResponseTime { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class AdminAlertDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty; // "Info", "Warning", "Error", "Critical"
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public bool IsRead { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? ActionUrl { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

public class AdminUserDto
{
    public Guid Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string Status { get; set; } = string.Empty; // "Active", "Inactive", "Suspended", "Banned"
    public bool IsEmailVerified { get; set; }
    public bool IsPhoneVerified { get; set; }
    public string Role { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public int ListingCount { get; set; }
    public int ReviewCount { get; set; }
    public int ReportCount { get; set; }
    public string? SuspensionReason { get; set; }
    public DateTime? SuspensionExpiresAt { get; set; }
}

public class AdminListingDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Category { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string OwnerName { get; set; } = string.Empty;
    public string OwnerEmail { get; set; } = string.Empty;
    public string? OrganizationName { get; set; }
    public bool IsVerified { get; set; }
    public bool IsFeatured { get; set; }
    public double? Rating { get; set; }
    public int ReviewCount { get; set; }
    public int ReportCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public string? ModerationNotes { get; set; }
}

public class AdminReviewDto
{
    public Guid Id { get; set; }
    public string ListingTitle { get; set; } = string.Empty;
    public string ReviewerName { get; set; } = string.Empty;
    public string ReviewerEmail { get; set; } = string.Empty;
    public int Rating { get; set; }
    public string? Comment { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public int HelpfulCount { get; set; }
    public int ReportCount { get; set; }
    public string? ModerationNotes { get; set; }
    public bool HasResponse { get; set; }
}

public class AdminReportDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty; // "Listing", "Review", "User"
    public string TargetTitle { get; set; } = string.Empty;
    public string ReporterName { get; set; } = string.Empty;
    public string ReporterEmail { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Status { get; set; } = string.Empty; // "Pending", "Investigating", "Resolved", "Dismissed"
    public string Priority { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? ResolvedBy { get; set; }
    public string? Resolution { get; set; }
}

public class UpdateUserStatusDto
{
    [Required]
    public string Status { get; set; } = string.Empty; // "Active", "Inactive", "Suspended", "Banned"

    public string? Reason { get; set; }

    public DateTime? SuspensionExpiresAt { get; set; }

    [StringLength(500)]
    public string? Notes { get; set; }
}

public class UpdateListingStatusDto
{
    [Required]
    public string Status { get; set; } = string.Empty; // "Active", "Inactive", "Pending", "Rejected"

    public string? Reason { get; set; }

    [StringLength(500)]
    public string? ModerationNotes { get; set; }

    public bool? IsVerified { get; set; }

    public bool? IsFeatured { get; set; }
}

public class UpdateReviewStatusDto
{
    [Required]
    public string Status { get; set; } = string.Empty; // "Approved", "Rejected", "Pending"

    public string? Reason { get; set; }

    [StringLength(500)]
    public string? ModerationNotes { get; set; }
}

public class UpdateReportStatusDto
{
    [Required]
    public string Status { get; set; } = string.Empty; // "Pending", "Investigating", "Resolved", "Dismissed"

    public string? Resolution { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    public string? ActionTaken { get; set; }
}
