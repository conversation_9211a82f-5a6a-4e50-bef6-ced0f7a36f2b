using System.ComponentModel.DataAnnotations;

namespace MuslimDirectory.API.Models.DTOs.Search;

public class SearchDto
{
    [Required(ErrorMessage = "Query is required")]
    [StringLength(500, ErrorMessage = "Query cannot exceed 500 characters")]
    public string Query { get; set; } = string.Empty;

    [StringLength(100, ErrorMessage = "Type cannot exceed 100 characters")]
    public string? Type { get; set; }

    [StringLength(100, ErrorMessage = "Category cannot exceed 100 characters")]
    public string? Category { get; set; }

    [StringLength(100, ErrorMessage = "Location cannot exceed 100 characters")]
    public string? Location { get; set; }

    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public int? Radius { get; set; } // in kilometers

    public int Page { get; set; } = 1;
    public int Limit { get; set; } = 20;
    public string? SortBy { get; set; }
}

public class SearchResultDto
{
    public string Type { get; set; } = string.Empty; // "listing", "organization", "category"
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? ImageUrl { get; set; }
    public string? Category { get; set; }
    public string? Location { get; set; }
    public decimal? Rating { get; set; }
    public int? ReviewCount { get; set; }
    public bool IsVerified { get; set; }
    public bool IsFeatured { get; set; }
    public decimal? Distance { get; set; } // in kilometers
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class SearchSuggestionDto
{
    public string Text { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // "query", "category", "location", "listing"
    public int Count { get; set; }
    public Guid? Id { get; set; }
}

public class RecentSearchDto
{
    public Guid Id { get; set; }
    public string Query { get; set; } = string.Empty;
    public string? Type { get; set; }
    public string? Category { get; set; }
    public string? Location { get; set; }
    public DateTime SearchedAt { get; set; }
}

public class SearchFiltersDto
{
    public List<string> Categories { get; set; } = new();
    public List<string> Types { get; set; } = new();
    public List<string> Locations { get; set; } = new();
    public decimal? MinRating { get; set; }
    public decimal? MaxDistance { get; set; }
    public bool? IsVerified { get; set; }
    public bool? IsFeatured { get; set; }
    public bool? IsOpen { get; set; }
}

public class SearchStatsDto
{
    public int TotalResults { get; set; }
    public int ListingResults { get; set; }
    public int OrganizationResults { get; set; }
    public int CategoryResults { get; set; }
    public Dictionary<string, int> CategoryBreakdown { get; set; } = new();
    public Dictionary<string, int> LocationBreakdown { get; set; } = new();
    public TimeSpan SearchDuration { get; set; }
}

public class PopularSearchDto
{
    public string Query { get; set; } = string.Empty;
    public int SearchCount { get; set; }
    public string Category { get; set; } = string.Empty;
    public DateTime LastSearched { get; set; }
}
