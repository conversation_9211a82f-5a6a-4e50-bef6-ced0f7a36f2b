namespace MuslimDirectory.API.Models.DTOs.System;

public class SystemHealthDto
{
    public string Status { get; set; } = string.Empty; // "Healthy", "Degraded", "Unhealthy"
    public DateTime Timestamp { get; set; }
    public TimeSpan Uptime { get; set; }
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, ComponentHealthDto> Components { get; set; } = new();
    public SystemMetricsDto Metrics { get; set; } = new();
}

public class ComponentHealthDto
{
    public string Status { get; set; } = string.Empty; // "Healthy", "Degraded", "Unhealthy"
    public string? Description { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public DateTime LastChecked { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}

public class SystemMetricsDto
{
    public long TotalUsers { get; set; }
    public long ActiveUsers { get; set; }
    public long TotalListings { get; set; }
    public long ActiveListings { get; set; }
    public long TotalReviews { get; set; }
    public long TotalOrganizations { get; set; }
    public long TotalSearches { get; set; }
    public double AverageResponseTime { get; set; }
    public long RequestsPerMinute { get; set; }
    public double DatabaseConnectionPoolUsage { get; set; }
    public long MemoryUsageMB { get; set; }
    public double CpuUsagePercent { get; set; }
}

public class SystemSettingsDto
{
    public string ApplicationName { get; set; } = string.Empty;
    public string Environment { get; set; } = string.Empty;
    public bool MaintenanceMode { get; set; }
    public string? MaintenanceMessage { get; set; }
    public bool RegistrationEnabled { get; set; }
    public bool EmailVerificationRequired { get; set; }
    public bool ReviewModerationEnabled { get; set; }
    public int MaxFileUploadSizeMB { get; set; }
    public List<string> AllowedImageFormats { get; set; } = new();
    public int SessionTimeoutMinutes { get; set; }
    public int PasswordMinLength { get; set; }
    public bool TwoFactorAuthEnabled { get; set; }
    public RateLimitSettingsDto RateLimits { get; set; } = new();
    public SearchSettingsDto SearchSettings { get; set; } = new();
    public NotificationSettingsDto NotificationSettings { get; set; } = new();
}

public class RateLimitSettingsDto
{
    public int RequestsPerMinute { get; set; }
    public int SearchRequestsPerMinute { get; set; }
    public int AuthRequestsPerMinute { get; set; }
    public int UploadRequestsPerMinute { get; set; }
}

public class SearchSettingsDto
{
    public int MaxResultsPerPage { get; set; }
    public int DefaultResultsPerPage { get; set; }
    public int MaxSearchRadius { get; set; }
    public int DefaultSearchRadius { get; set; }
    public bool GeolocationEnabled { get; set; }
    public int SearchHistoryRetentionDays { get; set; }
}

public class NotificationSettingsDto
{
    public bool EmailNotificationsEnabled { get; set; }
    public bool PushNotificationsEnabled { get; set; }
    public bool ReviewNotificationsEnabled { get; set; }
    public bool ListingNotificationsEnabled { get; set; }
    public bool SystemNotificationsEnabled { get; set; }
}

public class SystemVersionDto
{
    public string Version { get; set; } = string.Empty;
    public string BuildNumber { get; set; } = string.Empty;
    public DateTime BuildDate { get; set; }
    public string Environment { get; set; } = string.Empty;
    public string GitCommit { get; set; } = string.Empty;
    public string GitBranch { get; set; } = string.Empty;
    public List<string> Features { get; set; } = new();
    public Dictionary<string, string> Dependencies { get; set; } = new();
    public SystemCompatibilityDto Compatibility { get; set; } = new();
}

public class SystemCompatibilityDto
{
    public string MinimumClientVersion { get; set; } = string.Empty;
    public string RecommendedClientVersion { get; set; } = string.Empty;
    public List<string> SupportedApiVersions { get; set; } = new();
    public bool BackwardCompatible { get; set; }
}

public class CountryDto
{
    public string Code { get; set; } = string.Empty; // ISO 3166-1 alpha-2 code
    public string Name { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public string SubRegion { get; set; } = string.Empty;
    public List<string> Languages { get; set; } = new();
    public List<string> Currencies { get; set; } = new();
    public string Flag { get; set; } = string.Empty; // Unicode flag emoji
    public bool IsSupported { get; set; }
    public int ListingCount { get; set; }
    public List<StateProvinceDto> StatesProvinces { get; set; } = new();
}

public class StateProvinceDto
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // "State", "Province", "Territory", etc.
    public int ListingCount { get; set; }
    public List<CityDto> MajorCities { get; set; } = new();
}

public class CityDto
{
    public string Name { get; set; } = string.Empty;
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public int ListingCount { get; set; }
    public bool IsCapital { get; set; }
    public bool IsMajorCity { get; set; }
}
