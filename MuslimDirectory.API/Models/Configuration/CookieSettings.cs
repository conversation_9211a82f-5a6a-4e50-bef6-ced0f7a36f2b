namespace MuslimDirectory.API.Models.Configuration;

public class CookieSettings
{
    public string AccessTokenCookieName { get; set; } = "muslim_directory_access_token";
    public string RefreshTokenCookieName { get; set; } = "muslim_directory_refresh_token";
    public int AccessTokenExpirationInMinutes { get; set; } = 60;
    public int RefreshTokenExpirationInDays { get; set; } = 7;
    public bool SecureOnly { get; set; } = true;
    public bool HttpOnly { get; set; } = true;
    public string SameSite { get; set; } = "Strict"; // Strict, Lax, None
    public string Domain { get; set; } = string.Empty;
    public string Path { get; set; } = "/";
}
