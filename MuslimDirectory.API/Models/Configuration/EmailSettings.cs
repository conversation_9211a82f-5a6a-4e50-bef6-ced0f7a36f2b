namespace MuslimDirectory.API.Models.Configuration;

public class EmailSettings
{
    // API-based email service settings (Resend)
    public string ApiKey { get; set; } = string.Empty;
    public string ApiBaseUrl { get; set; } = "https://api.resend.com";
    public string SenderEmail { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;

    // Legacy SMTP settings (kept for backward compatibility)
    public string SmtpServer { get; set; } = string.Empty;
    public int SmtpPort { get; set; } = 587;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;

    // Email service type: "API" or "SMTP"
    public string ServiceType { get; set; } = "API";
}
