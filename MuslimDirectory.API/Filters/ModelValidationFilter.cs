namespace MuslimDirectory.API.Filters;

/// <summary>
/// Global filter to handle model validation errors and return consistent API response format
/// </summary>
public class ModelValidationFilter : IActionFilter
{
    public void OnActionExecuting(ActionExecutingContext context)
    {
        if (!context.ModelState.IsValid)
        {
            // Get the first validation error to use as the primary error
            var firstError = context.ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .FirstOrDefault();

            var primaryField = firstError.Key;
            var primaryMessage = firstError.Value?.Errors.FirstOrDefault()?.ErrorMessage ?? "Validation failed";

            // Collect all validation errors for details
            var allErrors = context.ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray() ?? Array.Empty<string>()
                );

            // Create consistent API response
            var response = new ApiResponse<object>
            {
                Success = false,
                Data = null,
                Message = null,
                Provider = null,
                Error = new ApiError
                {
                    Code = "VALIDATION_ERROR",
                    Message = primaryMessage,
                    Details = allErrors,
                    Field = primaryField
                },
                Meta = new ApiMeta
                {
                    Timestamp = DateTime.UtcNow,
                    Pagination = null
                }
            };

            context.Result = new BadRequestObjectResult(response);
        }
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // No action needed after execution
    }
}
