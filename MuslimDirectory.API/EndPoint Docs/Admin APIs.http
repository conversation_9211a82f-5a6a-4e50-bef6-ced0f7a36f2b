### Admin APIs Documentation
### Base URL: {{baseUrl}}/api/admin

### Variables
@baseUrl = https://localhost:7001
@adminToken = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### ========================================
### ADMIN ENDPOINTS (Admin/SuperAdmin Role Required)
### ========================================

### 1. Get Admin Dashboard
GET {{baseUrl}}/api/admin/dashboard
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "stats": {
#       "totalUsers": 1250,
#       "activeUsers": 1180,
#       "newUsersToday": 15,
#       "totalListings": 2890,
#       "activeListings": 2654,
#       "pendingListings": 45,
#       "totalReviews": 5432,
#       "pendingReviews": 23,
#       "totalReports": 89,
#       "unresolvedReports": 12,
#       "totalOrganizations": 156,
#       "verifiedOrganizations": 134,
#       "averageRating": 4.2,
#       "totalSearches": 12890,
#       "searchesToday": 234
#     },
#     "recentActivity": [...],
#     "pendingModeration": [...],
#     "systemStatus": {
#       "status": "Healthy",
#       "cpuUsage": 15.5,
#       "memoryUsage": 68.2,
#       "diskUsage": 45.8,
#       "activeConnections": 25,
#       "responseTime": 125.0,
#       "lastUpdated": "2024-01-15T14:30:00Z"
#     },
#     "alerts": [...]
#   }
# }

### 2. Get Moderation Queue
GET {{baseUrl}}/api/admin/moderation-queue
Accept: application/json
Authorization: {{adminToken}}

### Get Moderation Queue with Filters
GET {{baseUrl}}/api/admin/moderation-queue?type=Listing&status=Pending&priority=High&page=1&limit=20
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-************",
#         "type": "Listing",
#         "title": "New Mosque in Downtown",
#         "description": "A beautiful new mosque...",
#         "status": "Pending",
#         "priority": "Medium",
#         "submittedBy": "John Doe",
#         "submittedAt": "2024-01-15T10:30:00Z",
#         "reason": null,
#         "details": {}
#       }
#     ],
#     "totalCount": 45,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 3
#   }
# }

### 3. Get Users
GET {{baseUrl}}/api/admin/users
Accept: application/json
Authorization: {{adminToken}}

### Get Users with Search and Filters
GET {{baseUrl}}/api/admin/users?search=john&status=Active&role=User&page=1&limit=20
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-************",
#         "firstName": "John",
#         "lastName": "Doe",
#         "email": "<EMAIL>",
#         "phoneNumber": "+1234567890",
#         "status": "Active",
#         "isEmailVerified": true,
#         "isPhoneVerified": false,
#         "role": "User",
#         "createdAt": "2024-01-10T08:30:00Z",
#         "lastLoginAt": "2024-01-15T12:00:00Z",
#         "listingCount": 3,
#         "reviewCount": 12,
#         "reportCount": 0,
#         "suspensionReason": null,
#         "suspensionExpiresAt": null
#       }
#     ],
#     "totalCount": 1250,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 63
#   }
# }

### 4. Update User Status
PUT {{baseUrl}}/api/admin/users/550e8400-e29b-41d4-a716-************/status
Accept: application/json
Authorization: {{adminToken}}
Content-Type: application/json

{
  "status": "Suspended",
  "reason": "Violation of community guidelines",
  "suspensionExpiresAt": "2024-02-15T00:00:00Z",
  "notes": "User posted inappropriate content"
}

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "User status updated successfully"
# }

### 5. Get Listings
GET {{baseUrl}}/api/admin/listings
Accept: application/json
Authorization: {{adminToken}}

### Get Listings with Filters
GET {{baseUrl}}/api/admin/listings?search=mosque&status=Active&category=Religious&isVerified=true&page=1&limit=20
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-************",
#         "title": "Downtown Islamic Center",
#         "description": "A community mosque...",
#         "category": "Religious",
#         "status": "Active",
#         "ownerName": "Ahmed Ali",
#         "ownerEmail": "<EMAIL>",
#         "organizationName": "Islamic Society",
#         "isVerified": true,
#         "isFeatured": false,
#         "rating": 4.5,
#         "reviewCount": 23,
#         "reportCount": 0,
#         "createdAt": "2024-01-10T08:30:00Z",
#         "lastModifiedAt": "2024-01-12T10:15:00Z",
#         "moderationNotes": null
#       }
#     ],
#     "totalCount": 2890,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 145
#   }
# }

### 6. Update Listing Status
PUT {{baseUrl}}/api/admin/listings/550e8400-e29b-41d4-a716-************/status
Accept: application/json
Authorization: {{adminToken}}
Content-Type: application/json

{
  "status": "Active",
  "reason": "Approved after review",
  "moderationNotes": "All information verified",
  "isVerified": true,
  "isFeatured": false
}

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "Listing status updated successfully"
# }

### 7. Get Reviews
GET {{baseUrl}}/api/admin/reviews
Accept: application/json
Authorization: {{adminToken}}

### Get Reviews with Filters
GET {{baseUrl}}/api/admin/reviews?search=excellent&status=Pending&page=1&limit=20
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-************",
#         "listingTitle": "Downtown Islamic Center",
#         "reviewerName": "Sarah Ahmed",
#         "reviewerEmail": "<EMAIL>",
#         "rating": 5,
#         "comment": "Excellent facilities and welcoming community",
#         "status": "Pending",
#         "createdAt": "2024-01-15T09:30:00Z",
#         "helpfulCount": 3,
#         "reportCount": 0,
#         "moderationNotes": null,
#         "hasResponse": false
#       }
#     ],
#     "totalCount": 5432,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 272
#   }
# }

### 8. Update Review Status
PUT {{baseUrl}}/api/admin/reviews/550e8400-e29b-41d4-a716-************/status
Accept: application/json
Authorization: {{adminToken}}
Content-Type: application/json

{
  "status": "Approved",
  "reason": "Review meets community guidelines",
  "moderationNotes": "Constructive feedback provided"
}

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "Review status updated successfully"
# }

### 9. Get Reports
GET {{baseUrl}}/api/admin/reports
Accept: application/json
Authorization: {{adminToken}}

### Get Reports with Filters
GET {{baseUrl}}/api/admin/reports?type=Listing&status=Pending&priority=High&page=1&limit=20
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-************",
#         "type": "Listing",
#         "targetTitle": "Downtown Islamic Center",
#         "reporterName": "Anonymous User",
#         "reporterEmail": "<EMAIL>",
#         "reason": "Inappropriate Content",
#         "description": "The listing contains misleading information",
#         "status": "Pending",
#         "priority": "Medium",
#         "createdAt": "2024-01-15T11:30:00Z",
#         "resolvedAt": null,
#         "resolvedBy": null,
#         "resolution": null
#       }
#     ],
#     "totalCount": 89,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 5
#   }
# }

### 10. Update Report Status
PUT {{baseUrl}}/api/admin/reports/550e8400-e29b-41d4-a716-************/status
Accept: application/json
Authorization: {{adminToken}}
Content-Type: application/json

{
  "status": "Resolved",
  "resolution": "Contacted listing owner to update information",
  "notes": "Issue resolved through direct communication",
  "actionTaken": "Information updated"
}

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "Report status updated successfully"
# }

### ========================================
### ERROR RESPONSES
### ========================================

### Unauthorized Access:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "UNAUTHORIZED",
#     "message": "Admin role required"
#   }
# }

### Invalid ID:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "INVALID_USER_ID",
#     "message": "User ID is required"
#   }
# }

### Resource Not Found:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "USER_NOT_FOUND",
#     "message": "User not found"
#   }
# }
