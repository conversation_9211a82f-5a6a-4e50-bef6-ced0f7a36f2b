### Review Management APIs Documentation
### Base URL: {{baseUrl}}/api/review

### Variables
@baseUrl = https://localhost:7001
@listingId = 550e8400-e29b-41d4-a716-446655440000
@reviewId = 550e8400-e29b-41d4-a716-446655440001
@bearerToken = your_jwt_token_here

### ========================================
### PUBLIC ENDPOINTS (No Authentication Required)
### ========================================

### 1. Get Reviews for Listing
GET {{baseUrl}}/api/review?listingId={{listingId}}&page=1&limit=20&sortBy=newest&rating=5&hasResponse=true
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-446655440001",
#         "listingID": "550e8400-e29b-41d4-a716-446655440000",
#         "listingTitle": "Al-Noor Islamic Center",
#         "rating": 5,
#         "title": "Excellent Islamic Center",
#         "reviewText": "Great place for prayers and community events. Very welcoming atmosphere.",
#         "islamicComplianceRating": 5,
#         "status": "Approved",
#         "helpfulCount": 12,
#         "userFoundHelpful": null,
#         "author": {
#           "id": "550e8400-e29b-41d4-a716-446655440002",
#           "displayName": "Ahmed Khan",
#           "profilePicture": "https://example.com/profile.jpg",
#           "isVerified": true
#         },
#         "response": {
#           "id": "550e8400-e29b-41d4-a716-446655440003",
#           "responseText": "Thank you for your kind words!",
#           "respondedBy": {
#             "name": "Al-Noor Islamic Center",
#             "type": "Organization",
#             "logoURL": "https://example.com/logo.jpg"
#           },
#           "createdAt": "2024-01-15T10:30:00Z"
#         },
#         "createdAt": "2024-01-10T14:20:00Z",
#         "updatedAt": "2024-01-10T14:20:00Z"
#       }
#     ],
#     "totalCount": 45,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 3
#   },
#   "message": null,
#   "error": null
# }

### 2. Get Review Summary for Listing
GET {{baseUrl}}/api/review/summary?listingId={{listingId}}
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "averageRating": 4.2,
#     "islamicComplianceAverage": 4.5,
#     "totalReviews": 45,
#     "ratingDistribution": {
#       "1": 2,
#       "2": 3,
#       "3": 8,
#       "4": 15,
#       "5": 17
#     }
#   },
#   "message": null,
#   "error": null
# }

### ========================================
### AUTHENTICATED ENDPOINTS (Bearer Token Required)
### ========================================

### 3. Submit Review
POST {{baseUrl}}/api/review
Authorization: Bearer {{bearerToken}}
Content-Type: application/json

{
  "listingId": "{{listingId}}",
  "rating": 5,
  "title": "Excellent Islamic Center",
  "reviewText": "Great place for prayers and community events. Very welcoming atmosphere and knowledgeable imam.",
  "islamicComplianceRating": 5
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-446655440001",
#     "status": "Pending",
#     "message": "Review submitted successfully and is under moderation. You will be notified once it's approved."
#   },
#   "message": "Review created successfully",
#   "error": null
# }

### 4. Update Review
PUT {{baseUrl}}/api/review/{{reviewId}}
Authorization: Bearer {{bearerToken}}
Content-Type: application/json

{
  "rating": 4,
  "title": "Good Islamic Center",
  "reviewText": "Updated review: Good place for prayers. Could improve parking facilities.",
  "islamicComplianceRating": 4
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-446655440001",
#     "listingID": "550e8400-e29b-41d4-a716-446655440000",
#     "listingTitle": "Al-Noor Islamic Center",
#     "rating": 4,
#     "title": "Good Islamic Center",
#     "reviewText": "Updated review: Good place for prayers. Could improve parking facilities.",
#     "islamicComplianceRating": 4,
#     "status": "Pending",
#     "helpfulCount": 0,
#     "author": {
#       "id": "550e8400-e29b-41d4-a716-446655440002",
#       "displayName": "Ahmed Khan",
#       "profilePicture": null,
#       "isVerified": false
#     },
#     "response": null,
#     "createdAt": "2024-01-10T14:20:00Z",
#     "updatedAt": "2024-01-10T15:30:00Z"
#   },
#   "message": "Review updated successfully",
#   "error": null
# }

### 5. Delete Review
DELETE {{baseUrl}}/api/review/{{reviewId}}
Authorization: Bearer {{bearerToken}}

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "Review deleted successfully",
#   "error": null
# }

### 6. Mark Review as Helpful
POST {{baseUrl}}/api/review/{{reviewId}}/helpful
Authorization: Bearer {{bearerToken}}
Content-Type: application/json

{
  "isHelpful": true
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "helpfulCount": 13,
#     "userVote": true
#   },
#   "message": "Vote recorded successfully",
#   "error": null
# }

### 7. Report Review
POST {{baseUrl}}/api/review/{{reviewId}}/report
Authorization: Bearer {{bearerToken}}
Content-Type: application/json

{
  "reasonCode": "Inappropriate",
  "reasonText": "Contains offensive language and inappropriate content."
}

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "Review reported successfully",
#   "error": null
# }

### 8. Respond to Review (Organization Members Only)
POST {{baseUrl}}/api/review/{{reviewId}}/response
Authorization: Bearer {{bearerToken}}
Content-Type: application/json

{
  "responseText": "Thank you for your feedback! We're glad you enjoyed your experience at our center. We're working on improving our parking facilities."
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-446655440003",
#     "responseText": "Thank you for your feedback! We're glad you enjoyed your experience at our center. We're working on improving our parking facilities.",
#     "respondedBy": {
#       "name": "Al-Noor Islamic Center",
#       "type": "Organization",
#       "logoURL": "https://example.com/logo.jpg"
#     },
#     "createdAt": "2024-01-15T10:30:00Z"
#   },
#   "message": "Response created successfully",
#   "error": null
# }

### 9. Get User's Reviews
GET {{baseUrl}}/api/review/user?page=1&limit=20&status=Approved
Authorization: Bearer {{bearerToken}}
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-446655440001",
#         "listingID": "550e8400-e29b-41d4-a716-446655440000",
#         "listingTitle": "Al-Noor Islamic Center",
#         "rating": 5,
#         "title": "Excellent Islamic Center",
#         "reviewText": "Great place for prayers and community events.",
#         "islamicComplianceRating": 5,
#         "status": "Approved",
#         "helpfulCount": 12,
#         "response": null,
#         "createdAt": "2024-01-10T14:20:00Z",
#         "updatedAt": "2024-01-10T14:20:00Z"
#       }
#     ],
#     "totalCount": 5,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 1
#   },
#   "message": null,
#   "error": null
# }

### ========================================
### ERROR RESPONSES
### ========================================

### Validation Error Example:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "VALIDATION_FAILED",
#     "message": "Validation failed",
#     "details": {
#       "Rating": ["Rating is required", "Rating must be between 1 and 5"],
#       "ListingId": ["Listing ID is required"]
#     }
#   }
# }

### Authentication Error Example:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "UNAUTHORIZED",
#     "message": "User not authenticated"
#   }
# }

### Business Logic Error Example:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "REVIEW_ALREADY_EXISTS",
#     "message": "You have already reviewed this listing"
#   }
# }
