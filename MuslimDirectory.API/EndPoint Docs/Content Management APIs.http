### Content Management APIs Documentation
### Base URL: {{baseUrl}}/api/content

### Variables
@baseUrl = https://localhost:7001
@authToken = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
@adminToken = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### ========================================
### CONTENT PAGES ENDPOINTS
### ========================================

### 1. Get Content Pages (Admin)
GET {{baseUrl}}/api/content/pages
Accept: application/json
Authorization: {{adminToken}}

### Get Content Pages with Search and Filters
GET {{baseUrl}}/api/content/pages?search=about&isPublished=true&page=1&limit=10
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-446655440000",
#         "slug": "about-us",
#         "title": "About Us",
#         "content": "Welcome to Muslim Directory...",
#         "metaTitle": "About Muslim Directory",
#         "metaDescription": "Learn about our mission...",
#         "metaKeywords": "muslim, directory, about",
#         "isPublished": true,
#         "sortOrder": 1,
#         "createdAt": "2024-01-10T08:30:00Z",
#         "updatedAt": "2024-01-12T10:15:00Z",
#         "createdBy": "Admin User",
#         "updatedBy": "Admin User"
#       }
#     ],
#     "totalCount": 5,
#     "page": 1,
#     "pageSize": 10,
#     "totalPages": 1
#   }
# }

### 2. Get Content Page by Slug (Public)
GET {{baseUrl}}/api/content/pages/about-us
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-446655440000",
#     "slug": "about-us",
#     "title": "About Us",
#     "content": "Welcome to Muslim Directory...",
#     "metaTitle": "About Muslim Directory",
#     "metaDescription": "Learn about our mission...",
#     "isPublished": true,
#     "sortOrder": 1,
#     "createdAt": "2024-01-10T08:30:00Z"
#   }
# }

### 3. Create Content Page (Admin)
POST {{baseUrl}}/api/content/pages
Accept: application/json
Authorization: {{adminToken}}
Content-Type: application/json

{
  "slug": "privacy-policy",
  "title": "Privacy Policy",
  "content": "This privacy policy explains how we collect and use your data...",
  "metaTitle": "Privacy Policy - Muslim Directory",
  "metaDescription": "Our privacy policy and data protection practices",
  "metaKeywords": "privacy, policy, data, protection",
  "isPublished": true,
  "sortOrder": 5
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-446655440001",
#     "slug": "privacy-policy",
#     "title": "Privacy Policy",
#     "content": "This privacy policy explains...",
#     "isPublished": true,
#     "createdAt": "2024-01-15T14:30:00Z"
#   },
#   "message": "Content page created successfully"
# }

### 4. Update Content Page (Admin)
PUT {{baseUrl}}/api/content/pages/550e8400-e29b-41d4-a716-446655440001
Accept: application/json
Authorization: {{adminToken}}
Content-Type: application/json

{
  "title": "Updated Privacy Policy",
  "content": "This updated privacy policy explains...",
  "metaDescription": "Our updated privacy policy and data protection practices",
  "isPublished": true
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-446655440001",
#     "title": "Updated Privacy Policy",
#     "content": "This updated privacy policy explains...",
#     "updatedAt": "2024-01-15T15:30:00Z"
#   },
#   "message": "Content page updated successfully"
# }

### 5. Delete Content Page (Admin)
DELETE {{baseUrl}}/api/content/pages/550e8400-e29b-41d4-a716-446655440001
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "Content page deleted successfully"
# }

### ========================================
### FAQ ENDPOINTS
### ========================================

### 6. Get FAQs (Public)
GET {{baseUrl}}/api/content/faqs
Accept: application/json

### Get FAQs with Search and Filters
GET {{baseUrl}}/api/content/faqs?search=prayer&category=Religious&isPublished=true&page=1&limit=10
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-************",
#         "question": "How do I find prayer times?",
#         "answer": "You can find prayer times by searching for mosques in your area...",
#         "category": "Religious",
#         "isPublished": true,
#         "sortOrder": 1,
#         "viewCount": 125,
#         "helpfulCount": 23,
#         "createdAt": "2024-01-10T08:30:00Z",
#         "isHelpful": false
#       }
#     ],
#     "totalCount": 15,
#     "page": 1,
#     "pageSize": 10,
#     "totalPages": 2
#   }
# }

### 7. Get FAQ Categories (Public)
GET {{baseUrl}}/api/content/faqs/categories
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": [
#     {
#       "name": "Religious",
#       "description": null,
#       "faqCount": 8,
#       "sortOrder": 1
#     },
#     {
#       "name": "Technical",
#       "description": null,
#       "faqCount": 5,
#       "sortOrder": 2
#     }
#   ]
# }

### 8. Get FAQ by ID (Public)
GET {{baseUrl}}/api/content/faqs/550e8400-e29b-41d4-a716-************
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-************",
#     "question": "How do I find prayer times?",
#     "answer": "You can find prayer times by searching for mosques in your area...",
#     "category": "Religious",
#     "viewCount": 126,
#     "helpfulCount": 23,
#     "isHelpful": false
#   }
# }

### 9. Create FAQ (Admin)
POST {{baseUrl}}/api/content/faqs
Accept: application/json
Authorization: {{adminToken}}
Content-Type: application/json

{
  "question": "How do I add my business to the directory?",
  "answer": "To add your business, create an account and click 'Add Listing'...",
  "category": "General",
  "isPublished": true,
  "sortOrder": 10
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-************",
#     "question": "How do I add my business to the directory?",
#     "answer": "To add your business, create an account...",
#     "category": "General",
#     "isPublished": true,
#     "createdAt": "2024-01-15T14:30:00Z"
#   },
#   "message": "FAQ created successfully"
# }

### 10. Update FAQ (Admin)
PUT {{baseUrl}}/api/content/faqs/550e8400-e29b-41d4-a716-************
Accept: application/json
Authorization: {{adminToken}}
Content-Type: application/json

{
  "question": "How do I add my business to the Muslim directory?",
  "answer": "To add your business to our Muslim directory, create an account...",
  "category": "Business"
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-************",
#     "question": "How do I add my business to the Muslim directory?",
#     "answer": "To add your business to our Muslim directory...",
#     "category": "Business",
#     "updatedAt": "2024-01-15T15:30:00Z"
#   },
#   "message": "FAQ updated successfully"
# }

### 11. Delete FAQ (Admin)
DELETE {{baseUrl}}/api/content/faqs/550e8400-e29b-41d4-a716-************
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "FAQ deleted successfully"
# }

### 12. Mark FAQ as Helpful (Public)
POST {{baseUrl}}/api/content/faqs/550e8400-e29b-41d4-a716-************/helpful
Accept: application/json
Content-Type: application/json

true

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "FAQ feedback recorded successfully"
# }

### ========================================
### CONTACT SUBMISSIONS ENDPOINTS
### ========================================

### 13. Get Contact Submissions (Admin)
GET {{baseUrl}}/api/content/contact-submissions
Accept: application/json
Authorization: {{adminToken}}

### Get Contact Submissions with Filters
GET {{baseUrl}}/api/content/contact-submissions?status=New&priority=High&page=1&limit=10
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-446655440004",
#         "name": "John Doe",
#         "email": "<EMAIL>",
#         "phoneNumber": "+1234567890",
#         "subject": "Issue with listing submission",
#         "message": "I'm having trouble submitting my mosque listing...",
#         "status": "New",
#         "priority": "Medium",
#         "category": "Technical",
#         "response": null,
#         "createdAt": "2024-01-15T10:30:00Z",
#         "respondedAt": null,
#         "respondedBy": null
#       }
#     ],
#     "totalCount": 25,
#     "page": 1,
#     "pageSize": 10,
#     "totalPages": 3
#   }
# }

### 14. Create Contact Submission (Public)
POST {{baseUrl}}/api/content/contact-submissions
Accept: application/json
Content-Type: application/json

{
  "name": "Sarah Ahmed",
  "email": "<EMAIL>",
  "phoneNumber": "+1987654321",
  "subject": "Question about halal restaurants",
  "message": "I would like to know how restaurants are verified as halal in your directory...",
  "category": "General"
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-446655440005",
#     "name": "Sarah Ahmed",
#     "email": "<EMAIL>",
#     "subject": "Question about halal restaurants",
#     "status": "New",
#     "priority": "Medium",
#     "createdAt": "2024-01-15T14:30:00Z"
#   },
#   "message": "Contact submission created successfully"
# }

### 15. Update Contact Submission (Admin)
PUT {{baseUrl}}/api/content/contact-submissions/550e8400-e29b-41d4-a716-446655440005
Accept: application/json
Authorization: {{adminToken}}
Content-Type: application/json

{
  "status": "Resolved",
  "response": "Thank you for your question. Restaurants are verified through our certification process...",
  "priority": "Medium",
  "internalNotes": "Customer satisfied with response",
  "category": "General"
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "id": "550e8400-e29b-41d4-a716-446655440005",
#     "status": "Resolved",
#     "response": "Thank you for your question...",
#     "respondedAt": "2024-01-15T15:30:00Z",
#     "respondedBy": "Admin User"
#   },
#   "message": "Contact submission updated successfully"
# }

### ========================================
### STATISTICS ENDPOINTS
### ========================================

### 16. Get Content Statistics (Admin)
GET {{baseUrl}}/api/content/stats
Accept: application/json
Authorization: {{adminToken}}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "totalPages": 8,
#     "publishedPages": 6,
#     "draftPages": 2,
#     "totalFaqs": 25,
#     "publishedFaqs": 23,
#     "faqCategories": 5,
#     "totalContactSubmissions": 156,
#     "newContactSubmissions": 12,
#     "resolvedContactSubmissions": 134,
#     "mostViewedFaqId": 123456,
#     "mostViewedFaqQuestion": "How do I find prayer times?",
#     "lastContentUpdate": "2024-01-15T12:00:00Z"
#   }
# }

### ========================================
### ERROR RESPONSES
### ========================================

### Unauthorized Access:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "UNAUTHORIZED",
#     "message": "Admin role required"
#   }
# }

### Resource Not Found:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "CONTENT_PAGE_NOT_FOUND",
#     "message": "Content page not found"
#   }
# }

### Validation Error:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "SLUG_ALREADY_EXISTS",
#     "message": "A page with this slug already exists"
#   }
# }
