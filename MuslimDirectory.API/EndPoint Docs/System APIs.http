### System APIs Documentation
### Base URL: {{baseUrl}}/api/system

### Variables
@baseUrl = https://localhost:7001

### ========================================
### PUBLIC ENDPOINTS (No Authentication Required)
### ========================================

### 1. System Health Check
GET {{baseUrl}}/api/system/health
Accept: application/json

### Sample Response (Healthy):
# {
#   "success": true,
#   "data": {
#     "status": "Healthy",
#     "timestamp": "2024-01-15T14:30:00Z",
#     "uptime": "2.15:30:45",
#     "version": "1.0.0",
#     "components": {
#       "Database": {
#         "status": "Healthy",
#         "description": "Database connection successful",
#         "responseTime": "00:00:00.0250000",
#         "lastChecked": "2024-01-15T14:30:00Z",
#         "details": {
#           "ConnectionString": "Server=localhost",
#           "ResponseTimeMs": 25
#         }
#       },
#       "EmailService": {
#         "status": "Healthy",
#         "description": "Email service configured",
#         "responseTime": "00:00:00.0050000",
#         "lastChecked": "2024-01-15T14:30:00Z",
#         "details": {
#           "Configured": true,
#           "Provider": "Resend"
#         }
#       }
#     },
#     "metrics": {
#       "totalUsers": 1250,
#       "activeUsers": 345,
#       "totalListings": 2890,
#       "activeListings": 2654,
#       "totalReviews": 5432,
#       "totalOrganizations": 156,
#       "totalSearches": 12890,
#       "averageResponseTime": 150.0,
#       "requestsPerMinute": 25,
#       "databaseConnectionPoolUsage": 15.5,
#       "memoryUsageMB": 256,
#       "cpuUsagePercent": 12.5
#     }
#   },
#   "message": null,
#   "error": null
# }

### 2. System Settings
GET {{baseUrl}}/api/system/settings
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "applicationName": "Muslim Directory",
#     "environment": "Production",
#     "maintenanceMode": false,
#     "maintenanceMessage": null,
#     "registrationEnabled": true,
#     "emailVerificationRequired": true,
#     "reviewModerationEnabled": true,
#     "maxFileUploadSizeMB": 10,
#     "allowedImageFormats": ["jpg", "jpeg", "png", "webp"],
#     "sessionTimeoutMinutes": 60,
#     "passwordMinLength": 8,
#     "twoFactorAuthEnabled": false,
#     "rateLimits": {
#       "requestsPerMinute": 100,
#       "searchRequestsPerMinute": 50,
#       "authRequestsPerMinute": 10,
#       "uploadRequestsPerMinute": 5
#     },
#     "searchSettings": {
#       "maxResultsPerPage": 100,
#       "defaultResultsPerPage": 20,
#       "maxSearchRadius": 100,
#       "defaultSearchRadius": 25,
#       "geolocationEnabled": true,
#       "searchHistoryRetentionDays": 90
#     },
#     "notificationSettings": {
#       "emailNotificationsEnabled": true,
#       "pushNotificationsEnabled": false,
#       "reviewNotificationsEnabled": true,
#       "listingNotificationsEnabled": true,
#       "systemNotificationsEnabled": true
#     }
#   },
#   "message": null,
#   "error": null
# }

### 3. System Version
GET {{baseUrl}}/api/system/version
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "version": "1.0.0",
#     "buildNumber": "202401151430",
#     "buildDate": "2024-01-15T14:30:00Z",
#     "environment": "Production",
#     "gitCommit": "abc123def456",
#     "gitBranch": "main",
#     "features": [
#       "EmailVerification",
#       "ReviewModeration",
#       "GeolocationSearch",
#       "FileUpload"
#     ],
#     "dependencies": {
#       "Microsoft.AspNetCore": "8.0.0",
#       "System.Data.SqlClient": "4.8.5",
#       "XGENO.DBHelpers": "1.0.0"
#     },
#     "compatibility": {
#       "minimumClientVersion": "1.0.0",
#       "recommendedClientVersion": "1.0.0",
#       "supportedApiVersions": ["v1"],
#       "backwardCompatible": true
#     }
#   },
#   "message": null,
#   "error": null
# }

### 4. Get All Countries
GET {{baseUrl}}/api/system/countries
Accept: application/json

### Get Countries with States
GET {{baseUrl}}/api/system/countries?includeStates=true
Accept: application/json

### Get Countries with States and Cities
GET {{baseUrl}}/api/system/countries?includeStates=true&includeCities=true
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": [
#     {
#       "code": "US",
#       "name": "United States",
#       "nativeName": "United States",
#       "region": "Americas",
#       "subRegion": "Northern America",
#       "languages": ["en"],
#       "currencies": ["USD"],
#       "flag": "🇺🇸",
#       "isSupported": true,
#       "listingCount": 1250,
#       "statesProvinces": [
#         {
#           "code": "NY",
#           "name": "New York",
#           "type": "State",
#           "listingCount": 156,
#           "majorCities": [
#             {
#               "name": "New York City",
#               "latitude": 40.7128,
#               "longitude": -74.0060,
#               "listingCount": 89,
#               "isCapital": false,
#               "isMajorCity": true
#             }
#           ]
#         }
#       ]
#     }
#   ],
#   "message": null,
#   "error": null
# }

### 5. Get Supported Countries Only
GET {{baseUrl}}/api/system/supported-countries
Accept: application/json

### 6. Get Country by Code
GET {{baseUrl}}/api/system/countries/US
Accept: application/json

### Get Country with States
GET {{baseUrl}}/api/system/countries/US?includeStates=true
Accept: application/json

### 7. Get States by Country
GET {{baseUrl}}/api/system/countries/US/states
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": [
#     {
#       "code": "NY",
#       "name": "New York",
#       "type": "State",
#       "listingCount": 156,
#       "majorCities": []
#     },
#     {
#       "code": "CA",
#       "name": "California",
#       "type": "State",
#       "listingCount": 298,
#       "majorCities": []
#     }
#   ],
#   "message": null,
#   "error": null
# }

### 8. Get Major Cities by Country
GET {{baseUrl}}/api/system/countries/US/cities
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": [
#     {
#       "name": "New York City",
#       "latitude": 40.7128,
#       "longitude": -74.0060,
#       "listingCount": 89,
#       "isCapital": false,
#       "isMajorCity": true
#     },
#     {
#       "name": "Los Angeles",
#       "latitude": 34.0522,
#       "longitude": -118.2437,
#       "listingCount": 67,
#       "isCapital": false,
#       "isMajorCity": true
#     }
#   ],
#   "message": null,
#   "error": null
# }

### 9. Get Cities by State
GET {{baseUrl}}/api/system/states/550e8400-e29b-41d4-a716-446655440000/cities
Accept: application/json

### ========================================
### ADDITIONAL EXAMPLES
### ========================================

### Health Check for Monitoring
GET {{baseUrl}}/api/system/health
Accept: application/json
User-Agent: HealthCheck/1.0

### Get Settings for Client Configuration
GET {{baseUrl}}/api/system/settings
Accept: application/json

### Version Check for Compatibility
GET {{baseUrl}}/api/system/version
Accept: application/json

### Location Data for Forms
GET {{baseUrl}}/api/system/countries?includeStates=true
Accept: application/json

### ========================================
### ERROR RESPONSES
### ========================================

### Country Not Found:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "COUNTRY_NOT_FOUND",
#     "message": "Country not found or not supported"
#   }
# }

### Invalid Country Code:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "INVALID_COUNTRY_CODE",
#     "message": "Country code must be a valid 2-letter ISO code"
#   }
# }

### System Unhealthy:
# {
#   "success": false,
#   "data": {
#     "status": "Unhealthy",
#     "timestamp": "2024-01-15T14:30:00Z",
#     "components": {
#       "Database": {
#         "status": "Unhealthy",
#         "description": "Database connection failed: Connection timeout"
#       }
#     }
#   },
#   "message": null,
#   "error": null
# }
