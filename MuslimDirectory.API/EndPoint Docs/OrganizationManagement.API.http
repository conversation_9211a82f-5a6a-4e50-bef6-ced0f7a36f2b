### Organization Management API Documentation
### Base URL: https://localhost:7001/api/organization

### Variables
@baseUrl = https://localhost:7001
@organizationId = 00000000-0000-0000-0000-000000000000
@memberId = 00000000-0000-0000-0000-000000000000

### Authentication Token (Replace with actual JWT token)
@authToken = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### ========================================
### 1. GET ALL ORGANIZATIONS
### ========================================

### Get all organizations (public endpoint)
GET {{baseUrl}}/api/organization
Accept: application/json

### Get organizations with search filter
GET {{baseUrl}}/api/organization?search=mosque&page=1&pageSize=10
Accept: application/json

### Get organizations by country
GET {{baseUrl}}/api/organization?country=US&isVerified=true
Accept: application/json

### Get organizations with pagination
GET {{baseUrl}}/api/organization?page=2&pageSize=5
Accept: application/json

### ========================================
### 2. CREATE ORGANIZATION
### ========================================

### Create a new organization (requires authentication)
POST {{baseUrl}}/api/organization
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "Islamic Community Center",
  "description": "A community center serving the local Muslim population with prayer services, educational programs, and community events.",
  "logoURL": "https://example.com/logo.png",
  "website": "https://islamiccenter.org",
  "email": "<EMAIL>",
  "phoneNumber": "******-0123",
  "address": "123 Main Street, Anytown, State 12345",
  "country": "US",
  "islamicComplianceCertificate": "https://example.com/certificate.pdf"
}

### Create organization with minimal data
POST {{baseUrl}}/api/organization
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "Local Mosque"
}

### ========================================
### 3. GET ORGANIZATION BY ID
### ========================================

### Get organization details (public endpoint)
GET {{baseUrl}}/api/organization/{{organizationId}}
Accept: application/json

### ========================================
### 4. UPDATE ORGANIZATION
### ========================================

### Update organization (requires authentication and proper role)
PUT {{baseUrl}}/api/organization/{{organizationId}}
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "Updated Islamic Community Center",
  "description": "Updated description with new programs and services.",
  "website": "https://updated-islamiccenter.org",
  "email": "<EMAIL>"
}

### Partial update - only name
PUT {{baseUrl}}/api/organization/{{organizationId}}
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "New Organization Name"
}

### ========================================
### 5. GET ORGANIZATION MEMBERS
### ========================================

### Get organization members (requires authentication and membership)
GET {{baseUrl}}/api/organization/{{organizationId}}/members
Authorization: {{authToken}}
Accept: application/json

### ========================================
### 6. ADD ORGANIZATION MEMBER
### ========================================

### Add member to organization (requires authentication and admin/owner role)
POST {{baseUrl}}/api/organization/{{organizationId}}/members
Content-Type: application/json
Authorization: {{authToken}}

{
  "email": "<EMAIL>",
  "role": "Member"
}

### Add admin member
POST {{baseUrl}}/api/organization/{{organizationId}}/members
Content-Type: application/json
Authorization: {{authToken}}

{
  "email": "<EMAIL>",
  "role": "Admin"
}

### ========================================
### 7. GET MY ORGANIZATIONS
### ========================================

### Get current user's organizations (requires authentication)
GET {{baseUrl}}/api/organization/my-organizations
Authorization: {{authToken}}
Accept: application/json

### ========================================
### 8. REMOVE ORGANIZATION MEMBER
### ========================================

### Remove member from organization (requires authentication and admin/owner role)
DELETE {{baseUrl}}/api/organization/{{organizationId}}/members/{{memberId}}
Authorization: {{authToken}}

### ========================================
### 9. UPDATE MEMBER ROLE
### ========================================

### Update member role (requires authentication and owner role)
PUT {{baseUrl}}/api/organization/{{organizationId}}/members/{{memberId}}/role
Content-Type: application/json
Authorization: {{authToken}}

{
  "role": "Admin"
}

### ========================================
### ERROR TESTING
### ========================================

### Test validation errors - empty name
POST {{baseUrl}}/api/organization
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "",
  "description": "Test organization"
}

### Test unauthorized access
GET {{baseUrl}}/api/organization/{{organizationId}}/members

### Test non-existent organization
GET {{baseUrl}}/api/organization/99999999-9999-9999-9999-999999999999
Accept: application/json

### Test adding existing member
POST {{baseUrl}}/api/organization/{{organizationId}}/members
Content-Type: application/json
Authorization: {{authToken}}

{
  "email": "<EMAIL>",
  "role": "Member"
}

### Test adding non-existent user
POST {{baseUrl}}/api/organization/{{organizationId}}/members
Content-Type: application/json
Authorization: {{authToken}}

{
  "email": "<EMAIL>",
  "role": "Member"
}

### ========================================
### RESPONSE EXAMPLES
### ========================================

### Successful Organization List Response:
# {
#   "isSuccess": true,
#   "data": {
#     "items": [
#       {
#         "id": "123e4567-e89b-12d3-a456-426614174000",
#         "name": "Islamic Community Center",
#         "description": "A community center serving...",
#         "logoURL": "https://example.com/logo.png",
#         "country": "US",
#         "isVerified": true,
#         "memberCount": 25,
#         "createdAt": "2024-01-15T10:30:00Z"
#       }
#     ],
#     "totalCount": 1,
#     "page": 1,
#     "pageSize": 10,
#     "totalPages": 1
#   },
#   "message": "Success"
# }

### Successful Organization Details Response:
# {
#   "isSuccess": true,
#   "data": {
#     "id": "123e4567-e89b-12d3-a456-426614174000",
#     "name": "Islamic Community Center",
#     "description": "A community center serving...",
#     "logoURL": "https://example.com/logo.png",
#     "website": "https://islamiccenter.org",
#     "email": "<EMAIL>",
#     "phoneNumber": "******-0123",
#     "address": "123 Main Street, Anytown, State 12345",
#     "country": "US",
#     "isVerified": true,
#     "islamicComplianceCertificate": "https://example.com/certificate.pdf",
#     "createdAt": "2024-01-15T10:30:00Z",
#     "updatedAt": "2024-01-20T14:45:00Z",
#     "memberCount": 25
#   },
#   "message": "Success"
# }

### Error Response Example:
# {
#   "isSuccess": false,
#   "message": "Organization not found",
#   "errorCode": "ORGANIZATION_NOT_FOUND"
# }
