### Listing Management APIs Documentation
### Base URL: https://localhost:7001/api/listing

### Variables
@baseUrl = https://localhost:7001/api
@authToken = Bearer YOUR_JWT_TOKEN_HERE
@listingId = 00000000-0000-0000-0000-000000000000

### ========================================
### PUBLIC ENDPOINTS
### ========================================

### Get listings with filtering and pagination
GET {{baseUrl}}/listing?page=1&limit=20&search=prayer&category=uuid&platform=iOS&language=EN&pricingModel=Free&complianceStatus=Verified&featured=true&sortBy=newest&minRating=4
Accept: application/json

### Get featured listings for homepage
GET {{baseUrl}}/listing/featured?limit=6
Accept: application/json

### Get recently added listings
GET {{baseUrl}}/listing/new-releases?limit=10&days=30
Accept: application/json

### Get trending listings
GET {{baseUrl}}/listing/trending?timeframe=week&limit=10
Accept: application/json

### Get detailed listing information
GET {{baseUrl}}/listing/{{listingId}}
Accept: application/json

### Track listing view (for analytics)
POST {{baseUrl}}/listing/{{listingId}}/view
Content-Type: application/json

{
  "source": "search",
  "referrer": "category_page"
}

### ========================================
### AUTHENTICATED ENDPOINTS
### ========================================

### Get personalized recommendations (authenticated users)
GET {{baseUrl}}/listing/recommendations
Accept: application/json
Authorization: {{authToken}}

### Submit new listing
POST {{baseUrl}}/listing
Content-Type: application/json
Authorization: {{authToken}}

{
  "title": "My Islamic App",
  "shortDescription": "Brief description under 400 characters",
  "fullDescription": "Detailed description with features and benefits",
  "logoURL": "https://example.com/logo.png",
  "website": "https://myapp.com",
  "platformType": "Mobile App",
  "supportedPlatforms": ["iOS", "Android"],
  "appStoreURL": "https://apps.apple.com/app/...",
  "playStoreURL": "https://play.google.com/store/apps/...",
  "websiteURL": null,
  "pricingModel": "Free",
  "price": 0,
  "currency": "USD",
  "organizationId": "00000000-0000-0000-0000-000000000000",
  "categoryIds": ["uuid1", "uuid2"],
  "primaryCategoryId": "uuid1",
  "tagIds": ["uuid1", "uuid2"],
  "primaryLanguage": "EN",
  "supportedLanguages": ["EN", "AR"],
  "metaTitle": "SEO optimized title",
  "metaDescription": "SEO description",
  "media": [
    {
      "mediaType": "Screenshot",
      "mediaURL": "https://example.com/screenshot1.png",
      "sortOrder": 1
    }
  ]
}

### Update listing (organization members only)
PUT {{baseUrl}}/listing/{{listingId}}
Content-Type: application/json
Authorization: {{authToken}}

{
  "title": "Updated Islamic App",
  "shortDescription": "Updated brief description",
  "fullDescription": "Updated detailed description",
  "logoURL": "https://example.com/updated-logo.png",
  "website": "https://updated-myapp.com",
  "platformType": "Mobile App",
  "supportedPlatforms": ["iOS", "Android", "Web"],
  "appStoreURL": "https://apps.apple.com/app/updated...",
  "playStoreURL": "https://play.google.com/store/apps/updated...",
  "websiteURL": "https://web.myapp.com",
  "pricingModel": "Freemium",
  "price": 4.99,
  "currency": "USD",
  "categoryIds": ["uuid1", "uuid3"],
  "primaryCategoryId": "uuid1",
  "tagIds": ["uuid1", "uuid3", "uuid4"],
  "primaryLanguage": "EN",
  "supportedLanguages": ["EN", "AR", "UR"],
  "metaTitle": "Updated SEO optimized title",
  "metaDescription": "Updated SEO description",
  "media": [
    {
      "mediaType": "Screenshot",
      "mediaURL": "https://example.com/updated-screenshot1.png",
      "sortOrder": 1
    },
    {
      "mediaType": "Video",
      "mediaURL": "https://example.com/demo-video.mp4",
      "thumbnailURL": "https://example.com/video-thumb.png",
      "sortOrder": 2
    }
  ]
}

### Delete listing (organization owners only)
DELETE {{baseUrl}}/listing/{{listingId}}
Authorization: {{authToken}}

### Get listing analytics (organization members only)
GET {{baseUrl}}/listing/{{listingId}}/analytics?startDate=2025-01-01&endDate=2025-12-31&granularity=day
Accept: application/json
Authorization: {{authToken}}

### ========================================
### FAVORITES ENDPOINTS
### ========================================

### Add listing to favorites
POST {{baseUrl}}/listing/{{listingId}}/favorite
Authorization: {{authToken}}

### Remove listing from favorites
DELETE {{baseUrl}}/listing/{{listingId}}/favorite
Authorization: {{authToken}}

### Check if listing is favorited by user
GET {{baseUrl}}/listing/{{listingId}}/favorite
Accept: application/json
Authorization: {{authToken}}

### Get user's favorite listings
GET {{baseUrl}}/listing/favorites?page=1&limit=20&sortBy=newest&search=prayer
Accept: application/json
Authorization: {{authToken}}

### ========================================
### SAMPLE RESPONSES
### ========================================

### Sample Response: Get listings
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "uuid",
#         "title": "Prayer Times Pro",
#         "shortDescription": "Accurate prayer times for Muslims worldwide with Qibla direction",
#         "logoURL": "https://...",
#         "platformType": "Mobile App",
#         "supportedPlatforms": ["iOS", "Android"],
#         "pricingModel": "Free",
#         "price": 0,
#         "currency": "USD",
#         "islamicComplianceStatus": "Verified",
#         "rating": {
#           "average": 4.5,
#           "count": 120
#         },
#         "organization": {
#           "id": "uuid",
#           "name": "Islamic Apps Inc",
#           "logoURL": "https://...",
#           "isVerified": true
#         },
#         "primaryCategory": {
#           "id": "uuid",
#           "name": "Prayer & Worship"
#         },
#         "featuredLevel": 1,
#         "viewCount": 1250,
#         "createdAt": "2025-01-01T00:00:00Z",
#         "updatedAt": "2025-06-01T00:00:00Z"
#       }
#     ],
#     "totalCount": 500,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 25
#   }
# }

### Sample Response: Get listing details
# {
#   "success": true,
#   "data": {
#     "id": "uuid",
#     "title": "Prayer Times Pro",
#     "shortDescription": "Accurate prayer times for Muslims worldwide",
#     "fullDescription": "Detailed description with features, benefits, and Islamic compliance information...",
#     "logoURL": "https://...",
#     "website": "https://prayertimespro.com",
#     "platformType": "Mobile App",
#     "supportedPlatforms": ["iOS", "Android"],
#     "appStoreURL": "https://apps.apple.com/app/...",
#     "playStoreURL": "https://play.google.com/store/apps/...",
#     "websiteURL": null,
#     "pricingModel": "Free",
#     "price": 0,
#     "currency": "USD",
#     "islamicComplianceStatus": "Verified",
#     "complianceNotes": "Verified by Islamic Digital Compliance Board...",
#     "organization": {
#       "id": "uuid",
#       "name": "Islamic Apps Inc",
#       "logoURL": "https://...",
#       "isVerified": true
#     },
#     "categories": [
#       {
#         "id": "uuid",
#         "name": "Prayer & Worship",
#         "isPrimary": true
#       }
#     ],
#     "tags": [
#       {
#         "id": "uuid",
#         "name": "Salah"
#       }
#     ],
#     "media": [
#       {
#         "id": "uuid",
#         "mediaType": "Screenshot",
#         "mediaURL": "https://...",
#         "thumbnailURL": "https://...",
#         "sortOrder": 1
#       }
#     ],
#     "rating": {
#       "average": 4.5,
#       "count": 120,
#       "islamicComplianceAverage": 4.8,
#       "distribution": {
#         "5": 60,
#         "4": 30,
#         "3": 20,
#         "2": 5,
#         "1": 5
#       }
#     },
#     "primaryLanguage": "EN",
#     "supportedLanguages": ["EN", "AR", "UR", "ID"],
#     "viewCount": 1500,
#     "isFavorited": false,
#     "relatedListings": [],
#     "createdAt": "2025-01-01T00:00:00Z",
#     "updatedAt": "2025-06-01T00:00:00Z"
#   }
# }

### Sample Response: Create listing
# {
#   "success": true,
#   "data": {
#     "id": "uuid",
#     "status": "Pending",
#     "message": "Listing submitted successfully and is under review. You will be notified once the review is complete."
#   }
# }

### Sample Response: Get analytics
# {
#   "success": true,
#   "data": {
#     "summary": {
#       "totalViews": 1500,
#       "totalReviews": 120,
#       "averageRating": 4.5,
#       "favoriteCount": 80
#     },
#     "viewsOverTime": [
#       {
#         "date": "2025-01-01T00:00:00Z",
#         "count": 45
#       }
#     ],
#     "reviewsOverTime": [
#       {
#         "date": "2025-01-01T00:00:00Z",
#         "count": 3
#       }
#     ],
#     "favoritesOverTime": [
#       {
#         "date": "2025-01-01T00:00:00Z",
#         "count": 2
#       }
#     ]
#   }
# }
