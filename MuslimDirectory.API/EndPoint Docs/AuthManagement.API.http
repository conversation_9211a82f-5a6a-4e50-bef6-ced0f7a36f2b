# Muslim Directory API - Authentication & Authorization Endpoints
# Base URL: https://localhost:7000/api/v1 (Development)
# Content-Type: application/json

@baseUrl = https://localhost:7000/api/v1
@contentType = application/json

###############################################################################
# 1. USER SIGNUP
###############################################################################

### POST /auth/signup - Register a new user account
POST {{baseUrl}}/auth/signup
Content-Type: {{contentType}}

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "phoneNumber": "+**********",
  "country": "US",
  "city": "New York",
  "preferredLanguage": "EN",
  "gender": "M",
  "acceptTerms": true,
  "acceptPrivacy": true,
  "isTermsAccepted": true
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "user": {
#       "id": "123e4567-e89b-12d3-a456-************",
#       "email": "<EMAIL>",
#       "firstName": "John",
#       "lastName": "Doe",
#       "profilePicture": null,
#       "gender": "M",
#       "country": "US",
#       "city": "New York",
#       "phoneNumber": "+**********",
#       "preferredLanguage": "EN",
#       "isEmailVerified": false,
#       "isPhoneVerified": false,
#       "isTermsAccepted": true,
#       "userType": "Regular",
#       "memberSince": "2024-01-15T10:30:00Z",
#       "stats": {
#         "reviewsCount": 0,
#         "favoritesCount": 0
#       },
#       "lastLoginAt": null
#     },
#     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "refreshToken": "abc123def456..."
#   },
#   "message": "User registered successfully. Please verify your email.",
#   "provider": null,
#   "error": null,
#   "meta": {
#     "timestamp": "2025-07-06T15:04:11.4711306Z",
#     "pagination": null
#   }
# }

###############################################################################
# 2. USER SIGNIN
###############################################################################

### POST /auth/signin - Authenticate user and return access token
POST {{baseUrl}}/auth/signin
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "TestPassword123!"
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "user": {
#       "id": "123e4567-e89b-12d3-a456-************",
#       "email": "<EMAIL>",
#       "firstName": "John",
#       "lastName": "Doe",
#       "userType": "Regular",
#       "preferredLanguage": "EN",
#       "profilePicture": null,
#       "isEmailVerified": false
#     },
#     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "refreshToken": "abc123def456..."
#   },
#   "message": "Signed in successfully",
#   "error": null,
#   "meta": null
# }

###############################################################################
# 3. SOCIAL LOGIN
###############################################################################

### POST /auth/social-login - Authenticate using social providers (Google, Apple)
POST {{baseUrl}}/auth/social-login
Content-Type: {{contentType}}

{
  "provider": "Google",
  "providerToken": "google_oauth_token_here",
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "profilePicture": "https://example.com/profile.jpg"
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "user": {
#       "id": "123e4567-e89b-12d3-a456-************",
#       "email": "<EMAIL>",
#       "firstName": "Jane",
#       "lastName": "Smith",
#       "userType": "Regular",
#       "preferredLanguage": "EN",
#       "profilePicture": "https://example.com/profile.jpg",
#       "isEmailVerified": true
#     },
#     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "refreshToken": "xyz789abc123..."
#   },
#   "message": "Account created and logged in successfully",
#   "error": null,
#   "meta": "Google"
# }

###############################################################################
# 4. FORGOT PASSWORD
###############################################################################

### POST /auth/forgot-password - Request password reset
POST {{baseUrl}}/auth/forgot-password
Content-Type: {{contentType}}

{
  "email": "<EMAIL>"
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Password reset instructions sent to your email",
#   "message": "Password reset instructions sent to your email",
#   "error": null,
#   "meta": null
# }

###############################################################################
# 5. RESET PASSWORD
###############################################################################

### POST /auth/reset-password - Reset password using token
POST {{baseUrl}}/auth/reset-password
Content-Type: {{contentType}}

{
  "token": "reset_token_from_email",
  "newPassword": "NewPassword123!"
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Password reset successfully",
#   "message": "Password reset successfully",
#   "error": null,
#   "meta": null
# }

###############################################################################
# 6. VERIFY EMAIL
###############################################################################

### POST /auth/verify-email - Verify email address
POST {{baseUrl}}/auth/verify-email
Content-Type: {{contentType}}

{
  "token": "email_verification_token_from_email"
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Email verified successfully",
#   "message": "Email verified successfully",
#   "error": null,
#   "meta": null
# }

###############################################################################
# 7. RESEND VERIFICATION
###############################################################################

### POST /auth/resend-verification - Resend email verification
POST {{baseUrl}}/auth/resend-verification
Content-Type: {{contentType}}

{
  "email": "<EMAIL>"
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Verification email sent successfully",
#   "message": "Verification email sent successfully",
#   "error": null,
#   "meta": null
# }

###############################################################################
# 8. REFRESH TOKEN
###############################################################################

### POST /auth/refresh-token - Refresh access token
POST {{baseUrl}}/auth/refresh-token
Content-Type: {{contentType}}

{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "user": {
#       "id": "123e4567-e89b-12d3-a456-************",
#       "email": "<EMAIL>",
#       "firstName": "John",
#       "lastName": "Doe",
#       "userType": "Regular",
#       "preferredLanguage": "EN",
#       "profilePicture": null,
#       "isEmailVerified": false
#     },
#     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "refreshToken": "new_refresh_token_here..."
#   },
#   "message": "Token refreshed successfully",
#   "error": null,
#   "meta": null
# }

###############################################################################
# 9. SIGNOUT
###############################################################################

### POST /auth/signout - Logout user and invalidate tokens (Bearer Token)
POST {{baseUrl}}/auth/signout
Content-Type: {{contentType}}
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Logged out successfully",
#   "message": "Logged out successfully",
#   "error": null,
#   "meta": null
# }

### POST /auth/signout - Logout user and invalidate tokens (Cookie-based)
POST {{baseUrl}}/auth/signout
Content-Type: {{contentType}}
# Note: Cookies will be sent automatically by the browser
# muslim_directory_access_token and muslim_directory_refresh_token cookies

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Logged out successfully",
#   "message": "Logged out successfully",
#   "error": null,
#   "meta": null
# }
# Note: Cookies will be cleared (expired) in the response

###############################################################################
# VALIDATION ERROR TESTING
###############################################################################

### POST /auth/signup - Test validation errors (should return consistent ApiResponse format)
POST {{baseUrl}}/auth/signup
Content-Type: {{contentType}}

{
  "firstName": "",
  "lastName": "",
  "email": "invalid-email",
  "password": "123",
  "phoneNumber": "",
  "country": "",
  "city": "",
  "preferredLanguage": "",
  "gender": "",
  "acceptTerms": false,
  "acceptPrivacy": false
}

# Expected Response (400 Bad Request) - Consistent ApiResponse format:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "provider": null,
#   "error": {
#     "code": "VALIDATION_ERROR",
#     "message": "The Email field is not a valid e-mail address.",
#     "field": "email",
#     "details": {
#       "firstName": ["The FirstName field is required."],
#       "lastName": ["The LastName field is required."],
#       "email": ["The Email field is not a valid e-mail address."],
#       "password": ["Password must be at least 8 characters long."],
#       "phoneNumber": ["The PhoneNumber field is required."],
#       "country": ["The Country field is required."],
#       "city": ["The City field is required."],
#       "preferredLanguage": ["The PreferredLanguage field is required."],
#       "gender": ["The Gender field is required."],
#       "acceptTerms": ["You must accept the terms and conditions."],
#       "acceptPrivacy": ["You must accept the privacy policy."]
#     }
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

### POST /auth/signin - Test validation errors for signin
POST {{baseUrl}}/auth/signin
Content-Type: {{contentType}}

{
  "email": "",
  "password": ""
}

# Expected Response (400 Bad Request) - Consistent ApiResponse format:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "provider": null,
#   "error": {
#     "code": "VALIDATION_ERROR",
#     "message": "The Email field is required.",
#     "field": "email",
#     "details": {
#       "email": ["The Email field is required."],
#       "password": ["The Password field is required."]
#     }
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

###############################################################################
# ERROR RESPONSES
###############################################################################

# Validation Error Example (400 Bad Request) - NEW CONSISTENT FORMAT:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "provider": null,
#   "error": {
#     "code": "VALIDATION_ERROR",
#     "message": "The Email field is required.",
#     "field": "email",
#     "details": {
#       "email": ["The Email field is required."],
#       "password": ["Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"]
#     }
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

# Authentication Error Example (400 Bad Request) - CONSISTENT FORMAT:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "provider": null,
#   "error": {
#     "code": "INVALID_CREDENTIALS",
#     "message": "Invalid email or password",
#     "field": null,
#     "details": null
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

# User Not Found Error Example (400 Bad Request) - CONSISTENT FORMAT:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "provider": null,
#   "error": {
#     "code": "USER_NOT_FOUND",
#     "message": "User not found",
#     "field": null,
#     "details": null
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

# Email Already Exists Error Example (400 Bad Request) - CONSISTENT FORMAT:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "provider": null,
#   "error": {
#     "code": "EMAIL_EXISTS",
#     "message": "Email already exists",
#     "field": "email",
#     "details": null
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

# Invalid Token Error Example (400 Bad Request) - CONSISTENT FORMAT:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "provider": null,
#   "error": {
#     "code": "INVALID_TOKEN",
#     "message": "Invalid or expired token",
#     "field": "token",
#     "details": null
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

###############################################################################
# VALIDATION RULES
###############################################################################

# Password Requirements:
# - Minimum 8 characters
# - Maximum 100 characters
# - At least one uppercase letter (A-Z)
# - At least one lowercase letter (a-z)
# - At least one number (0-9)
# - At least one special character (@$!%*?&)

# Email Requirements:
# - Valid email format (<EMAIL>)
# - Maximum 255 characters
# - Must be unique in the system

# Phone Number Requirements:
# - Valid international format (e.g., +**********)
# - 10-15 digits including country code
# - Must start with + followed by country code

# Country Code Requirements:
# - 2-letter ISO country code (e.g., US, UK, CA, IN, PK)
# - Must be uppercase

# Gender Requirements:
# - Single character: M (Male), F (Female), O (Other)
# - Case sensitive

# Language Requirements:
# - 2-letter language code (e.g., EN, AR, FR, UR)
# - Must be uppercase

# Name Requirements:
# - First Name: 2-50 characters, letters and spaces only
# - Last Name: 2-50 characters, letters and spaces only

# City Requirements:
# - 2-100 characters
# - Letters, spaces, hyphens, and apostrophes allowed

###############################################################################
# AUTHENTICATION HEADERS
###############################################################################

# For protected endpoints, include JWT token in Authorization header:
# Authorization: Bearer <your_jwt_token_here>

# Example:
# Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c

###############################################################################
# TESTING NOTES
###############################################################################

# 1. Make sure your MS SQL Server database is running
# 2. Update connection string in appsettings.json if needed
# 3. Run database migrations/scripts to create tables
# 4. Start the API: dotnet run
# 5. Use these HTTP requests to test endpoints
# 6. For email verification/password reset, check your email service configuration
# 7. Test validation errors to ensure consistent ApiResponse format
# 8. Verify that ModelValidationFilter is working correctly

###############################################################################
# COMMON ERROR CODES
###############################################################################

# VALIDATION_ERROR - Request validation failed
# INVALID_CREDENTIALS - Wrong email/password
# USER_NOT_FOUND - User doesn't exist
# EMAIL_EXISTS - Email already registered
# INVALID_TOKEN - Token expired or invalid
# EMAIL_ALREADY_VERIFIED - Email already verified
# SIGNUP_FAILED - User registration failed
# SIGNIN_FAILED - User login failed
# SOCIAL_LOGIN_FAILED - Social authentication failed
# FORGOT_PASSWORD_FAILED - Password reset request failed
# RESET_PASSWORD_FAILED - Password reset failed
# EMAIL_VERIFICATION_FAILED - Email verification failed
# RESEND_VERIFICATION_FAILED - Resend verification failed
# REFRESH_TOKEN_FAILED - Token refresh failed
# LOGOUT_FAILED - Logout failed
# INVALID_USER - Invalid user context
# INVALID_USER_ID - Invalid user ID format

###############################################################################
# SECURE COOKIES IMPLEMENTATION
###############################################################################

# The API now supports secure cookie-based authentication alongside JWT Bearer tokens.
#
# Key Features:
# 1. Automatic cookie setting on successful authentication (signup, signin, social-login)
# 2. HttpOnly cookies to prevent XSS attacks
# 3. Secure flag for HTTPS-only transmission (configurable)
# 4. SameSite protection against CSRF attacks
# 5. Automatic cookie clearing on logout
# 6. Support for both cookie and bearer token authentication
#
# Cookie Names:
# - Access Token: muslim_directory_access_token
# - Refresh Token: muslim_directory_refresh_token
#
# Cookie Settings (configurable in appsettings.json):
# {
#   "CookieSettings": {
#     "AccessTokenCookieName": "muslim_directory_access_token",
#     "RefreshTokenCookieName": "muslim_directory_refresh_token",
#     "AccessTokenExpirationInMinutes": 60,
#     "RefreshTokenExpirationInDays": 7,
#     "SecureOnly": false,  // Set to true in production with HTTPS
#     "HttpOnly": true,     // Prevents JavaScript access
#     "SameSite": "Lax",    // CSRF protection (Strict, Lax, None)
#     "Domain": "",         // Leave empty for current domain
#     "Path": "/"           // Cookie path
#   }
# }
#
# Authentication Flow:
# 1. User signs up/in → API sets secure cookies automatically
# 2. Browser includes cookies in subsequent requests
# 3. API validates token from either Authorization header OR cookies
# 4. User logs out → API clears cookies
#
# CORS Configuration:
# - Updated to support credentials (cookies)
# - Specific origins instead of AllowAnyOrigin()
# - AllowCredentials() enabled
#
# Security Benefits:
# - HttpOnly prevents XSS cookie theft
# - Secure flag ensures HTTPS-only transmission
# - SameSite prevents CSRF attacks
# - Automatic expiration
# - Server-side token validation
#
# Browser Compatibility:
# - All modern browsers support secure cookies
# - Automatic cookie management (no client-side code needed)
# - Works with both web apps and mobile web views
#
# Testing Notes:
# - Use browser dev tools to inspect cookies
# - Cookies are automatically included in requests
# - No need to manually set Authorization headers when using cookies
# - Test both cookie and bearer token authentication methods
