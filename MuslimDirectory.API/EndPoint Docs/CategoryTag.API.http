### Category & Tag Management API Documentation
### Base URL: https://localhost:7001/api

### Variables
@baseUrl = https://localhost:7001
@categoryId = 00000000-0000-0000-0000-000000000000
@tagId = 00000000-0000-0000-0000-000000000000

### Authentication Token (Replace with actual JWT token for admin operations)
@authToken = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### ========================================
### CATEGORY ENDPOINTS
### ========================================

### 1. GET ALL CATEGORIES
### Get all categories with subcategories (public endpoint)
GET {{baseUrl}}/api/category
Accept: application/json

### Get categories without subcategories
GET {{baseUrl}}/api/category?includeSubCategories=false
Accept: application/json

### Get categories including inactive ones
GET {{baseUrl}}/api/category?includeInactive=true
Accept: application/json

### Get subcategories of a specific parent
GET {{baseUrl}}/api/category?parentId={{categoryId}}
Accept: application/json

### ========================================
### 2. GET CATEGORY BY ID WITH LISTINGS
### ========================================

### Get category details with listings (public endpoint)
GET {{baseUrl}}/api/category/{{categoryId}}
Accept: application/json

### Get category with filtered listings
GET {{baseUrl}}/api/category/{{categoryId}}?page=1&pageSize=10&status=Approved&complianceStatus=Verified
Accept: application/json

### Get category with platform and pricing filters
GET {{baseUrl}}/api/category/{{categoryId}}?platformType=Mobile App&pricingModel=Free&sortBy=featured&sortOrder=desc
Accept: application/json

### ========================================
### 3. CREATE CATEGORY (Admin Only)
### ========================================

### Create a new root category
POST {{baseUrl}}/api/category
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "Islamic Education",
  "description": "Educational resources and learning materials for Islamic studies",
  "iconURL": "https://example.com/education-icon.png",
  "sortOrder": 1
}

### Create a subcategory
POST {{baseUrl}}/api/category
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "Quran Learning",
  "description": "Apps and resources for learning and memorizing the Quran",
  "iconURL": "https://example.com/quran-icon.png",
  "parentCategoryId": "{{categoryId}}",
  "sortOrder": 1
}

### ========================================
### 4. UPDATE CATEGORY (Admin Only)
### ========================================

### Update category details
PUT {{baseUrl}}/api/category/{{categoryId}}
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "Updated Islamic Education",
  "description": "Updated description for educational resources",
  "sortOrder": 2,
  "isActive": true
}

### ========================================
### 5. DELETE CATEGORY (Admin Only)
### ========================================

### Delete category (only if no subcategories or listings)
DELETE {{baseUrl}}/api/category/{{categoryId}}
Authorization: {{authToken}}

### ========================================
### 6. GET CATEGORIES LIST (Admin Only)
### ========================================

### Get paginated categories list for admin
GET {{baseUrl}}/api/category/list?page=1&pageSize=10
Authorization: {{authToken}}
Accept: application/json

### Search categories
GET {{baseUrl}}/api/category/list?search=education&includeInactive=true
Authorization: {{authToken}}
Accept: application/json

### ========================================
### TAG ENDPOINTS
### ========================================

### 7. GET ALL TAGS
### Get all tags with pagination (public endpoint)
GET {{baseUrl}}/api/tag?page=1&pageSize=20
Accept: application/json

### Get tags with search
GET {{baseUrl}}/api/tag?search=prayer&sortBy=usage&sortOrder=desc
Accept: application/json

### Get tags sorted by name
GET {{baseUrl}}/api/tag?sortBy=name&sortOrder=asc
Accept: application/json

### ========================================
### 8. GET TAG BY ID
### ========================================

### Get tag details (public endpoint)
GET {{baseUrl}}/api/tag/{{tagId}}
Accept: application/json

### ========================================
### 9. GET TAG WITH LISTINGS
### ========================================

### Get tag with associated listings (public endpoint)
GET {{baseUrl}}/api/tag/{{tagId}}/listings?page=1&pageSize=10
Accept: application/json

### Get tag listings with filters
GET {{baseUrl}}/api/tag/{{tagId}}/listings?status=Approved&complianceStatus=Verified
Accept: application/json

### ========================================
### 10. CREATE TAG (Admin Only)
### ========================================

### Create a new tag
POST {{baseUrl}}/api/tag
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "Prayer Times",
  "description": "Applications that provide accurate prayer times and notifications"
}

### ========================================
### 11. UPDATE TAG (Admin Only)
### ========================================

### Update tag details
PUT {{baseUrl}}/api/tag/{{tagId}}
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "Updated Prayer Times",
  "description": "Updated description for prayer time applications",
  "isActive": true
}

### ========================================
### 12. DELETE TAG (Admin Only)
### ========================================

### Delete tag (only if no associated listings)
DELETE {{baseUrl}}/api/tag/{{tagId}}
Authorization: {{authToken}}

### ========================================
### 13. GET POPULAR TAGS
### ========================================

### Get most popular tags (public endpoint)
GET {{baseUrl}}/api/tag/popular?limit=20
Accept: application/json

### Get top 10 popular tags
GET {{baseUrl}}/api/tag/popular?limit=10
Accept: application/json

### ========================================
### 14. SEARCH TAGS
### ========================================

### Search tags by name (public endpoint)
GET {{baseUrl}}/api/tag/search?query=prayer&limit=10
Accept: application/json

### Search tags with different query
GET {{baseUrl}}/api/tag/search?query=quran&limit=5
Accept: application/json

### ========================================
### ERROR TESTING
### ========================================

### Test validation errors - empty category name
POST {{baseUrl}}/api/category
Content-Type: application/json
Authorization: {{authToken}}

{
  "name": "",
  "description": "Test category"
}

### Test unauthorized access to admin endpoints
POST {{baseUrl}}/api/category
Content-Type: application/json

{
  "name": "Test Category"
}

### Test non-existent category
GET {{baseUrl}}/api/category/99999999-9999-9999-9999-999999999999
Accept: application/json

### Test non-existent tag
GET {{baseUrl}}/api/tag/99999999-9999-9999-9999-999999999999
Accept: application/json

### Test deleting category with subcategories
DELETE {{baseUrl}}/api/category/{{categoryId}}
Authorization: {{authToken}}

### Test search without query parameter
GET {{baseUrl}}/api/tag/search
Accept: application/json

### ========================================
### RESPONSE EXAMPLES
### ========================================

### Successful Categories Response:
# {
#   "isSuccess": true,
#   "data": [
#     {
#       "id": "123e4567-e89b-12d3-a456-426614174000",
#       "name": "Islamic Education",
#       "description": "Educational resources and learning materials",
#       "iconURL": "https://example.com/education-icon.png",
#       "parentCategoryId": null,
#       "parentCategoryName": null,
#       "sortOrder": 1,
#       "isActive": true,
#       "createdAt": "2024-01-15T10:30:00Z",
#       "listingCount": 15,
#       "subCategories": [
#         {
#           "id": "456e7890-e89b-12d3-a456-426614174001",
#           "name": "Quran Learning",
#           "description": "Apps for learning the Quran",
#           "iconURL": "https://example.com/quran-icon.png",
#           "parentCategoryId": "123e4567-e89b-12d3-a456-426614174000",
#           "parentCategoryName": "Islamic Education",
#           "sortOrder": 1,
#           "isActive": true,
#           "createdAt": "2024-01-16T11:00:00Z",
#           "listingCount": 8
#         }
#       ]
#     }
#   ],
#   "message": "Success"
# }

### Successful Tags Response:
# {
#   "isSuccess": true,
#   "data": {
#     "items": [
#       {
#         "id": "789e0123-e89b-12d3-a456-426614174002",
#         "name": "Prayer Times",
#         "description": "Applications that provide prayer times",
#         "isActive": true,
#         "createdAt": "2024-01-15T10:30:00Z",
#         "usageCount": 25
#       }
#     ],
#     "totalCount": 1,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 1
#   },
#   "message": "Success"
# }
