# Muslim Directory API - User Management Endpoints
# Base URL: https://localhost:7000/api/v1 (Development)
# Content-Type: application/json
# Authentication: <PERSON><PERSON> or Secure Cookies

@baseUrl = https://localhost:7000/api/v1
@contentType = application/json
@authToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

###############################################################################
# 1. GET USER PROFILE
###############################################################################

### GET /users/profile - Get current user's profile information
GET {{baseUrl}}/users/profile
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "user": {
#       "id": "123e4567-e89b-12d3-a456-426614174000",
#       "email": "<EMAIL>",
#       "firstName": "John",
#       "lastName": "Doe",
#       "profilePicture": "https://example.com/profile.jpg",
#       "gender": "M",
#       "country": "US",
#       "city": "New York",
#       "phoneNumber": "+**********",
#       "preferredLanguage": "EN",
#       "isEmailVerified": true,
#       "isPhoneVerified": false,
#       "userType": "Regular",
#       "memberSince": "2024-01-15T10:30:00Z",
#       "stats": {
#         "reviewsCount": 15,
#         "favoritesCount": 8
#       },
#       "lastLoginAt": "2025-01-04T09:15:00Z",
#       "listings": [
#         {
#           "id": "456e7890-e89b-12d3-a456-426614174001",
#           "title": "Islamic Prayer Times App",
#           "shortDescription": "Accurate prayer times for Muslims worldwide",
#           "logoURL": "https://example.com/app-logo.jpg",
#           "platformType": "Mobile App",
#           "supportedPlatforms": ["iOS", "Android"],
#           "pricingModel": "Free",
#           "price": null,
#           "currency": null,
#           "islamicComplianceStatus": "Approved",
#           "rating": {
#             "average": 4.5,
#             "count": 120,
#             "islamicComplianceAverage": 0,
#             "distribution": {}
#           },
#           "organization": {
#             "id": "789e0123-e89b-12d3-a456-426614174002",
#             "name": "Islamic Tech Solutions",
#             "logoURL": "https://example.com/org-logo.jpg",
#             "isVerified": true
#           },
#           "primaryCategory": {
#             "id": "abc12345-e89b-12d3-a456-426614174003",
#             "name": "Prayer & Worship"
#           },
#           "featuredLevel": 1,
#           "viewCount": 2500,
#           "createdAt": "2024-01-10T08:00:00Z",
#           "updatedAt": "2024-01-20T12:30:00Z"
#         },
#         {
#           "id": "def67890-e89b-12d3-a456-426614174004",
#           "title": "Halal Food Finder",
#           "shortDescription": "Find halal restaurants and food options nearby",
#           "logoURL": "https://example.com/food-app-logo.jpg",
#           "platformType": "Mobile App",
#           "supportedPlatforms": ["iOS", "Android", "Web"],
#           "pricingModel": "Freemium",
#           "price": 2.99,
#           "currency": "USD",
#           "islamicComplianceStatus": "Under Review",
#           "rating": {
#             "average": 4.2,
#             "count": 85,
#             "islamicComplianceAverage": 0,
#             "distribution": {}
#           },
#           "organization": null,
#           "primaryCategory": {
#             "id": "ghi78901-e89b-12d3-a456-426614174005",
#             "name": "Food & Dining"
#           },
#           "featuredLevel": 0,
#           "viewCount": 1200,
#           "createdAt": "2024-02-05T14:15:00Z",
#           "updatedAt": "2024-02-10T16:45:00Z"
#         }
#       ]
#     }
#   },
#   "message": null,
#   "provider": null,
#   "error": null,
#   "meta": {
#     "timestamp": "2025-07-06T15:04:11.4711306Z",
#     "pagination": null
#   }
# }

### GET /users/profile - Using Cookie Authentication (no Authorization header needed)
GET {{baseUrl}}/users/profile
Content-Type: {{contentType}}
# Note: Cookies will be sent automatically by the browser
# muslim_directory_access_token cookie

###############################################################################
# 2. UPDATE USER PROFILE
###############################################################################

### PUT /users/profile - Update user profile information
PUT {{baseUrl}}/users/profile
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "firstName": "John",
  "lastName": "Smith",
  "phoneNumber": "+**********",
  "country": "US",
  "city": "Los Angeles",
  "preferredLanguage": "EN",
  "gender": "M",
  "profilePicture": "https://example.com/new-profile.jpg"
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Profile updated successfully",
#   "message": "Profile updated successfully",
#   "error": null,
#   "meta": null
# }

### PUT /users/profile - Partial update (only update specific fields)
PUT {{baseUrl}}/users/profile
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "firstName": "Johnny",
  "city": "San Francisco"
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Profile updated successfully",
#   "message": "Profile updated successfully",
#   "error": null,
#   "meta": null
# }

###############################################################################
# 3. CHANGE PASSWORD
###############################################################################

### PUT /users/change-password - Change user password
PUT {{baseUrl}}/users/change-password
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewPassword456!",
  "confirmPassword": "NewPassword456!"
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Password changed successfully",
#   "message": "Password changed successfully",
#   "error": null,
#   "meta": null
# }

###############################################################################
# 4. GET USER PREFERENCES
###############################################################################

### GET /users/preferences - Get user preferences
GET {{baseUrl}}/users/preferences
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "language": "EN",
#     "notifications": {
#       "emailNotifications": true,
#       "pushNotifications": false,
#       "smsNotifications": false,
#       "marketingEmails": true,
#       "weeklyDigest": true,
#       "newListingAlerts": false,
#       "reviewReminders": true
#     },
#     "privacy": {
#       "profileVisibility": "Public",
#       "showEmail": false,
#       "showPhone": false,
#       "allowMessaging": true,
#       "showReviews": true,
#       "showFavorites": false
#     },
#     "appPreferences": {
#       "theme": "Light",
#       "defaultView": "Grid",
#       "autoPlayVideos": false,
#       "preferredPlatforms": ["iOS", "Android"],
#       "defaultSortBy": "Rating",
#       "showOnlyVerified": false
#     }
#   },
#   "message": null,
#   "error": null,
#   "meta": null
# }

###############################################################################
# 5. UPDATE USER PREFERENCES
###############################################################################

### PUT /users/preferences - Update user preferences (full update)
PUT {{baseUrl}}/users/preferences
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "language": "AR",
  "notifications": {
    "emailNotifications": false,
    "pushNotifications": true,
    "smsNotifications": false,
    "marketingEmails": false,
    "weeklyDigest": true,
    "newListingAlerts": true,
    "reviewReminders": false
  },
  "privacy": {
    "profileVisibility": "Private",
    "showEmail": false,
    "showPhone": false,
    "allowMessaging": false,
    "showReviews": true,
    "showFavorites": false
  },
  "appPreferences": {
    "theme": "Dark",
    "defaultView": "List",
    "autoPlayVideos": true,
    "preferredPlatforms": ["iOS", "Web"],
    "defaultSortBy": "Newest",
    "showOnlyVerified": true
  }
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Preferences updated successfully",
#   "message": "Preferences updated successfully",
#   "error": null,
#   "meta": null
# }

### PUT /users/preferences - Partial preferences update
PUT {{baseUrl}}/users/preferences
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "language": "UR",
  "notifications": {
    "emailNotifications": true,
    "pushNotifications": true
  },
  "appPreferences": {
    "theme": "Dark",
    "preferredPlatforms": ["Android", "Web", "Desktop"]
  }
}

# Expected Response (200 OK):
# {
#   "success": true,
#   "data": "Preferences updated successfully",
#   "message": "Preferences updated successfully",
#   "error": null,
#   "meta": null
# }

###############################################################################
# VALIDATION ERROR TESTING
###############################################################################

### PUT /users/profile - Test validation errors
PUT {{baseUrl}}/users/profile
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "firstName": "",
  "lastName": "A",
  "phoneNumber": "invalid-phone",
  "country": "INVALID",
  "city": "",
  "preferredLanguage": "INVALID",
  "gender": "X",
  "profilePicture": "not-a-url"
}

# Expected Response (400 Bad Request):
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "VALIDATION_ERROR",
#     "message": "The FirstName field is required.",
#     "field": "firstName",
#     "details": {
#       "firstName": ["First name is required and must be 2-50 characters long"],
#       "lastName": ["Last name must be 2-50 characters long"],
#       "phoneNumber": ["Phone number must be in international format (e.g., +**********)"],
#       "country": ["Country must be a valid 2-letter ISO code"],
#       "preferredLanguage": ["Preferred language must be a valid 2-letter code"],
#       "gender": ["Gender must be M, F, or O"],
#       "profilePicture": ["Profile picture must be a valid URL"]
#     }
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

### PUT /users/change-password - Test password validation errors
PUT {{baseUrl}}/users/change-password
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "currentPassword": "",
  "newPassword": "123",
  "confirmPassword": "456"
}

# Expected Response (400 Bad Request):
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "VALIDATION_ERROR",
#     "message": "The CurrentPassword field is required.",
#     "field": "currentPassword",
#     "details": {
#       "currentPassword": ["Current password is required"],
#       "newPassword": ["Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character"],
#       "confirmPassword": ["Passwords do not match"]
#     }
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

###############################################################################
# ERROR RESPONSES
###############################################################################

# User Not Found Error (400 Bad Request):
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "USER_NOT_FOUND",
#     "message": "User not found",
#     "field": null,
#     "details": null
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

# Invalid Current Password Error (400 Bad Request):
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "INVALID_PASSWORD",
#     "message": "Current password is incorrect",
#     "field": "currentPassword",
#     "details": null
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

# Unauthorized Error (401 Unauthorized):
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "UNAUTHORIZED",
#     "message": "Authentication required",
#     "field": null,
#     "details": null
#   },
#   "meta": {
#     "timestamp": "2025-01-04T10:30:00Z",
#     "pagination": null
#   }
# }

###############################################################################
# VALIDATION RULES
###############################################################################

# Profile Update Validation:
# - First Name: 2-50 characters, letters and spaces only
# - Last Name: 2-50 characters, letters and spaces only
# - Phone Number: International format (+**********), 10-15 digits
# - Country: 2-letter ISO country code (US, UK, CA, etc.)
# - City: 2-100 characters, letters, spaces, hyphens, apostrophes
# - Preferred Language: 2-letter code (EN, AR, UR, FR, etc.)
# - Gender: Single character (M, F, O)
# - Profile Picture: Valid URL format

# Password Change Validation:
# - Current Password: Required, must match existing password
# - New Password: 8-100 characters, must contain:
#   * At least one uppercase letter (A-Z)
#   * At least one lowercase letter (a-z)
#   * At least one number (0-9)
#   * At least one special character (@$!%*?&)
# - Confirm Password: Must match new password exactly

# Preferences Validation:
# - Language: Valid 2-letter language code
# - Profile Visibility: "Public", "Private", or "Friends"
# - Theme: "Light", "Dark", or "Auto"
# - Default View: "Grid", "List", or "Card"
# - Default Sort By: "Newest", "Oldest", "Rating", "Popular", "Title"
# - Preferred Platforms: Array of valid platforms ["iOS", "Android", "Web", "Desktop"]

###############################################################################
# AUTHENTICATION
###############################################################################

# Bearer Token Authentication:
# Authorization: Bearer <your_jwt_token_here>

# Cookie Authentication:
# Cookies are automatically sent by the browser:
# - muslim_directory_access_token
# - muslim_directory_refresh_token

# Both authentication methods are supported simultaneously.
# The API will check for tokens in this order:
# 1. Authorization header (Bearer token)
# 2. Cookies (if no Authorization header)

###############################################################################
# TESTING NOTES
###############################################################################

# 1. Ensure you have a valid JWT token from authentication endpoints
# 2. Replace {{authToken}} with your actual JWT token
# 3. All endpoints require authentication (401 if not authenticated)
# 4. Test both full and partial updates for profile and preferences
# 5. Verify validation errors return consistent ApiResponse format
# 6. Test both Bearer token and cookie authentication methods
# 7. Check that user stats (reviews/favorites count) are calculated correctly
# 8. Verify password changes invalidate existing sessions 

###############################################################################
# COMMON ERROR CODES
###############################################################################

# USER_NOT_FOUND - User doesn't exist or is inactive
# VALIDATION_ERROR - Request validation failed
# INVALID_PASSWORD - Current password is incorrect
# UNAUTHORIZED - Authentication required
# FORBIDDEN - User doesn't have permission
# INTERNAL_ERROR - Server error occurred
# INVALID_USER_ID - Invalid user ID format
# PROFILE_UPDATE_FAILED - Profile update operation failed
# PASSWORD_CHANGE_FAILED - Password change operation failed
# PREFERENCES_UPDATE_FAILED - Preferences update operation failed

###############################################################################
# SECURITY FEATURES
###############################################################################

# 1. JWT Token Validation - All endpoints require valid authentication
# 2. User Context Validation - Users can only access their own data
# 3. Password Verification - Current password required for password changes
# 4. Input Validation - All inputs validated and sanitized
# 5. Secure Cookies - HttpOnly, Secure, SameSite protection
# 6. CORS Protection - Configured for specific origins with credentials
# 7. SQL Injection Prevention - Parameterized queries used throughout
# 8. XSS Prevention - Input validation and output encoding

###############################################################################
# RESPONSE STRUCTURE DETAILS
###############################################################################

# User Profile Response includes:
# - Basic user information (name, email, contact details)
# - Account status (email/phone verification, terms acceptance)
# - User statistics (reviews count, favorites count)
# - User's submitted listings array with full listing details:
#   * Listing basic info (title, description, logo)
#   * Platform and pricing information
#   * Islamic compliance status
#   * Rating and review statistics
#   * Organization details (if applicable)
#   * Primary category information
#   * View count and creation/update timestamps

# Listings Array Fields:
# - id: Unique identifier for the listing
# - title: Name of the app/service
# - shortDescription: Brief description
# - logoURL: URL to the listing's logo image
# - platformType: Type of platform (Mobile App, Website, etc.)
# - supportedPlatforms: Array of supported platforms
# - pricingModel: Free, Paid, Freemium, Subscription
# - price: Price amount (null for free apps)
# - currency: Currency code (USD, EUR, etc.)
# - islamicComplianceStatus: Approved, Under Review, Rejected
# - rating: Object containing average rating and count
# - organization: Organization details (if listing belongs to an org)
# - primaryCategory: Main category the listing belongs to
# - featuredLevel: Featured status level (0 = not featured)
# - viewCount: Number of times the listing has been viewed
# - createdAt/updatedAt: Timestamps for creation and last update

# The listings are ordered by creation date (newest first)
# Only listings submitted by the authenticated user are included
