### Search APIs Documentation
### Base URL: {{baseUrl}}/api/search

### Variables
@baseUrl = https://localhost:7001
@bearerToken = your_jwt_token_here

### ========================================
### PUBLIC ENDPOINTS (No Authentication Required)
### ========================================

### 1. Global Search
GET {{baseUrl}}/api/search?query=mosque&type=listing&category=Mosque&location=New York&latitude=40.7128&longitude=-74.0060&radius=10&page=1&limit=20&sortBy=rating
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "type": "listing",
#         "id": "550e8400-e29b-41d4-a716-446655440000",
#         "title": "Al-Noor Islamic Center",
#         "description": "Beautiful mosque with community programs and Islamic education.",
#         "imageUrl": "https://example.com/mosque.jpg",
#         "category": "Mosque",
#         "location": "New York, NY",
#         "rating": 4.5,
#         "reviewCount": 25,
#         "isVerified": true,
#         "isFeatured": true,
#         "distance": 2.3,
#         "additionalData": {}
#       },
#       {
#         "type": "organization",
#         "id": "550e8400-e29b-41d4-a716-446655440001",
#         "title": "Islamic Society of New York",
#         "description": "Community organization serving Muslims in New York area.",
#         "imageUrl": "https://example.com/org-logo.jpg",
#         "category": null,
#         "location": "New York, NY",
#         "rating": null,
#         "reviewCount": null,
#         "isVerified": true,
#         "isFeatured": false,
#         "distance": 1.8,
#         "additionalData": {
#           "listingCount": 5
#         }
#       }
#     ],
#     "totalCount": 45,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 3
#   },
#   "message": null,
#   "error": null
# }

### 2. Get Search Suggestions
GET {{baseUrl}}/api/search/suggestions?query=mos&limit=10
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": [
#     {
#       "text": "Al-Noor Mosque",
#       "type": "listing",
#       "count": 25,
#       "id": "550e8400-e29b-41d4-a716-446655440000"
#     },
#     {
#       "text": "Mosque",
#       "type": "category",
#       "count": 150,
#       "id": "550e8400-e29b-41d4-a716-446655440002"
#     },
#     {
#       "text": "mosque near me",
#       "type": "query",
#       "count": 45,
#       "id": null
#     }
#   ],
#   "message": null,
#   "error": null
# }

### 3. Get Search Statistics
GET {{baseUrl}}/api/search/stats?query=halal restaurant&category=Restaurant&location=Chicago
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "totalResults": 85,
#     "listingResults": 75,
#     "organizationResults": 8,
#     "categoryResults": 2,
#     "categoryBreakdown": {
#       "Restaurant": 75,
#       "Grocery Store": 10
#     },
#     "locationBreakdown": {
#       "Chicago": 60,
#       "Suburbs": 25
#     },
#     "searchDuration": "00:00:00.1250000"
#   },
#   "message": null,
#   "error": null
# }

### 4. Get Popular Searches
GET {{baseUrl}}/api/search/popular?limit=10
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": [
#     {
#       "query": "halal restaurant",
#       "searchCount": 245,
#       "category": "Restaurant",
#       "lastSearched": "2024-01-15T14:30:00Z"
#     },
#     {
#       "query": "mosque near me",
#       "searchCount": 189,
#       "category": "Mosque",
#       "lastSearched": "2024-01-15T13:45:00Z"
#     }
#   ],
#   "message": null,
#   "error": null
# }

### 5. Get Search Filters
GET {{baseUrl}}/api/search/filters
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "categories": [
#       "Mosque",
#       "Restaurant",
#       "Grocery Store",
#       "Islamic School",
#       "Community Center"
#     ],
#     "types": [
#       "listing",
#       "organization",
#       "category"
#     ],
#     "locations": [
#       "New York",
#       "Chicago",
#       "Los Angeles",
#       "Houston",
#       "Philadelphia"
#     ],
#     "minRating": 1,
#     "maxDistance": 50,
#     "isVerified": null,
#     "isFeatured": null,
#     "isOpen": null
#   },
#   "message": null,
#   "error": null
# }

### ========================================
### AUTHENTICATED ENDPOINTS (Bearer Token Required)
### ========================================

### 6. Get Recent Searches
GET {{baseUrl}}/api/search/recent?page=1&limit=20
Authorization: Bearer {{bearerToken}}
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "items": [
#       {
#         "id": "550e8400-e29b-41d4-a716-446655440003",
#         "query": "halal restaurant",
#         "type": "listing",
#         "category": "Restaurant",
#         "location": "New York",
#         "searchedAt": "2024-01-15T14:30:00Z"
#       },
#       {
#         "id": "550e8400-e29b-41d4-a716-446655440004",
#         "query": "mosque",
#         "type": null,
#         "category": "Mosque",
#         "location": null,
#         "searchedAt": "2024-01-15T13:45:00Z"
#       }
#     ],
#     "totalCount": 15,
#     "page": 1,
#     "pageSize": 20,
#     "totalPages": 1
#   },
#   "message": null,
#   "error": null
# }

### 7. Clear Recent Searches
DELETE {{baseUrl}}/api/search/recent
Authorization: Bearer {{bearerToken}}

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": "Recent searches cleared successfully",
#   "error": null
# }

### ========================================
### SEARCH EXAMPLES
### ========================================

### Search by Category
GET {{baseUrl}}/api/search?query=restaurant&category=Restaurant&sortBy=rating
Accept: application/json

### Search by Location
GET {{baseUrl}}/api/search?query=mosque&location=Chicago&sortBy=distance
Accept: application/json

### Search with Geolocation
GET {{baseUrl}}/api/search?query=halal&latitude=40.7128&longitude=-74.0060&radius=5&sortBy=distance
Accept: application/json

### Search Organizations Only
GET {{baseUrl}}/api/search?query=islamic society&type=organization
Accept: application/json

### Search Categories Only
GET {{baseUrl}}/api/search?query=education&type=category
Accept: application/json

### Advanced Search with Multiple Filters
GET {{baseUrl}}/api/search?query=community center&category=Community Center&location=Los Angeles&sortBy=verified&page=1&limit=10
Accept: application/json

### ========================================
### ERROR RESPONSES
### ========================================

### Validation Error Example:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "INVALID_QUERY",
#     "message": "Search query is required"
#   }
# }

### Authentication Error Example:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "UNAUTHORIZED",
#     "message": "User not authenticated"
#   }
# }

### Search Failed Error Example:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "SEARCH_FAILED",
#     "message": "Search operation failed",
#     "details": "Database connection timeout"
#   }
# }
