### Utilities APIs Documentation
### Base URL: {{baseUrl}}/api/utilities

### Variables
@baseUrl = https://localhost:5166
@authToken = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### ========================================
### FILE UPLOAD ENDPOINTS
### ========================================

### 1. Upload Single File
POST {{baseUrl}}/api/utilities/upload
Authorization: {{authToken}}
Content-Type: multipart/form-data

--boundary123
Content-Disposition: form-data; name="file"; filename="example.jpg"
Content-Type: image/jpeg

< ./path/to/your/file.jpg
--boundary123
Content-Disposition: form-data; name="folder"

profile-pictures
--boundary123
Content-Disposition: form-data; name="customFileName"

my-profile-pic.jpg
--boundary123
Content-Disposition: form-data; name="makePublic"

true
--boundary123--

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "fileName": "my-profile-pic_550e8400-e29b-41d4-a716-446655440000.jpg",
#     "publicUrl": "https://glorious-yellow-wolverine.myfilebase.com/ipfs/QmevD6SJsQfSUm95aRv9Heftp4sgFsekUTgC8HXRvRbezi",
#     "key": "profile-pictures/my-profile-pic_550e8400-e29b-41d4-a716-446655440000.jpg",
#     "fileSize": 1024000,
#     "contentType": "image/jpeg",
#     "uploadedAt": "2024-01-15T14:30:00Z",
#     "folder": "profile-pictures"
#   },
#   "message": "File uploaded successfully",
#   "provider": null,
#   "error": null,
#   "meta": {
#     "timestamp": "2024-01-15T14:30:00Z",
#     "pagination": null
#   }
# }

### 2. Upload Multiple Files
POST {{baseUrl}}/api/utilities/upload-multiple
Authorization: {{authToken}}
Content-Type: multipart/form-data

--boundary123
Content-Disposition: form-data; name="files"; filename="file1.jpg"
Content-Type: image/jpeg

< ./path/to/file1.jpg
--boundary123
Content-Disposition: form-data; name="files"; filename="file2.png"
Content-Type: image/png

< ./path/to/file2.png
--boundary123
Content-Disposition: form-data; name="folder"

gallery
--boundary123
Content-Disposition: form-data; name="makePublic"

true
--boundary123--

### Sample Response:
# {
#   "success": true,
#   "data": [
#     {
#       "fileName": "file1_550e8400-e29b-41d4-a716-446655440001.jpg",
#       "publicUrl": "https://glorious-yellow-wolverine.myfilebase.com/ipfs/QmevD6SJsQfSUm95aRv9Heftp4sgFsekUTgC8HXRvRbezi",
#       "key": "gallery/file1_550e8400-e29b-41d4-a716-446655440001.jpg",
#       "fileSize": 1024000,
#       "contentType": "image/jpeg",
#       "uploadedAt": "2024-01-15T14:30:00Z",
#       "folder": "gallery"
#     },
#     {
#       "fileName": "file2_550e8400-e29b-41d4-a716-446655440002.png",
#       "publicUrl": "https://glorious-yellow-wolverine.myfilebase.com/ipfs/QmXYZ123anotherHashExample456",
#       "key": "gallery/file2_550e8400-e29b-41d4-a716-446655440002.png",
#       "fileSize": 2048000,
#       "contentType": "image/png",
#       "uploadedAt": "2024-01-15T14:30:00Z",
#       "folder": "gallery"
#     }
#   ],
#   "message": "All 2 files uploaded successfully"
# }

### 3. Delete File
DELETE {{baseUrl}}/api/utilities/delete
Authorization: {{authToken}}
Content-Type: application/json

{
  "key": "profile-pictures/my-profile-pic_550e8400-e29b-41d4-a716-446655440000.jpg"
}

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "success": true,
#     "message": "File deleted successfully",
#     "key": "profile-pictures/my-profile-pic_550e8400-e29b-41d4-a716-446655440000.jpg"
#   },
#   "message": null,
#   "error": null
# }

### 4. Get File URL (Public)
GET {{baseUrl}}/api/utilities/url/profile-pictures/my-profile-pic_550e8400-e29b-41d4-a716-446655440000.jpg
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": "https://your-bucket.s3.filebase.com/profile-pictures/my-profile-pic_550e8400-e29b-41d4-a716-446655440000.jpg",
#   "message": null,
#   "error": null
# }

### 5. Check File Exists
GET {{baseUrl}}/api/utilities/exists/profile-pictures/my-profile-pic_550e8400-e29b-41d4-a716-446655440000.jpg
Authorization: {{authToken}}
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": true,
#   "message": null,
#   "error": null
# }

### 6. Get Upload Information (Public)
GET {{baseUrl}}/api/utilities/upload-info
Accept: application/json

### Sample Response:
# {
#   "success": true,
#   "data": {
#     "maxFileSize": "50MB",
#     "maxFilesPerUpload": 10,
#     "allowedFileTypes": [
#       "Images: JPEG, JPG, PNG, GIF, WebP",
#       "Documents: PDF, TXT, DOC, DOCX, XLS, XLSX"
#     ],
#     "supportedFormats": [
#       "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
#       "application/pdf", "text/plain", "application/msword",
#       "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
#       "application/vnd.ms-excel",
#       "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
#     ],
#     "guidelines": [
#       "Files are automatically renamed with unique identifiers",
#       "Files are stored securely in FileBase S3-compatible storage",
#       "Public files are accessible via direct URL",
#       "Authentication required for upload and delete operations"
#     ]
#   }
# }

### ========================================
### EXAMPLE USAGE SCENARIOS
### ========================================

### Upload Profile Picture
POST {{baseUrl}}/api/utilities/upload
Authorization: {{authToken}}
Content-Type: multipart/form-data

# Form data:
# file: [select your image file]
# folder: "profiles"
# makePublic: true

### Upload Document
POST {{baseUrl}}/api/utilities/upload
Authorization: {{authToken}}
Content-Type: multipart/form-data

# Form data:
# file: [select your PDF file]
# folder: "documents"
# customFileName: "my-resume.pdf"
# makePublic: false

### Upload Gallery Images
POST {{baseUrl}}/api/utilities/upload-multiple
Authorization: {{authToken}}
Content-Type: multipart/form-data

# Form data:
# files: [select multiple image files]
# folder: "gallery"
# makePublic: true

### ========================================
### ERROR RESPONSES
### ========================================

### File Too Large:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "FILE_TOO_LARGE",
#     "message": "File size cannot exceed 50MB"
#   }
# }

### Invalid File Type:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "INVALID_FILE_TYPE",
#     "message": "File type not allowed. Supported types: images, PDF, text, Word, Excel"
#   }
# }

### Unauthorized:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "UNAUTHORIZED",
#     "message": "Authentication required"
#   }
# }

### File Not Found:
# {
#   "success": false,
#   "data": null,
#   "message": null,
#   "error": {
#     "code": "S3_ERROR",
#     "message": "FileBase error: The specified key does not exist"
#   }
# }

### ========================================
### CONFIGURATION REQUIREMENTS
### ========================================

### Required appsettings.json configuration:
# {
#   "FileBase": {
#     "BucketName": "your-bucket-name",
#     "BaseUrl": "https://your-bucket.s3.filebase.com",
#     "IpfsGatewayUrl": "https://glorious-yellow-wolverine.myfilebase.com/ipfs",
#     "AccessKey": "your-filebase-access-key",
#     "SecretKey": "your-filebase-secret-key",
#     "Region": "us-east-1"
#   }
# }

### Configuration Notes:
# - All FileBase credentials are now stored in appsettings.json
# - No environment variables required
# - Region defaults to "us-east-1" if not specified
# - ServiceURL is automatically set to "https://s3.filebase.com"
