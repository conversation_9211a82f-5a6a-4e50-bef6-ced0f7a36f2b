<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="4.0.2" />
        <PackageReference Include="AWSSDK.S3" Version="4.0.4.2" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
        <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.1" />
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="XGENO.DBHelpers">
        <HintPath>..\XGENO.DBHelpers.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project>
