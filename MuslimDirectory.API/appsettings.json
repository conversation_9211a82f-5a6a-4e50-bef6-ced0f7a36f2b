{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=p1415.use1.mysecurecloudhost.com;Database=salaamprojects_muslim_directory_dev_db;Uid=naweedco_muslim_directory_dev_db_user;Password=****************;TrustServerCertificate=True;"}, "JwtSettings": {"SecretKey": "Muslim_Directory_Super!Secret!Key%MuslimDirectoryAimsToServeTheMuslimCommunity%", "Issuer": "MuslimDirectory", "Audience": "MuslimDirectoryUsers", "ExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7}, "EmailSettings": {"ServiceType": "SMTP", "ApiKey": "re_<PERSON><PERSON><PERSON><PERSON>", "ApiBaseUrl": "https://api.resend.com", "SenderEmail": "<EMAIL>", "SenderName": "Muslim Directory", "SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "fcet gdlw ynfi twuf"}, "CookieSettings": {"AccessTokenCookieName": "muslim_directory_access_token", "RefreshTokenCookieName": "muslim_directory_refresh_token", "AccessTokenExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7, "SecureOnly": false, "HttpOnly": true, "SameSite": "Lax", "Domain": "", "Path": "/"}, "FileBase": {"BucketName": "muslim-directory-files", "BaseUrl": "https://muslim-directory-files.s3.filebase.com", "IpfsGatewayUrl": "https://glorious-yellow-wolverine.myfilebase.com/ipfs", "AccessKey": "9A8812ECEF4D9E764A8D", "SecretKey": "9tDVNCWPLCgbfHZVG9pxwCMCATaViEAGpQsTpNyt", "Region": "us-east-1"}}