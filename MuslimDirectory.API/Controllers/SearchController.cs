using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Search;
using MuslimDirectory.API.Models.Common;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/search")]
public class SearchController(ISearchService searchService) : ControllerBase
{
    private readonly ISearchService _searchService = searchService;

    private Guid GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private string? GetSessionId()
    {
        return HttpContext.Session?.Id ?? Request.Headers["X-Session-ID"].FirstOrDefault();
    }

    /// <summary>
    /// Global search across listings, organizations, and categories
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PaginatedResult<SearchResultDto>>>> Search(
        [FromQuery] string query,
        [FromQuery] string? type = null,
        [FromQuery] string? category = null,
        [FromQuery] string? location = null,
        [FromQuery] decimal? latitude = null,
        [FromQuery] decimal? longitude = null,
        [FromQuery] int? radius = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20,
        [FromQuery] string? sortBy = null)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return BadRequest(ApiResponse<PaginatedResult<SearchResultDto>>.ErrorResponse(
                "INVALID_QUERY", "Search query is required"));
        }

        if (page < 1) page = 1;
        if (limit < 1 || limit > 100) limit = 20;

        var searchDto = new SearchDto
        {
            Query = query.Trim(),
            Type = type,
            Category = category,
            Location = location,
            Latitude = latitude,
            Longitude = longitude,
            Radius = radius,
            Page = page,
            Limit = limit,
            SortBy = sortBy
        };

        var userId = User.Identity?.IsAuthenticated == true ? GetUserId() : (Guid?)null;
        var sessionId = GetSessionId();

        var result = await _searchService.SearchAsync(searchDto, userId, sessionId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get search suggestions based on partial query
    /// </summary>
    [HttpGet("suggestions")]
    public async Task<ActionResult<ApiResponse<List<SearchSuggestionDto>>>> GetSearchSuggestions(
        [FromQuery] string query,
        [FromQuery] int limit = 10)
    {
        if (string.IsNullOrWhiteSpace(query) || query.Length < 2)
        {
            return BadRequest(ApiResponse<List<SearchSuggestionDto>>.ErrorResponse(
                "INVALID_QUERY", "Query must be at least 2 characters long"));
        }

        if (limit < 1 || limit > 50) limit = 10;

        var result = await _searchService.GetSearchSuggestionsAsync(query.Trim(), limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get user's recent searches
    /// </summary>
    [HttpGet("recent")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<PaginatedResult<RecentSearchDto>>>> GetRecentSearches(
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        if (page < 1) page = 1;
        if (limit < 1 || limit > 100) limit = 20;

        var userId = GetUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<PaginatedResult<RecentSearchDto>>.ErrorResponse(
                "UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _searchService.GetRecentSearchesAsync(userId, page, limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Clear user's recent searches
    /// </summary>
    [HttpDelete("recent")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> ClearRecentSearches()
    {
        var userId = GetUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse(
                "UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _searchService.ClearRecentSearchesAsync(userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get search statistics for a query
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<ApiResponse<SearchStatsDto>>> GetSearchStats(
        [FromQuery] string query,
        [FromQuery] string? type = null,
        [FromQuery] string? category = null,
        [FromQuery] string? location = null,
        [FromQuery] decimal? latitude = null,
        [FromQuery] decimal? longitude = null,
        [FromQuery] int? radius = null)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return BadRequest(ApiResponse<SearchStatsDto>.ErrorResponse(
                "INVALID_QUERY", "Search query is required"));
        }

        var searchDto = new SearchDto
        {
            Query = query.Trim(),
            Type = type,
            Category = category,
            Location = location,
            Latitude = latitude,
            Longitude = longitude,
            Radius = radius,
            Page = 1,
            Limit = 1
        };

        var result = await _searchService.GetSearchStatsAsync(searchDto);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get popular searches
    /// </summary>
    [HttpGet("popular")]
    public async Task<ActionResult<ApiResponse<List<PopularSearchDto>>>> GetPopularSearches(
        [FromQuery] int limit = 10)
    {
        if (limit < 1 || limit > 50) limit = 10;

        var result = await _searchService.GetPopularSearchesAsync(limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get available search filters
    /// </summary>
    [HttpGet("filters")]
    public async Task<ActionResult<ApiResponse<SearchFiltersDto>>> GetSearchFilters()
    {
        var result = await _searchService.GetSearchFiltersAsync();
        return result.Success ? Ok(result) : BadRequest(result);
    }
}
