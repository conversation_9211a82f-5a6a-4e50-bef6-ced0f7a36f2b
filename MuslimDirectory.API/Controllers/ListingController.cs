using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MuslimDirectory.API.Models.DTOs.Listing;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Common;
using MuslimDirectory.API.Services.Interfaces;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/listing")]
public class ListingController : ControllerBase
{
    private readonly IListingService _listingService;

    public ListingController(IListingService listingService)
    {
        _listingService = listingService;
    }

    /// <summary>
    /// Get listings with filtering and pagination (public endpoint)
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PaginatedResult<ListingDto>>>> GetListings(
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20,
        [FromQuery] string? search = null,
        [FromQuery] string? category = null,
        [FromQuery] string? platform = null,
        [FromQuery] string? language = null,
        [FromQuery] string? pricingModel = null,
        [FromQuery] string? complianceStatus = null,
        [FromQuery] bool? featured = null,
        [FromQuery] string? sortBy = null,
        [FromQuery] int? minRating = null)
    {
        var result = await _listingService.GetListingsAsync(
            page, limit, search, category, platform, language, 
            pricingModel, complianceStatus, featured, sortBy, minRating);

        return Ok(result);
    }

    /// <summary>
    /// Get featured listings for homepage
    /// </summary>
    [HttpGet("featured")]
    public async Task<ActionResult<ApiResponse<List<FeaturedListingDto>>>> GetFeaturedListings(
        [FromQuery] int limit = 6)
    {
        var result = await _listingService.GetFeaturedListingsAsync(limit);
        return Ok(result);
    }

    /// <summary>
    /// Get recently added listings
    /// </summary>
    [HttpGet("new-releases")]
    public async Task<ActionResult<ApiResponse<List<ListingDto>>>> GetNewReleases(
        [FromQuery] int limit = 10,
        [FromQuery] int days = 30)
    {
        var result = await _listingService.GetNewReleasesAsync(limit, days);
        return Ok(result);
    }

    /// <summary>
    /// Get trending listings
    /// </summary>
    [HttpGet("trending")]
    public async Task<ActionResult<ApiResponse<List<ListingDto>>>> GetTrendingListings(
        [FromQuery] string timeframe = "week",
        [FromQuery] int limit = 10)
    {
        var result = await _listingService.GetTrendingListingsAsync(timeframe, limit);
        return Ok(result);
    }

    /// <summary>
    /// Get personalized recommendations (authenticated users)
    /// </summary>
    [HttpGet("recommendations")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<RecommendationsDto>>> GetRecommendations()
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<RecommendationsDto>.ErrorResponse("User not authenticated", "UNAUTHORIZED"));
        }

        var result = await _listingService.GetRecommendationsAsync(userId);
        return Ok(result);
    }

    /// <summary>
    /// Get detailed listing information
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<ListingDetailDto>>> GetListingById(Guid id)
    {
        var userId = GetCurrentUserIdOptional();
        var result = await _listingService.GetListingByIdAsync(id, userId);
        return Ok(result);
    }

    /// <summary>
    /// Submit new listing
    /// </summary>
    [HttpPost]
    [Authorize]
    public async Task<ActionResult<ApiResponse<ListingDto>>> CreateListing([FromBody] CreateListingDto createDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ListingDto>.ErrorResponse("User not authenticated", "UNAUTHORIZED"));
        }

        var result = await _listingService.CreateListingAsync(createDto, userId);
        
        if (result.Success)
        {
            return CreatedAtAction(nameof(GetListingById), new { id = result.Data!.Id }, result);
        }

        return BadRequest(result);
    }

    /// <summary>
    /// Update listing (organization members only)
    /// </summary>
    [HttpPut("{id}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<ListingDto>>> UpdateListing(Guid id, [FromBody] UpdateListingDto updateDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ListingDto>.ErrorResponse("User not authenticated", "UNAUTHORIZED"));
        }

        var result = await _listingService.UpdateListingAsync(id, updateDto, userId);
        return Ok(result);
    }

    /// <summary>
    /// Delete listing (organization owners only)
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteListing(Guid id)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse("User not authenticated", "UNAUTHORIZED"));
        }

        var result = await _listingService.DeleteListingAsync(id, userId);
        return Ok(result);
    }

    /// <summary>
    /// Track listing view (for analytics)
    /// </summary>
    [HttpPost("{id}/view")]
    public async Task<ActionResult<ApiResponse<bool>>> TrackView(Guid id, [FromBody] TrackViewDto trackDto)
    {
        var result = await _listingService.TrackViewAsync(id, trackDto);
        return Ok(result);
    }

    /// <summary>
    /// Get listing analytics (organization members only)
    /// </summary>
    [HttpGet("{id}/analytics")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<ListingAnalyticsDto>>> GetListingAnalytics(
        Guid id,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string granularity = "day")
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ListingAnalyticsDto>.ErrorResponse("User not authenticated", "UNAUTHORIZED"));
        }

        var result = await _listingService.GetListingAnalyticsAsync(id, userId, startDate, endDate, granularity);
        return Ok(result);
    }

    /// <summary>
    /// Add listing to favorites
    /// </summary>
    [HttpPost("{id}/favorite")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> AddToFavorites(Guid id)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse("User not authenticated", "UNAUTHORIZED"));
        }

        var result = await _listingService.AddToFavoritesAsync(id, userId);
        return Ok(result);
    }

    /// <summary>
    /// Remove listing from favorites
    /// </summary>
    [HttpDelete("{id}/favorite")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> RemoveFromFavorites(Guid id)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse("User not authenticated", "UNAUTHORIZED"));
        }

        var result = await _listingService.RemoveFromFavoritesAsync(id, userId);
        return Ok(result);
    }

    /// <summary>
    /// Check if listing is favorited by user
    /// </summary>
    [HttpGet("{id}/favorite")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> CheckIsFavorited(Guid id)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse("User not authenticated", "UNAUTHORIZED"));
        }

        var result = await _listingService.CheckIsFavoritedAsync(id, userId);
        return Ok(result);
    }

    /// <summary>
    /// Get user's favorite listings
    /// </summary>
    [HttpGet("favorites")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<PaginatedResult<ListingDto>>>> GetUserFavorites(
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20,
        [FromQuery] string? sortBy = null,
        [FromQuery] string? search = null)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<PaginatedResult<ListingDto>>.ErrorResponse("User not authenticated", "UNAUTHORIZED"));
        }

        var result = await _listingService.GetUserFavoritesAsync(userId, page, limit, sortBy, search);
        return Ok(result);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private Guid? GetCurrentUserIdOptional()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }
}
