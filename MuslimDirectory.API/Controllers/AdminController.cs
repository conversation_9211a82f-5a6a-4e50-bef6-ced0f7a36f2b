using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Admin;
using MuslimDirectory.API.Models.Common;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/admin")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminController(IAdminService adminService) : ControllerBase
{
    private readonly IAdminService _adminService = adminService;

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    /// <summary>
    /// Get admin dashboard with statistics and overview
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<ApiResponse<AdminDashboardDto>>> GetDashboard()
    {
        var result = await _adminService.GetDashboardAsync();
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get moderation queue with filtering options
    /// </summary>
    [HttpGet("moderation-queue")]
    public async Task<ActionResult<ApiResponse<PaginatedResult<AdminModerationItemDto>>>> GetModerationQueue(
        [FromQuery] string? type = null,
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        if (limit > 100) limit = 100; // Prevent excessive data retrieval

        var result = await _adminService.GetModerationQueueAsync(type, status, priority, page, limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get users with filtering and search options
    /// </summary>
    [HttpGet("users")]
    public async Task<ActionResult<ApiResponse<PaginatedResult<AdminUserDto>>>> GetUsers(
        [FromQuery] string? search = null,
        [FromQuery] string? status = null,
        [FromQuery] string? role = null,
        [FromQuery] DateTime? createdFrom = null,
        [FromQuery] DateTime? createdTo = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        if (limit > 100) limit = 100; // Prevent excessive data retrieval

        var result = await _adminService.GetUsersAsync(search, status, role, createdFrom, createdTo, page, limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Update user status (activate, suspend, ban, etc.)
    /// </summary>
    [HttpPut("users/{id}/status")]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateUserStatus(
        Guid id, 
        [FromBody] UpdateUserStatusDto updateDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_USER_ID", "User ID is required"));
        }

        var adminUserId = GetCurrentUserId();
        if (adminUserId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse(
                "UNAUTHORIZED", "Unable to identify admin user"));
        }

        var result = await _adminService.UpdateUserStatusAsync(id, updateDto, adminUserId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get listings with filtering and search options
    /// </summary>
    [HttpGet("listings")]
    public async Task<ActionResult<ApiResponse<PaginatedResult<AdminListingDto>>>> GetListings(
        [FromQuery] string? search = null,
        [FromQuery] string? status = null,
        [FromQuery] string? category = null,
        [FromQuery] bool? isVerified = null,
        [FromQuery] bool? isFeatured = null,
        [FromQuery] DateTime? createdFrom = null,
        [FromQuery] DateTime? createdTo = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        if (limit > 100) limit = 100; // Prevent excessive data retrieval

        var result = await _adminService.GetListingsAsync(search, status, category, isVerified, isFeatured, 
            createdFrom, createdTo, page, limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Update listing status (approve, reject, verify, feature, etc.)
    /// </summary>
    [HttpPut("listings/{id}/status")]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateListingStatus(
        Guid id, 
        [FromBody] UpdateListingStatusDto updateDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_LISTING_ID", "Listing ID is required"));
        }

        var adminUserId = GetCurrentUserId();
        if (adminUserId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse(
                "UNAUTHORIZED", "Unable to identify admin user"));
        }

        var result = await _adminService.UpdateListingStatusAsync(id, updateDto, adminUserId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get reviews with filtering and search options
    /// </summary>
    [HttpGet("reviews")]
    public async Task<ActionResult<ApiResponse<PaginatedResult<AdminReviewDto>>>> GetReviews(
        [FromQuery] string? search = null,
        [FromQuery] string? status = null,
        [FromQuery] DateTime? createdFrom = null,
        [FromQuery] DateTime? createdTo = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        if (limit > 100) limit = 100; // Prevent excessive data retrieval

        var result = await _adminService.GetReviewsAsync(search, status, createdFrom, createdTo, page, limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Update review status (approve, reject, etc.)
    /// </summary>
    [HttpPut("reviews/{id}/status")]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateReviewStatus(
        Guid id, 
        [FromBody] UpdateReviewStatusDto updateDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_REVIEW_ID", "Review ID is required"));
        }

        var adminUserId = GetCurrentUserId();
        if (adminUserId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse(
                "UNAUTHORIZED", "Unable to identify admin user"));
        }

        var result = await _adminService.UpdateReviewStatusAsync(id, updateDto, adminUserId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get reports with filtering options
    /// </summary>
    [HttpGet("reports")]
    public async Task<ActionResult<ApiResponse<PaginatedResult<AdminReportDto>>>> GetReports(
        [FromQuery] string? type = null,
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] DateTime? createdFrom = null,
        [FromQuery] DateTime? createdTo = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        if (limit > 100) limit = 100; // Prevent excessive data retrieval

        var result = await _adminService.GetReportsAsync(type, status, priority, createdFrom, createdTo, page, limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Update report status (resolve, dismiss, investigate, etc.)
    /// </summary>
    [HttpPut("reports/{id}/status")]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateReportStatus(
        Guid id, 
        [FromBody] UpdateReportStatusDto updateDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_REPORT_ID", "Report ID is required"));
        }

        var adminUserId = GetCurrentUserId();
        if (adminUserId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse(
                "UNAUTHORIZED", "Unable to identify admin user"));
        }

        var result = await _adminService.UpdateReportStatusAsync(id, updateDto, adminUserId);
        return result.Success ? Ok(result) : BadRequest(result);
    }
}
