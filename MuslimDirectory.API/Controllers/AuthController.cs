namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/auth")]
public class AuthController(IAuthService authService, ICookieService cookieService) : ControllerBase
{
    /// <summary>
    /// Register a new user account
    /// </summary>
    /// <param name="request">User registration details</param>
    /// <returns>Authentication response with user details and tokens</returns>
    [HttpPost("signup")]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> Signup([FromBody] SignupRequest request)
    {
        var result = await authService.SignupAsync(request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        // Set secure cookies for authentication
        if (result.Data != null && !string.IsNullOrEmpty(result.Data.Token) && !string.IsNullOrEmpty(result.Data.RefreshToken))
        {
            cookieService.SetAuthCookies(Response, result.Data.Token, result.Data.RefreshToken);
        }

        return Ok(result);
    }

    /// <summary>
    /// Authenticate user and return access token
    /// </summary>
    /// <param name="request">User login credentials</param>
    /// <returns>Authentication response with user details and tokens</returns>
    [HttpPost("signin")]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> Signin([FromBody] SigninRequest request)
    {
        var result = await authService.SigninAsync(request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        // Set secure cookies for authentication
        if (result.Data != null && !string.IsNullOrEmpty(result.Data.Token) && !string.IsNullOrEmpty(result.Data.RefreshToken))
        {
            cookieService.SetAuthCookies(Response, result.Data.Token, result.Data.RefreshToken);
        }

        return Ok(result);
    }

    /// <summary>
    /// Authenticate using social providers (Google, Apple)
    /// </summary>
    /// <param name="request">Social login details</param>
    /// <returns>Authentication response with user details and tokens</returns>
    [HttpPost("social-login")]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> SocialLogin([FromBody] SocialLoginRequest request)
    {
        var result = await authService.SocialLoginAsync(request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        // Set secure cookies for authentication
        if (result.Data != null && !string.IsNullOrEmpty(result.Data.Token) && !string.IsNullOrEmpty(result.Data.RefreshToken))
        {
            cookieService.SetAuthCookies(Response, result.Data.Token, result.Data.RefreshToken);
        }

        return Ok(result);
    }

    /// <summary>
    /// Request password reset
    /// </summary>
    /// <param name="request">Email for password reset</param>
    /// <returns>Success message</returns>
    [HttpPost("forgot-password")]
    public async Task<ActionResult<ApiResponse<string>>> ForgotPassword([FromBody] ForgotPasswordRequest request)
    {
        var result = await authService.ForgotPasswordAsync(request);
        return Ok(result);
    }

    /// <summary>
    /// Reset password using token
    /// </summary>
    /// <param name="request">Reset token and new password</param>
    /// <returns>Success message</returns>
    [HttpPost("reset-password")]
    public async Task<ActionResult<ApiResponse<string>>> ResetPassword([FromBody] ResetPasswordRequest request)
    {
        var result = await authService.ResetPasswordAsync(request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Verify email address
    /// </summary>
    /// <param name="request">Email verification token</param>
    /// <returns>Success message</returns>
    [HttpPost("verify-email")]
    public async Task<ActionResult<ApiResponse<string>>> VerifyEmail([FromBody] VerifyEmailRequest request)
    {
        var result = await authService.VerifyEmailAsync(request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Resend email verification
    /// </summary>
    /// <param name="request">Email for verification resend</param>
    /// <returns>Success message</returns>
    [HttpPost("resend-verification")]
    public async Task<ActionResult<ApiResponse<string>>> ResendVerification([FromBody] ResendVerificationRequest request)
    {
        var result = await authService.ResendVerificationAsync(request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Refresh access token
    /// </summary>
    /// <param name="request">Current access token</param>
    /// <returns>New authentication response with refreshed tokens</returns>
    [HttpPost("refresh-token")]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        // If no token in request body, try to get from cookie
        if (string.IsNullOrEmpty(request.AccessToken))
        {
            request.AccessToken = cookieService.GetAccessTokenFromCookie(Request) ?? string.Empty;
        }

        var result = await authService.RefreshTokenAsync(request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        // Set new secure cookies for refreshed tokens
        if (result.Data != null && !string.IsNullOrEmpty(result.Data.Token) && !string.IsNullOrEmpty(result.Data.RefreshToken))
        {
            cookieService.SetAuthCookies(Response, result.Data.Token, result.Data.RefreshToken);
        }

        return Ok(result);
    }

    /// <summary>
    /// Logout user and invalidate tokens
    /// </summary>
    /// <returns>Success message</returns>
    [HttpPost("signout")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<string>>> Logout()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        if (string.IsNullOrEmpty(userId))
        {
            return BadRequest(ApiResponse<string>.ErrorResponse("INVALID_USER", "Invalid user"));
        }

        var result = await authService.LogoutAsync(userId);

        // Clear secure cookies on logout
        cookieService.ClearAuthCookies(Response);

        return Ok(result);
    }
}
