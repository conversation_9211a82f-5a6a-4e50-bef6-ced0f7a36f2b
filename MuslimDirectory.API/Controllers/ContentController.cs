using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Content;
using MuslimDirectory.API.Models.Common;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/content")]
public class ContentController(IContentService contentService) : ControllerBase
{
    private readonly IContentService _contentService = contentService;

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    #region Content Pages

    /// <summary>
    /// Get content pages with search and filtering
    /// </summary>
    [HttpGet("pages")]
    public async Task<ActionResult<ApiResponse<PaginatedResult<ContentPageDto>>>> GetContentPages(
        [FromQuery] string? search = null,
        [FromQuery] bool? isPublished = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        if (limit > 100) limit = 100; // Prevent excessive data retrieval

        var result = await _contentService.GetContentPagesAsync(search, isPublished, page, limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get content page by slug (public endpoint)
    /// </summary>
    [HttpGet("pages/{slug}")]
    public async Task<ActionResult<ApiResponse<ContentPageDto>>> GetContentPageBySlug(string slug)
    {
        if (string.IsNullOrEmpty(slug))
        {
            return BadRequest(ApiResponse<ContentPageDto>.ErrorResponse(
                "INVALID_SLUG", "Page slug is required"));
        }

        var result = await _contentService.GetContentPageBySlugAsync(slug);
        return result.Success ? Ok(result) : NotFound(result);
    }

    /// <summary>
    /// Create new content page (Admin only)
    /// </summary>
    [HttpPost("pages")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<ContentPageDto>>> CreateContentPage(
        [FromBody] CreateContentPageDto createDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ContentPageDto>.ErrorResponse(
                "UNAUTHORIZED", "Unable to identify user"));
        }

        var result = await _contentService.CreateContentPageAsync(createDto, userId);
        return result.Success ? CreatedAtAction(nameof(GetContentPageBySlug), 
            new { slug = createDto.Slug }, result) : BadRequest(result);
    }

    /// <summary>
    /// Update content page (Admin only)
    /// </summary>
    [HttpPut("pages/{id}")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<ContentPageDto>>> UpdateContentPage(
        Guid id, 
        [FromBody] UpdateContentPageDto updateDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<ContentPageDto>.ErrorResponse(
                "INVALID_PAGE_ID", "Page ID is required"));
        }

        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ContentPageDto>.ErrorResponse(
                "UNAUTHORIZED", "Unable to identify user"));
        }

        var result = await _contentService.UpdateContentPageAsync(id, updateDto, userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Delete content page (Admin only)
    /// </summary>
    [HttpDelete("pages/{id}")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteContentPage(Guid id)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_PAGE_ID", "Page ID is required"));
        }

        var result = await _contentService.DeleteContentPageAsync(id);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    #endregion

    #region FAQs

    /// <summary>
    /// Get FAQs with search and filtering (public endpoint)
    /// </summary>
    [HttpGet("faqs")]
    public async Task<ActionResult<ApiResponse<PaginatedResult<FaqDto>>>> GetFaqs(
        [FromQuery] string? search = null,
        [FromQuery] string? category = null,
        [FromQuery] bool? isPublished = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        if (limit > 100) limit = 100; // Prevent excessive data retrieval

        var result = await _contentService.GetFaqsAsync(search, category, isPublished, page, limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get FAQ categories (public endpoint)
    /// </summary>
    [HttpGet("faqs/categories")]
    public async Task<ActionResult<ApiResponse<List<FaqCategoryDto>>>> GetFaqCategories()
    {
        var result = await _contentService.GetFaqCategoriesAsync();
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get FAQ by ID (public endpoint)
    /// </summary>
    [HttpGet("faqs/{id}")]
    public async Task<ActionResult<ApiResponse<FaqDto>>> GetFaqById(Guid id)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<FaqDto>.ErrorResponse(
                "INVALID_FAQ_ID", "FAQ ID is required"));
        }

        var result = await _contentService.GetFaqByIdAsync(id);
        return result.Success ? Ok(result) : NotFound(result);
    }

    /// <summary>
    /// Create new FAQ (Admin only)
    /// </summary>
    [HttpPost("faqs")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<FaqDto>>> CreateFaq([FromBody] CreateFaqDto createDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<FaqDto>.ErrorResponse(
                "UNAUTHORIZED", "Unable to identify user"));
        }

        var result = await _contentService.CreateFaqAsync(createDto, userId);
        return result.Success ? CreatedAtAction(nameof(GetFaqById), 
            new { id = result.Data?.Id }, result) : BadRequest(result);
    }

    /// <summary>
    /// Update FAQ (Admin only)
    /// </summary>
    [HttpPut("faqs/{id}")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<FaqDto>>> UpdateFaq(
        Guid id, 
        [FromBody] UpdateFaqDto updateDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<FaqDto>.ErrorResponse(
                "INVALID_FAQ_ID", "FAQ ID is required"));
        }

        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<FaqDto>.ErrorResponse(
                "UNAUTHORIZED", "Unable to identify user"));
        }

        var result = await _contentService.UpdateFaqAsync(id, updateDto, userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Delete FAQ (Admin only)
    /// </summary>
    [HttpDelete("faqs/{id}")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteFaq(Guid id)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_FAQ_ID", "FAQ ID is required"));
        }

        var result = await _contentService.DeleteFaqAsync(id);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Mark FAQ as helpful/not helpful (public endpoint)
    /// </summary>
    [HttpPost("faqs/{id}/helpful")]
    public async Task<ActionResult<ApiResponse<bool>>> MarkFaqAsHelpful(
        Guid id, 
        [FromBody] bool isHelpful)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_FAQ_ID", "FAQ ID is required"));
        }

        var userId = GetCurrentUserId();
        var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();

        var result = await _contentService.MarkFaqAsHelpfulAsync(id, isHelpful, 
            userId != Guid.Empty ? userId : null, ipAddress);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    #endregion

    #region Contact Submissions

    /// <summary>
    /// Get contact submissions (Admin only)
    /// </summary>
    [HttpGet("contact-submissions")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<PaginatedResult<ContactSubmissionDto>>>> GetContactSubmissions(
        [FromQuery] string? search = null,
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] string? category = null,
        [FromQuery] DateTime? createdFrom = null,
        [FromQuery] DateTime? createdTo = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        if (limit > 100) limit = 100; // Prevent excessive data retrieval

        var result = await _contentService.GetContactSubmissionsAsync(search, status, priority, 
            category, createdFrom, createdTo, page, limit);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Create contact submission (public endpoint)
    /// </summary>
    [HttpPost("contact-submissions")]
    public async Task<ActionResult<ApiResponse<ContactSubmissionDto>>> CreateContactSubmission(
        [FromBody] CreateContactSubmissionDto createDto)
    {
        var result = await _contentService.CreateContactSubmissionAsync(createDto);
        return result.Success ? CreatedAtAction(nameof(GetContactSubmissions), 
            new { }, result) : BadRequest(result);
    }

    /// <summary>
    /// Update contact submission (Admin only)
    /// </summary>
    [HttpPut("contact-submissions/{id}")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<ContactSubmissionDto>>> UpdateContactSubmission(
        Guid id, 
        [FromBody] UpdateContactSubmissionDto updateDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<ContactSubmissionDto>.ErrorResponse(
                "INVALID_SUBMISSION_ID", "Contact submission ID is required"));
        }

        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ContactSubmissionDto>.ErrorResponse(
                "UNAUTHORIZED", "Unable to identify user"));
        }

        var result = await _contentService.UpdateContactSubmissionAsync(id, updateDto, userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    #endregion

    #region Statistics

    /// <summary>
    /// Get content statistics (Admin only)
    /// </summary>
    [HttpGet("stats")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<ApiResponse<ContentStatsDto>>> GetContentStats()
    {
        var result = await _contentService.GetContentStatsAsync();
        return result.Success ? Ok(result) : BadRequest(result);
    }

    #endregion
}
