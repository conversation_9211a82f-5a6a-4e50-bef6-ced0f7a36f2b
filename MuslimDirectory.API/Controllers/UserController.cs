using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.User;
using MuslimDirectory.API.Models.Common;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/user")]
[Authorize]
public class UserController(IUserService userService) : ControllerBase
{
    /// <summary>
    /// Get current user's profile
    /// </summary>
    /// <returns>User profile with stats</returns>
    [HttpGet("profile")]
    public async Task<ActionResult<ApiResponse<UserProfileWrapperResponse>>> GetProfile()
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<UserProfileWrapperResponse>.ErrorResponse("UNAUTHORIZED", "User not authenticated"));
        }

        var result = await userService.GetUserProfileAsync(userId);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Update user profile
    /// </summary>
    /// <param name="request">Profile update details</param>
    /// <returns>Success message</returns>
    [HttpPut("profile")]
    public async Task<ActionResult<ApiResponse<UserProfileWrapperResponse>>> UpdateProfile([FromBody] UpdateUserProfileRequest request)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<UserProfileWrapperResponse>.ErrorResponse("UNAUTHORIZED", "User not authenticated"));
        }

        var result = await userService.UpdateUserProfileAsync(userId, request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Change user password
    /// </summary>
    /// <param name="request">Password change details</param>
    /// <returns>Success message</returns>
    [HttpPut("change-password")]
    public async Task<ActionResult<ApiResponse<string>>> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<string>.ErrorResponse("UNAUTHORIZED", "User not authenticated"));
        }

        var result = await userService.ChangePasswordAsync(userId, request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get user preferences
    /// </summary>
    /// <returns>User preferences</returns>
    [HttpGet("preferences")]
    public async Task<ActionResult<ApiResponse<UserPreferencesResponse>>> GetPreferences()
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<UserPreferencesResponse>.ErrorResponse("UNAUTHORIZED", "User not authenticated"));
        }

        var result = await userService.GetUserPreferencesAsync(userId);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Update user preferences
    /// </summary>
    /// <param name="request">Preferences update details</param>
    /// <returns>Success message</returns>
    [HttpPut("preferences")]
    public async Task<ActionResult<ApiResponse<string>>> UpdatePreferences([FromBody] UpdateUserPreferencesRequest request)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<string>.ErrorResponse("UNAUTHORIZED", "User not authenticated"));
        }

        var result = await userService.UpdateUserPreferencesAsync(userId, request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get current user ID from JWT token
    /// </summary>
    /// <returns>User ID or Guid.Empty if not found</returns>
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}
