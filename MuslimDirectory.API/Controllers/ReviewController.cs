using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Review;
using MuslimDirectory.API.Models.Common;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/review")]
public class ReviewController(IReviewService reviewService) : ControllerBase
{
    private readonly IReviewService _reviewService = reviewService;

    private Guid GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    /// <summary>
    /// Get reviews for a listing
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PaginatedResult<ReviewDto>>>> GetReviews(
        [FromQuery] Guid listingId,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20,
        [FromQuery] string? sortBy = null,
        [FromQuery] int? rating = null,
        [FromQuery] bool? hasResponse = null)
    {
        if (listingId == Guid.Empty)
        {
            return BadRequest(ApiResponse<PaginatedResult<ReviewDto>>.ErrorResponse(
                "INVALID_LISTING_ID", "Listing ID is required"));
        }

        if (page < 1) page = 1;
        if (limit < 1 || limit > 100) limit = 20;

        var result = await _reviewService.GetReviewsAsync(listingId, page, limit, sortBy, rating, hasResponse);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Submit a review for a listing
    /// </summary>
    [HttpPost]
    [Authorize]
    public async Task<ActionResult<ApiResponse<ReviewCreateResponseDto>>> CreateReview(
        [FromBody] CreateReviewDto createDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<ReviewCreateResponseDto>.ErrorResponse(
                "VALIDATION_FAILED", "Validation failed", ModelState));
        }

        var userId = GetUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ReviewCreateResponseDto>.ErrorResponse(
                "UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _reviewService.CreateReviewAsync(createDto, userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Update a review
    /// </summary>
    [HttpPut("{id}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<ReviewDto>>> UpdateReview(
        Guid id, [FromBody] UpdateReviewDto updateDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<ReviewDto>.ErrorResponse(
                "INVALID_REVIEW_ID", "Review ID is required"));
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<ReviewDto>.ErrorResponse(
                "VALIDATION_FAILED", "Validation failed", ModelState));
        }

        var userId = GetUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ReviewDto>.ErrorResponse(
                "UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _reviewService.UpdateReviewAsync(id, updateDto, userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Delete a review
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteReview(Guid id)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_REVIEW_ID", "Review ID is required"));
        }

        var userId = GetUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse(
                "UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _reviewService.DeleteReviewAsync(id, userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Mark a review as helpful or not helpful
    /// </summary>
    [HttpPost("{id}/helpful")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<ReviewHelpfulResponseDto>>> MarkReviewHelpful(
        Guid id, [FromBody] ReviewHelpfulDto helpfulDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<ReviewHelpfulResponseDto>.ErrorResponse(
                "INVALID_REVIEW_ID", "Review ID is required"));
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<ReviewHelpfulResponseDto>.ErrorResponse(
                "VALIDATION_FAILED", "Validation failed", ModelState));
        }

        var userId = GetUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ReviewHelpfulResponseDto>.ErrorResponse(
                "UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _reviewService.MarkReviewHelpfulAsync(id, helpfulDto, userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Report a review
    /// </summary>
    [HttpPost("{id}/report")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> ReportReview(
        Guid id, [FromBody] ReportReviewDto reportDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_REVIEW_ID", "Review ID is required"));
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "VALIDATION_FAILED", "Validation failed", ModelState));
        }

        var userId = GetUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResponse(
                "UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _reviewService.ReportReviewAsync(id, reportDto, userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Respond to a review (organization members only)
    /// </summary>
    [HttpPost("{id}/response")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<ReviewResponseDto>>> RespondToReview(
        Guid id, [FromBody] ReviewResponseCreateDto responseDto)
    {
        if (id == Guid.Empty)
        {
            return BadRequest(ApiResponse<ReviewResponseDto>.ErrorResponse(
                "INVALID_REVIEW_ID", "Review ID is required"));
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<ReviewResponseDto>.ErrorResponse(
                "VALIDATION_FAILED", "Validation failed", ModelState));
        }

        var userId = GetUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<ReviewResponseDto>.ErrorResponse(
                "UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _reviewService.RespondToReviewAsync(id, responseDto, userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get user's reviews
    /// </summary>
    [HttpGet("user")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<PaginatedResult<ReviewDto>>>> GetUserReviews(
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20,
        [FromQuery] string? status = null)
    {
        if (page < 1) page = 1;
        if (limit < 1 || limit > 100) limit = 20;

        var userId = GetUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(ApiResponse<PaginatedResult<ReviewDto>>.ErrorResponse(
                "UNAUTHORIZED", "User not authenticated"));
        }

        var result = await _reviewService.GetUserReviewsAsync(userId, page, limit, status);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get review summary for a listing
    /// </summary>
    [HttpGet("summary")]
    public async Task<ActionResult<ApiResponse<ReviewSummaryDto>>> GetReviewSummary(
        [FromQuery] Guid listingId)
    {
        if (listingId == Guid.Empty)
        {
            return BadRequest(ApiResponse<ReviewSummaryDto>.ErrorResponse(
                "INVALID_LISTING_ID", "Listing ID is required"));
        }

        var result = await _reviewService.GetReviewSummaryAsync(listingId);
        return result.Success ? Ok(result) : BadRequest(result);
    }
}
