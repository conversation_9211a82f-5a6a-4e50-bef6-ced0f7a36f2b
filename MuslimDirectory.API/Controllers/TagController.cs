using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Models.DTOs.Tag;
using MuslimDirectory.API.Services.Interfaces;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/tag")]
public class TagController : ControllerBase
{
    private readonly ITagService _tagService;

    public TagController(ITagService tagService)
    {
        _tagService = tagService;
    }

    /// <summary>
    /// Get all tags with pagination and filtering
    /// </summary>
    [HttpGet]
    [AllowAnonymous]
    public async Task<IActionResult> GetTags(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? search = null,
        [FromQuery] bool includeInactive = false,
        [FromQuery] string? sortBy = null,
        [FromQuery] string? sortOrder = null)
    {
        if (page < 1) page = 1;
        if (pageSize < 1 || pageSize > 100) pageSize = 10;

        var result = await _tagService.GetTagsAsync(page, pageSize, search, includeInactive, sortBy, sortOrder);
        
        if (result.Success)
            return Ok(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get tag by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetTagById(Guid id)
    {
        var result = await _tagService.GetTagByIdAsync(id);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Error?.Code == "TAG_NOT_FOUND")
            return NotFound(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get tag with associated listings
    /// </summary>
    [HttpGet("{id:guid}/listings")]
    [AllowAnonymous]
    public async Task<IActionResult> GetTagWithListings(
        Guid id,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? status = null,
        [FromQuery] string? complianceStatus = null)
    {
        if (page < 1) page = 1;
        if (pageSize < 1 || pageSize > 100) pageSize = 10;

        var result = await _tagService.GetTagWithListingsAsync(id, page, pageSize, status, complianceStatus);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Error?.Code == "TAG_NOT_FOUND")
            return NotFound(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Create a new tag (Admin only)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<IActionResult> CreateTag([FromBody] CreateTagDto createDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _tagService.CreateTagAsync(createDto, userId);
        
        if (result.Success)
            return CreatedAtAction(nameof(GetTagById), new { id = result.Data!.Id }, result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Update tag (Admin only)
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<IActionResult> UpdateTag(Guid id, [FromBody] UpdateTagDto updateDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _tagService.UpdateTagAsync(id, updateDto, userId);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Error?.Code == "TAG_NOT_FOUND")
            return NotFound(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Delete tag (Admin only)
    /// </summary>
    [HttpDelete("{id:guid}")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<IActionResult> DeleteTag(Guid id)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _tagService.DeleteTagAsync(id, userId);
        
        if (result.Success)
            return NoContent();
        
        if (result.Error?.Code == "TAG_NOT_FOUND")
            return NotFound(result);
        
        if (result.Error?.Code == "HAS_LISTINGS")
            return BadRequest(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get popular tags
    /// </summary>
    [HttpGet("popular")]
    [AllowAnonymous]
    public async Task<IActionResult> GetPopularTags([FromQuery] int limit = 20)
    {
        if (limit < 1 || limit > 100) limit = 20;

        var result = await _tagService.GetPopularTagsAsync(limit);
        
        if (result.Success)
            return Ok(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Search tags
    /// </summary>
    [HttpGet("search")]
    [AllowAnonymous]
    public async Task<IActionResult> SearchTags(
        [FromQuery] string query,
        [FromQuery] int limit = 10)
    {
        if (string.IsNullOrWhiteSpace(query))
            return BadRequest("Query parameter is required");

        if (limit < 1 || limit > 50) limit = 10;

        var result = await _tagService.SearchTagsAsync(query, limit);
        
        if (result.Success)
            return Ok(result);
        
        return BadRequest(result);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}
