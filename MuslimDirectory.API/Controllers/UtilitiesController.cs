using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.Utilities;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/utilities")]
public class UtilitiesController(IFileBaseService fileBaseService) : ControllerBase
{
    private readonly IFileBaseService _fileBaseService = fileBaseService;

    /// <summary>
    /// Upload a file to FileBase bucket
    /// </summary>
    /// <param name="file">The file to upload</param>
    /// <param name="folder">Optional folder path</param>
    /// <param name="customFileName">Optional custom filename</param>
    /// <param name="makePublic">Whether to make the file publicly accessible (default: true)</param>
    /// <returns>File upload response with public URL</returns>
    [HttpPost("upload")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<FileUploadResponse>>> UploadFile(
        [FromForm] IFormFile file,
        [FromForm] string? folder = null,
        [FromForm] string? customFileName = null,
        [FromForm] bool makePublic = true)
    {
        if (file == null)
        {
            return BadRequest(ApiResponse<FileUploadResponse>.ErrorResponse(
                "FILE_REQUIRED", "File is required"));
        }

        var result = await _fileBaseService.UploadFileAsync(file, folder, customFileName, makePublic);
        
        if (result.Success)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// Upload multiple files to FileBase bucket
    /// </summary>
    /// <param name="files">The files to upload</param>
    /// <param name="folder">Optional folder path</param>
    /// <param name="makePublic">Whether to make the files publicly accessible (default: true)</param>
    /// <returns>List of file upload responses with public URLs</returns>
    [HttpPost("upload-multiple")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<List<FileUploadResponse>>>> UploadMultipleFiles(
        [FromForm] List<IFormFile> files,
        [FromForm] string? folder = null,
        [FromForm] bool makePublic = true)
    {
        if (files == null || files.Count == 0)
        {
            return BadRequest(ApiResponse<List<FileUploadResponse>>.ErrorResponse(
                "FILES_REQUIRED", "At least one file is required"));
        }

        if (files.Count > 10)
        {
            return BadRequest(ApiResponse<List<FileUploadResponse>>.ErrorResponse(
                "TOO_MANY_FILES", "Maximum 10 files allowed per upload"));
        }

        var uploadResults = new List<FileUploadResponse>();
        var errors = new List<string>();

        foreach (var file in files)
        {
            var result = await _fileBaseService.UploadFileAsync(file, folder, null, makePublic);
            
            if (result.Success && result.Data != null)
            {
                uploadResults.Add(result.Data);
            }
            else
            {
                errors.Add($"Failed to upload {file.FileName}: {result.Error?.Message}");
            }
        }

        if (uploadResults.Count == 0)
        {
            return BadRequest(ApiResponse<List<FileUploadResponse>>.ErrorResponse(
                "ALL_UPLOADS_FAILED", "All file uploads failed", string.Join("; ", errors)));
        }

        var response = ApiResponse<List<FileUploadResponse>>.SuccessResponse(uploadResults);
        
        if (errors.Count > 0)
        {
            response.Message = $"Uploaded {uploadResults.Count} files successfully. Errors: {string.Join("; ", errors)}";
        }
        else
        {
            response.Message = $"All {uploadResults.Count} files uploaded successfully";
        }

        return Ok(response);
    }

    /// <summary>
    /// Delete a file from FileBase bucket
    /// </summary>
    /// <param name="request">Delete request containing the file key</param>
    /// <returns>Delete operation result</returns>
    [HttpDelete("delete")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<FileDeleteResponse>>> DeleteFile([FromBody] FileDeleteRequest request)
    {
        var result = await _fileBaseService.DeleteFileAsync(request.Key);
        
        if (result.Success)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get the public URL for a file
    /// </summary>
    /// <param name="key">The file key/path in the bucket</param>
    /// <returns>Public URL of the file</returns>
    [HttpGet("url/{*key}")]
    public async Task<ActionResult<ApiResponse<string>>> GetFileUrl(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
        {
            return BadRequest(ApiResponse<string>.ErrorResponse(
                "INVALID_KEY", "File key is required"));
        }

        var result = await _fileBaseService.GetFileUrlAsync(key);
        
        if (result.Success)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// Check if a file exists in the bucket
    /// </summary>
    /// <param name="key">The file key/path in the bucket</param>
    /// <returns>True if file exists, false otherwise</returns>
    [HttpGet("exists/{*key}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> FileExists(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
        {
            return BadRequest(ApiResponse<bool>.ErrorResponse(
                "INVALID_KEY", "File key is required"));
        }

        var result = await _fileBaseService.FileExistsAsync(key);
        
        if (result.Success)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get upload guidelines and limits
    /// </summary>
    /// <returns>Upload guidelines and file limits</returns>
    [HttpGet("upload-info")]
    public ActionResult<ApiResponse<object>> GetUploadInfo()
    {
        var uploadInfo = new
        {
            MaxFileSize = "50MB",
            MaxFilesPerUpload = 10,
            AllowedFileTypes = new[]
            {
                "Images: JPEG, JPG, PNG, GIF, WebP",
                "Documents: PDF, TXT, DOC, DOCX, XLS, XLSX"
            },
            SupportedFormats = new[]
            {
                "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
                "application/pdf", "text/plain", "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/vnd.ms-excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            },
            Guidelines = new[]
            {
                "Files are automatically renamed with unique identifiers",
                "Files are stored securely in FileBase S3-compatible storage",
                "Public files are accessible via direct URL",
                "Authentication required for upload and delete operations"
            }
        };

        return Ok(ApiResponse<object>.SuccessResponse(uploadInfo));
    }

    /// <summary>
    /// Test file upload with detailed debugging information
    /// </summary>
    /// <param name="file">The file to upload</param>
    /// <returns>Detailed upload information for debugging</returns>
    [HttpPost("test-upload")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<object>>> TestUpload([FromForm] IFormFile file)
    {
        if (file == null)
        {
            return BadRequest(ApiResponse<object>.ErrorResponse(
                "FILE_REQUIRED", "File is required"));
        }

        var debugInfo = new
        {
            FileName = file.FileName,
            ContentType = file.ContentType,
            FileSize = file.Length,
            Headers = file.Headers.ToDictionary(h => h.Key, h => h.Value.ToString())
        };

        var result = await _fileBaseService.UploadFileAsync(file, "test-uploads", null, true);

        var response = new
        {
            DebugInfo = debugInfo,
            UploadResult = result
        };

        return Ok(ApiResponse<object>.SuccessResponse(response));
    }
}
