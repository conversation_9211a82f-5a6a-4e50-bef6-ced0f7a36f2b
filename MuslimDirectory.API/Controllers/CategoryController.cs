using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Models.DTOs.Category;
using MuslimDirectory.API.Services.Interfaces;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/category")]
public class CategoryController : ControllerBase
{
    private readonly ICategoryService _categoryService;

    public CategoryController(ICategoryService categoryService)
    {
        _categoryService = categoryService;
    }

    /// <summary>
    /// Get all categories with optional subcategories
    /// </summary>
    [HttpGet]
    [AllowAnonymous]
    public async Task<IActionResult> GetCategories(
        [FromQuery] bool includeSubCategories = true,
        [FromQuery] bool includeInactive = false,
        [FromQuery] Guid? parentId = null)
    {
        var result = await _categoryService.GetCategoriesAsync(includeSubCategories, includeInactive, parentId);
        
        if (result.Success)
            return Ok(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get category by ID with listings
    /// </summary>
    [HttpGet("{id:guid}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetCategoryById(
        Guid id,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? status = null,
        [FromQuery] string? complianceStatus = null,
        [FromQuery] string? platformType = null,
        [FromQuery] string? pricingModel = null,
        [FromQuery] string? sortBy = null,
        [FromQuery] string? sortOrder = null)
    {
        if (page < 1) page = 1;
        if (pageSize < 1 || pageSize > 100) pageSize = 10;

        var result = await _categoryService.GetCategoryByIdAsync(
            id, page, pageSize, status, complianceStatus, platformType, pricingModel, sortBy, sortOrder);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Error?.Code == "CATEGORY_NOT_FOUND")
            return NotFound(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Create a new category (Admin only)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<IActionResult> CreateCategory([FromBody] CreateCategoryDto createDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _categoryService.CreateCategoryAsync(createDto, userId);
        
        if (result.Success)
            return CreatedAtAction(nameof(GetCategoryById), new { id = result.Data!.Id }, result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Update category (Admin only)
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<IActionResult> UpdateCategory(Guid id, [FromBody] UpdateCategoryDto updateDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _categoryService.UpdateCategoryAsync(id, updateDto, userId);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Error?.Code == "CATEGORY_NOT_FOUND")
            return NotFound(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Delete category (Admin only)
    /// </summary>
    [HttpDelete("{id:guid}")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<IActionResult> DeleteCategory(Guid id)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _categoryService.DeleteCategoryAsync(id, userId);
        
        if (result.Success)
            return NoContent();
        
        if (result.Error?.Code == "CATEGORY_NOT_FOUND")
            return NotFound(result);
        
        if (result.Error?.Code == "HAS_SUBCATEGORIES" || result.Error?.Code == "HAS_LISTINGS")
            return BadRequest(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get categories list with pagination (Admin only)
    /// </summary>
    [HttpGet("list")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<IActionResult> GetCategoriesList(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? search = null,
        [FromQuery] bool includeInactive = false)
    {
        if (page < 1) page = 1;
        if (pageSize < 1 || pageSize > 100) pageSize = 10;

        var result = await _categoryService.GetCategoriesListAsync(page, pageSize, search, includeInactive);
        
        if (result.Success)
            return Ok(result);
        
        return BadRequest(result);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}
