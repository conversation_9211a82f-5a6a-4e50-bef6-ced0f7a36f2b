using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Models.DTOs.Organization;
using MuslimDirectory.API.Services.Interfaces;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/organization")]
[Authorize]
public class OrganizationController : ControllerBase
{
    private readonly IOrganizationService _organizationService;

    public OrganizationController(IOrganizationService organizationService)
    {
        _organizationService = organizationService;
    }

    /// <summary>
    /// Get all organizations with optional filtering and pagination
    /// </summary>
    [HttpGet]
    [AllowAnonymous]
    public async Task<IActionResult> GetOrganizations(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? search = null,
        [FromQuery] string? country = null,
        [FromQuery] bool? isVerified = null)
    {
        if (page < 1) page = 1;
        if (pageSize < 1 || pageSize > 100) pageSize = 10;

        var result = await _organizationService.GetOrganizationsAsync(page, pageSize, search, country, isVerified);
        
        if (result.Success)
            return Ok(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Create a new organization
    /// </summary>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> CreateOrganization([FromBody] CreateOrganizationDto createDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _organizationService.CreateOrganizationAsync(createDto, userId);
        
        if (result.Success)
            return CreatedAtAction(nameof(GetOrganizationById), new { id = result.Data!.Id }, result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get organization by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetOrganizationById(Guid id)
    {
        var result = await _organizationService.GetOrganizationByIdAsync(id);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Error?.Code == "ORGANIZATION_NOT_FOUND")
            return NotFound(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Update organization
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize]
    public async Task<IActionResult> UpdateOrganization(Guid id, [FromBody] UpdateOrganizationDto updateDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _organizationService.UpdateOrganizationAsync(id, updateDto, userId);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Error?.Code == "ORGANIZATION_NOT_FOUND")
            return NotFound(result);
        
        if (result.Error?.Code == "ACCESS_DENIED" || result.Error?.Code == "INSUFFICIENT_PERMISSIONS")
            return Forbid();
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get organization members
    /// </summary>
    [HttpGet("{id:guid}/members")]
    public async Task<IActionResult> GetOrganizationMembers(Guid id)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _organizationService.GetOrganizationMembersAsync(id, userId);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Error?.Code == "ACCESS_DENIED" || result.Error?.Code == "INSUFFICIENT_PERMISSIONS")
            return Forbid();
        
        return BadRequest(result);
    }

    /// <summary>
    /// Add organization member
    /// </summary>
    [HttpPost("{id:guid}/members")]
    [Authorize]
    public async Task<IActionResult> AddOrganizationMember(Guid id, [FromBody] AddOrganizationMemberDto memberDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _organizationService.AddOrganizationMemberAsync(id, memberDto, userId);
        
        if (result.Success)
            return CreatedAtAction(nameof(GetOrganizationMembers), new { id }, result);
        
        if (result.Error?.Code == "ACCESS_DENIED" || result.Error?.Code == "INSUFFICIENT_PERMISSIONS")
            return Forbid();
        
        if (result.Error?.Code == "USER_NOT_FOUND")
            return NotFound(result);
        
        if (result.Error?.Code == "USER_ALREADY_MEMBER")
            return Conflict(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get current user's organizations
    /// </summary>
    [HttpGet("my-organizations")]
    public async Task<IActionResult> GetMyOrganizations()
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _organizationService.GetMyOrganizationsAsync(userId);
        
        if (result.Success)
            return Ok(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Remove organization member
    /// </summary>
    [HttpDelete("{organizationId:guid}/members/{memberId:guid}")]
    [Authorize]
    public async Task<IActionResult> RemoveOrganizationMember(Guid organizationId, Guid memberId)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _organizationService.RemoveOrganizationMemberAsync(organizationId, memberId, userId);
        
        if (result.Success)
            return NoContent();
        
        if (result.Error?.Code == "ACCESS_DENIED" || result.Error?.Code == "INSUFFICIENT_PERMISSIONS")
            return Forbid();
        
        if (result.Error?.Code == "MEMBER_NOT_FOUND")
            return NotFound(result);
        
        if (result.Error?.Code == "CANNOT_REMOVE_OWNER")
            return BadRequest(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Update member role
    /// </summary>
    [HttpPut("{organizationId:guid}/members/{memberId:guid}/role")]
    [Authorize]
    public async Task<IActionResult> UpdateMemberRole(Guid organizationId, Guid memberId, [FromBody] UpdateMemberRoleDto roleDto)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
            return Unauthorized();

        var result = await _organizationService.UpdateMemberRoleAsync(organizationId, memberId, roleDto.Role, userId);
        
        if (result.Success)
            return NoContent();
        
        if (result.Error?.Code == "ACCESS_DENIED" || result.Error?.Code == "INSUFFICIENT_PERMISSIONS")
            return Forbid();
        
        if (result.Error?.Code == "MEMBER_NOT_FOUND")
            return NotFound(result);
        
        if (result.Error?.Code == "CANNOT_CHANGE_OWNER_ROLE")
            return BadRequest(result);
        
        return BadRequest(result);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}

public class UpdateMemberRoleDto
{
    public string Role { get; set; } = string.Empty;
}
