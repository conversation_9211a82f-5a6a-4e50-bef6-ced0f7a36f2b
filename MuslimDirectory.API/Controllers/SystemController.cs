using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Services.Interfaces;
using MuslimDirectory.API.Models.DTOs.System;
using MuslimDirectory.API.Models.Common;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("api/v1.0/system")]
public class SystemController(ISystemService systemService) : ControllerBase
{
    private readonly ISystemService _systemService = systemService;

    /// <summary>
    /// Get system health status
    /// </summary>
    [HttpGet("health")]
    public async Task<ActionResult<ApiResponse<SystemHealthDto>>> GetSystemHealth()
    {
        var result = await _systemService.GetSystemHealthAsync();
        
        if (!result.Success)
            return StatusCode(503, result); // Service Unavailable for health check failures
        
        // Return appropriate status code based on health
        return result.Data?.Status switch
        {
            "Healthy" => Ok(result),
            "Degraded" => StatusCode(200, result), // Still OK but with warnings
            "Unhealthy" => StatusCode(503, result), // Service Unavailable
            _ => Ok(result)
        };
    }

    /// <summary>
    /// Get public system settings
    /// </summary>
    [HttpGet("settings")]
    public async Task<ActionResult<ApiResponse<SystemSettingsDto>>> GetSystemSettings()
    {
        var result = await _systemService.GetSystemSettingsAsync();
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get system version information
    /// </summary>
    [HttpGet("version")]
    public async Task<ActionResult<ApiResponse<SystemVersionDto>>> GetSystemVersion()
    {
        var result = await _systemService.GetSystemVersionAsync();
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get list of supported countries
    /// </summary>
    [HttpGet("countries")]
    public async Task<ActionResult<ApiResponse<List<CountryDto>>>> GetCountries(
        [FromQuery] bool includeStates = false,
        [FromQuery] bool includeCities = false)
    {
        var result = await _systemService.GetCountriesAsync(includeStates, includeCities);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get country details by country code
    /// </summary>
    [HttpGet("countries/{countryCode}")]
    public async Task<ActionResult<ApiResponse<CountryDto>>> GetCountryByCode(
        string countryCode,
        [FromQuery] bool includeStates = false,
        [FromQuery] bool includeCities = false)
    {
        if (string.IsNullOrWhiteSpace(countryCode) || countryCode.Length != 2)
        {
            return BadRequest(ApiResponse<CountryDto>.ErrorResponse(
                "INVALID_COUNTRY_CODE", "Country code must be a valid 2-letter ISO code"));
        }

        var result = await _systemService.GetCountryByCodeAsync(countryCode.ToUpper(), includeStates, includeCities);
        return result.Success ? Ok(result) : NotFound(result);
    }

    /// <summary>
    /// Get states/provinces by country code
    /// </summary>
    [HttpGet("countries/{countryCode}/states")]
    public async Task<ActionResult<ApiResponse<List<StateProvinceDto>>>> GetStatesByCountry(string countryCode)
    {
        if (string.IsNullOrWhiteSpace(countryCode) || countryCode.Length != 2)
        {
            return BadRequest(ApiResponse<List<StateProvinceDto>>.ErrorResponse(
                "INVALID_COUNTRY_CODE", "Country code must be a valid 2-letter ISO code"));
        }

        var result = await _systemService.GetStatesByCountryAsync(countryCode.ToUpper());
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get major cities by country code
    /// </summary>
    [HttpGet("countries/{countryCode}/cities")]
    public async Task<ActionResult<ApiResponse<List<CityDto>>>> GetMajorCitiesByCountry(string countryCode)
    {
        if (string.IsNullOrWhiteSpace(countryCode) || countryCode.Length != 2)
        {
            return BadRequest(ApiResponse<List<CityDto>>.ErrorResponse(
                "INVALID_COUNTRY_CODE", "Country code must be a valid 2-letter ISO code"));
        }

        var result = await _systemService.GetMajorCitiesByCountryAsync(countryCode.ToUpper());
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get cities by state ID
    /// </summary>
    [HttpGet("states/{stateId}/cities")]
    public async Task<ActionResult<ApiResponse<List<CityDto>>>> GetCitiesByState(Guid stateId)
    {
        if (stateId == Guid.Empty)
        {
            return BadRequest(ApiResponse<List<CityDto>>.ErrorResponse(
                "INVALID_STATE_ID", "State ID is required"));
        }

        var result = await _systemService.GetCitiesByStateAsync(stateId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    /// <summary>
    /// Get only supported countries (shortcut endpoint)
    /// </summary>
    [HttpGet("supported-countries")]
    public async Task<ActionResult<ApiResponse<List<CountryDto>>>> GetSupportedCountries()
    {
        var result = await _systemService.GetSupportedCountriesAsync();
        return result.Success ? Ok(result) : BadRequest(result);
    }
}
